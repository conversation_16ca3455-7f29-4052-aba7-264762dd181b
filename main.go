package main

import (
	esd_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/mock"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/router"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/auth"
	"context"
	"database/sql"
	"encoding/gob"
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

func main() {

	// Logger
	utils.InitLogger(true)
	utils.Log.Info("App starting up")

	err := godotenv.Load(".env")
	if err != nil {
		panic("unable to load .env file!")
	}

	databaseURL := os.Getenv("DATABASE_URL")
	etimsAPIBaseURL := os.Getenv("ETIMS_API_BASE_URL_SANDBOX")

	if os.Getenv("ENVIRONMENT") == "production" {
		databaseURL = os.Getenv("DATABASE_URL_PROD")
		etimsAPIBaseURL = os.Getenv("ETIMS_API_BASE_URL_PRODUCTION")
	}

	transactionsDB := esd_db.InitTransactionsDBWithURL(databaseURL)
	defer transactionsDB.Close()

	txnsDB, err := initiateDatabaseConnection(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to connect to ESD database, err=[%v]", err))
	}

	// Providers
	var mpesa providers.MPesa
	var smtpSender providers.SmtpSender

	assetStore := providers.NewAssetStore(
		os.Getenv("AWS_BUCKET"),
		os.Getenv("AWS_REGION"),
		os.Getenv("AWS_ACCESS_KEY_ID"),
		os.Getenv("AWS_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)

	uploadAssetStore := providers.NewAssetStore(
		os.Getenv("AWS_BUCKET"),
		os.Getenv("AWS_REGION"),
		os.Getenv("AWS_ASSET_UPLOADER_ACCESS_KEY_ID"),
		os.Getenv("AWS_ASSET_UPLOADER_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)

	blurHash := providers.NewBlurHasher()
	imaging := providers.NewImaging()
	assetManager := providers.NewAssetManager(assetStore, uploadAssetStore, blurHash, imaging)

	utils.Log.Infof("assetManager=[%v]", assetManager)

	etimsVSCUProvider := providers.NewEtimsVSCU(etimsAPIBaseURL)
	mpesaPhoneNumberHashDecoder := providers.NewMPesaPhoneNumberHashDecoder()

	if os.Getenv("ENVIRONMENT") == "production" {
		mpesa = providers.NewMPesa()
		smtpSender = providers.NewSmtpSender()
	} else {
		mpesa = mock.NewMPesa()
		smtpSender = providers.NewSmtpSender()
	}

	// Test MPESA STK Push
	// testMPesaSTKPush(mpesa)
	// os.Exit(0)

	// Repos
	activityRepository := repos.NewActivityRepository(txnsDB)
	apiKeyRepository := repos.NewAPIKeyRepository(txnsDB)
	applicationRepository := repos.NewApplicationRepository()
	categoryRepository := repos.NewCategoryRepository(txnsDB)
	companyRegistrationRepository := repos.NewCompanyRegistrationRepository(txnsDB)
	currencyRepository := repos.NewCurrencyRepository()
	customerIDTypeRepository := repos.NewCustomerIDTypeRepository(txnsDB)
	customerRepository := repos.NewCustomerRepository(txnsDB)
	etimsItemRepository := repos.NewEtimsItemRepository(txnsDB)
	etimsStockItemRepository := repos.NewEtimsStockItemRepository(txnsDB)
	etimsStockRepository := repos.NewEtimsStockRepository(txnsDB)
	etimsNoticeRepository := repos.NewEtimsNoticeRepository(txnsDB)
	excelFileUploadRepository := repos.NewExcelFileUploadRepository(txnsDB)
	expenseTypeRepository := repos.NewExpenseTypeRepository(txnsDB)
	hashRequestRepository := repos.NewHashRequestRepository(txnsDB)
	incomeTrackingRepository := repos.NewIncomeTrackingRepository(txnsDB)
	invoiceItemRepository := repos.NewInvoiceItemRepository(txnsDB)
	invoiceRepository := repos.NewInvoiceRepository(txnsDB)
	itemQuantityRepository := repos.NewItemQuantityRepository(txnsDB)
	itemRepository := repos.NewItemRepository(txnsDB)
	mpesaDepositRepository := repos.NewMPesaDepositRepository(txnsDB)
	organizationRepository := repos.NewOrganizationRepository(txnsDB)
	phoneNumberHashRepository := repos.NewPhoneNumberHashRepository(txnsDB)
	phoneNumberRepository := repos.NewPhoneNumberRepository()
	receivingRepository := repos.NewReceivingRepository(txnsDB)
	saleItemRepository := repos.NewSaleItemRepository(txnsDB)
	saleRepository := repos.NewSaleRepository(txnsDB)
	settingRepository := repos.NewSettingRepository(txnsDB)
	sessionRepository := repos.NewSessionRepository()
	storeRepository := repos.NewStoreRepository(txnsDB)
	supplierRepository := repos.NewSupplierRepository(txnsDB)
	taxCategoryRepository := repos.NewTaxCategoryRepository(txnsDB)
	taxTypeRepository := repos.NewTaxTypeRepository(txnsDB)
	withholdingTaxRateRepository := repos.NewWithholdingTaxRateRepository(txnsDB)
	transactionRepository := repos.NewTransactionRepository(txnsDB)
	userApplicationRepository := repos.NewUserApplicationRepository()
	userCreditRepository := repos.NewUserCreditRepository(txnsDB)
	userExpenseRepository := repos.NewUserExpenseRepository(txnsDB)
	userIncomeRepository := repos.NewUserIncomeRepository(txnsDB)
	userRepository := repos.NewUserRepository(txnsDB)
	zReportInvoiceRepository := repos.NewZReportInvoiceRepository(txnsDB)
	zReportRepository := repos.NewZReportRepository(txnsDB)

	// Services
	analyticsService := services.NewAnalyticsService(
		apiKeyRepository,
		categoryRepository,
		customerRepository,
		excelFileUploadRepository,
		itemRepository,
		invoiceRepository,
		organizationRepository,
		saleRepository,
		storeRepository,
		userCreditRepository,
		userRepository,
	)

	apiKeyService := services.NewAPIKeyService(
		apiKeyRepository,
		storeRepository,
		userRepository,
	)

	categoryService := services.NewCategoryService(
		categoryRepository,
		userRepository,
	)

	currencyService := services.NewCurrencyService(currencyRepository)

	customerService := services.NewCustomerService(customerRepository, userRepository)

	etimsItemService := services.NewEtimsItemService(
		etimsVSCUProvider,
		etimsItemRepository,
		storeRepository,
	)

	etimsStockService := services.NewEtimsStockService(
		etimsVSCUProvider,
		etimsStockRepository,
		etimsStockItemRepository,
		storeRepository,
	)

	etimsVSCUService := services.NewEtimsVSCUService(
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		etimsItemRepository,
		etimsNoticeRepository,
		invoiceItemRepository,
		invoiceRepository,
		os.Getenv("QRCODE_FOLDER"),
		storeRepository,
	)

	excelFileUploadService := services.NewExcelFileUploadService(
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		excelFileUploadRepository,
		invoiceRepository,
		storeRepository,
		userRepository,
	)

	expenseTypeService := services.NewExpenseTypeService(expenseTypeRepository)

	traVfdService := services.NewTRAVFDService(
		companyRegistrationRepository,
		customerIDTypeRepository,
		invoiceRepository,
		zReportRepository,
		zReportInvoiceRepository,
	)

	creditNoteService := services.NewCreditNoteService(
		etimsItemRepository,
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_PRODUCTION"),
		os.Getenv("ETIMS_VERIFICATION_BASE_URL_SANDBOX"),
		etimsVSCUProvider,
		invoiceItemRepository,
		invoiceRepository,
		storeRepository,
		traVfdService,
		userRepository,
	)

	incomeTrackingService := services.NewIncomeTrackingService(incomeTrackingRepository)

	invoiceService := services.NewInvoiceService(
		invoiceItemRepository,
		invoiceRepository,
		smtpSender,
		storeRepository,
		traVfdService,
		userRepository,
	)

	itemService := services.NewItemService(
		etimsVSCUProvider,
		etimsItemRepository,
		itemQuantityRepository,
		itemRepository,
		organizationRepository,
		storeRepository,
		userRepository,
	)

	paymentService := services.NewPaymentService(
		mpesa,
		mpesaDepositRepository,
		mpesaPhoneNumberHashDecoder,
		organizationRepository,
		phoneNumberRepository,
		smtpSender,
	)

	paymentTransactionsService := services.NewPaymentTransactionsService(mpesa)

	phoneNumberHasheservice := services.NewPhoneNumberHashService(
		hashRequestRepository,
		phoneNumberHashRepository,
		transactionRepository,
		userCreditRepository,
	)

	receivingService := services.NewReceivingService(
		itemQuantityRepository,
		itemRepository,
		organizationRepository,
		receivingRepository,
		userRepository,
	)

	saleService := services.NewSaleService(
		currencyRepository,
		itemRepository,
		organizationRepository,
		saleItemRepository,
		saleRepository,
		storeRepository,
		userRepository,
	)

	settingService := services.NewSettingService(settingRepository)

	sessionService := services.NewSessionService(
		applicationRepository,
		phoneNumberRepository,
		sessionRepository,
		userApplicationRepository,
		userRepository,
	)

	storeService := services.NewStoreService(
		organizationRepository,
		storeRepository,
		userRepository,
	)

	supplierService := services.NewSupplierService(supplierRepository, userRepository)

	taxCategoryService := services.NewTaxCategoryService(
		taxCategoryRepository,
		taxTypeRepository,
		userRepository,
	)

	taxTypeService := services.NewTaxTypeService(organizationRepository, taxTypeRepository, userRepository)

	withholdingTaxRateService := services.NewWithholdingTaxRateService(
		taxCategoryRepository,
		taxTypeRepository,
		userRepository,
		withholdingTaxRateRepository,
	)

	userCreditService := services.NewUserCreditService(userCreditRepository, userRepository)

	userExpenseService := services.NewUserExpenseService(
		expenseTypeRepository,
		userExpenseRepository,
	)

	userIncomeService := services.NewUserIncomeService(taxCategoryRepository, userIncomeRepository)

	userService := services.NewUserService(
		activityRepository,
		apiKeyRepository,
		applicationRepository,
		organizationRepository,
		phoneNumberRepository,
		sessionRepository,
		storeRepository,
		userApplicationRepository,
		userCreditRepository,
		userRepository,
	)

	sessionAuthenticator := auth.NewSessionAuthenticator(sessionRepository, userRepository)

	// Router
	ginRouter := router.SetupRouter(
		transactionsDB,
		sessionAuthenticator,
		activityRepository,
		apiKeyRepository,
		apiKeyService,
		creditNoteService,
		etimsItemService,
		etimsStockService,
		etimsVSCUService,
		excelFileUploadService,
		expenseTypeService,
		userRepository,
		analyticsService,
		categoryService,
		currencyService,
		customerService,
		incomeTrackingService,
		invoiceService,
		itemService,
		paymentService,
		paymentTransactionsService,
		phoneNumberHasheservice,
		receivingService,
		saleService,
		settingService,
		sessionService,
		storeRepository,
		storeService,
		supplierService,
		taxCategoryService,
		taxTypeService,
		withholdingTaxRateService,
		userCreditService,
		userExpenseService,
		userIncomeService,
		userService,
	)

	ctx := context.Background()
	userService.SetupAdmin(ctx, transactionsDB)

	// for gin context access later
	gob.Register(entities.User{})

	port := os.Getenv("PORT")

	// Start server
	utils.Log.Infof("Starting application on port=[%v]", port)
	ginRouter.Run(":" + port)
	utils.Log.Infof("Started application on port=[%v]", port)
}

func initiateDatabaseConnection(databaseURL string) (*sql.DB, error) {

	txnsDB, err := sql.Open("postgres", databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to connect to ESD database, err=[%v]", err))
	}

	// postgrse DB Connection - *sql.DB
	postgresESDDB, err := setupDatabase(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to connect to postgres database, err=[%v]", err))
	}

	// run db migrations
	storage := esd_db.NewStorage(postgresESDDB)
	err = storage.RunMigrations(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to run postgres db migrations, err=[%v]", err))
	}

	return txnsDB, nil
}

func setupDatabase(connString string) (*sql.DB, error) {
	// change "postgres" for whatever supported database you want to use
	db, err := sql.Open("postgres", connString)
	if err != nil {
		return nil, err
	}

	// ping the DB to ensure that it is connected
	err = db.Ping()
	if err != nil {
		return nil, err
	}

	return db, nil
}

func conductEtimsTests() {
	// Demo local dev device
	etimsVscuCore := providers.NewEtimsVSCUCore(
		"https://etims-api-sbx.kra.go.ke/etims-api",
		"00",
		"SN5433Q09",
		"P051922564N",
	)

	// etimsVscuCore = providers.NewEtimsVSCUCore(
	// 	"https://etims-api.kra.go.ke/etims-api",
	// 	"02",
	// 	"STAN-CVU-Y12-8879",
	// 	"P052012153M",
	// )
	// etimsVscuCore.SelectServerTime()
	etimsVscuCore.SelectNotices()
	etimsVscuCore.EchoTest("Testing 123..")
	// etimsVscuCore.SelectInfo()

	// Demo local dev device
	// etimsVscuCore = providers.NewEtimsVSCUCore(
	// 	"https://etims-api.kra.go.ke/etims-api",
	// 	"00",
	// 	"SOK241PRMA",
	// 	"P052133751T",
	// )
	// etimsVscuCore.SelectInfo()
}

func testMPesaSTKPush(mpesa providers.MPesa) {
	resp, err := mpesa.InitiateSTKPushRequest(&forms.MPesaSTKPushRequestForm{
		Amount:                 1.00,
		PhoneNumber:            "************",
		AccountReference:       "HS01002",
		TransactionDescription: "Hash account topup.",
		// CallBackURL:            "https://optimusapi.strutstechnology.com/api/payments/safaricom/result",
	})

	if err != nil {
		utils.Log.Errorf("failed to initiate stk push, err=[%v]", err)
	} else {
		utils.Log.Infof("stk push response=[%+v]", resp)
	}

	utils.Log.Infof("mpesa STK resp=[%v]", resp)
}
