#!/bin/bash

APP_NAME="compliance-and-risk-management-backend"

# Pull the latest changes from gitlab
git pull origin main

# Build application
make compile-prod

echo "Application ready for deployment."

# Move one directory up
# cd ..

# # Stop the current running application
# if pgrep -f $APP_NAME; then
#   pkill -f $APP_NAME
# fi

# # Load .env file
# if [ -f .env ]; then
#   export $(grep -v '^#' .env | xargs)
# fi

# # Start the new application
# nohup ./$APP_NAME &

# # Wait for the application to start
# while ! nc -z localhost 9015; do   
#   sleep 1
# done

# echo "Application has been deployed and started successfully."
