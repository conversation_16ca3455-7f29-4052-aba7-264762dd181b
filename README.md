# MPesa Hash Decoder

This backend application decodes M-Pesa hash values and gives you the valid respective phone number.

- Setup the postgres database and start the application by running `make server`, the database migrations
  will get run automatically, and the application will start on the verified port.

- Copy .env.sample to .env and replace the keys accordingly, setting the sensitive keys to secrets.

# Deploy

Deploying to production involves the following steps;

1. Pushing the latest changes;

   - This triggers a webhook on gitlab actions which calls deploy.sh on linode.
   - The deploy.sh script pulls the latest changes to the server.

2. Login to the server and run the below command to create an executable;

   make compile-prod

   - After the sh file is created, version it, stop the running application, and start the latest application.
