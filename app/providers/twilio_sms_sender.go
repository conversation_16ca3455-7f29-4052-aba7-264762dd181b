package providers

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"
	"net/http"
	"net/url"
	"os"
	"strings"
)

const twilioMessagesURL = "https://api.twilio.com/2010-04-01/Accounts/%s/Messages.json"

type (
	TwilioSmsSender interface {
		SendSms(phoneNumber entities.PhoneNumber, message string) error
	}

	twilioError struct {
		Code     int    `json:"code"`
		Message  string `json:"message"`
		MoreInfo string `json:"more_info"`
		Status   int    `json:"status"`
	}

	AppTwilioSmsSender struct {
		accountSID          string
		authToken           string
		messagingServiceSID string
	}
)

func NewTwilioSmsSender() TwilioSmsSender {
	return NewTwilioSmsSenderWithCredentials(
		os.Getenv("TWILIO_ACCOUNT_SID"),
		os.Get<PERSON>v("TWILIO_AUTH_TOKEN"),
		os.Getenv("TWILIO_MESSAGING_SERVICE_SID"),
	)
}

func NewTwilioSmsSenderWithCredentials(accountSID, authToken, messagingServiceSID string) TwilioSmsSender {
	return &AppTwilioSmsSender{
		accountSID:          accountSID,
		authToken:           authToken,
		messagingServiceSID: messagingServiceSID,
	}
}

func (s *AppTwilioSmsSender) SendSms(phoneNumber entities.PhoneNumber, message string) error {

	values := url.Values{}
	values.Set("To", phoneNumber.String())
	values.Set("MessagingServiceSid", s.messagingServiceSID)
	values.Set("Body", message)

	reader := *strings.NewReader(values.Encode())

	url := fmt.Sprintf(twilioMessagesURL, s.accountSID)
	req, err := http.NewRequest("POST", url, &reader)
	if err != nil {
		return utils.NewError(
			err,
			"[TwilioSmsSender] Failed to create a new post request to send SMS message=[%v]",
			values,
		)
	}

	req.SetBasicAuth(s.accountSID, s.authToken)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return utils.NewError(
			err,
			"[TwilioSmsSender] Failed to post request to send Twilio SMS message=[%v]",
			values,
		)
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return utils.NewError(
			err,
			"[TwilioSmsSender] Could not read response body of response with status code=[%v] when sending message=[%v] to phone=[%v]",
			res.StatusCode,
			message,
			phoneNumber,
		)
	}

	if res.StatusCode < 200 || res.StatusCode > 299 {

		var twilioErr twilioError
		err = json.Unmarshal(body, &twilioErr)
		if err != nil {
			return utils.NewError(
				err,
				"Could not unmarshal error response=[%v] while sending sms through twilio with message=[%v] to phone=[%v]",
				string(body),
				message,
				phoneNumber,
			)
		}

		return utils.NewError(
			errors.New("invalid status code"),
			"[TwilioSmsSender] Received response status code=[%v] and response body=[%v] when sending message=[%v] to phone=[%v]",
			res.StatusCode,
			string(body),
			message,
			phoneNumber,
		)
	}

	logger.Infof(
		"[TwilioSmsSender] Made Twilio API CALL to POST SMS message=[%v] and got following response status=[%v] and value=[%v]",
		values,
		res.StatusCode,
		string(body),
	)

	return nil
}
