package providers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"compliance-and-risk-management-backend/app/entities"
)

const API_LAYER_BASE_URL = "https://api.apilayer.com"

type (
	ExchangeRate interface {
		ConvertCurrency(from, to string) (*entities.ExchangeRate, error)
		GetLatestExchangeRates() (*entities.ExchangeRates, error)
	}

	AppExchangeRate struct {
		apiKey string
	}
)

func NewExchangeRate() ExchangeRate {
	return NewExchangeRateWithMinAmount(
		os.Getenv("EXCHANGE_RATE_API_KEY"),
	)
}

func NewExchangeRateWithMinAmount(
	apiKey string,
) ExchangeRate {
	return &AppExchangeRate{
		apiKey: apiKey,
	}
}

func (s *AppExchangeRate) ConvertCurrency(from, to string) (*entities.ExchangeRate, error) {

	exchangeRate := &entities.ExchangeRate{}

	client := http.Client{}
	url := fmt.Sprintf("%v/currency_data/convert?from=%v&to=%v&amount=1", API_LAYER_BASE_URL, from, to)
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return exchangeRate, err
	}

	request.Header.Add("apikey", s.apiKey)

	resp, err := client.Do(request)
	if err != nil {
		fmt.Println(err)
		return exchangeRate, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return exchangeRate, err
	}

	json.Unmarshal([]byte(string(body)), &exchangeRate)

	return exchangeRate, nil
}

func (s *AppExchangeRate) GetLatestExchangeRates() (*entities.ExchangeRates, error) {

	exchangeRates := &entities.ExchangeRates{}

	client := http.Client{}
	url := fmt.Sprintf("%v/latest?base=USD", API_LAYER_BASE_URL)
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Println(err)
		return exchangeRates, err
	}

	resp, err := client.Do(request)
	if err != nil {
		fmt.Println(err)
		return exchangeRates, err
	}

	json.NewDecoder(resp.Body).Decode(exchangeRates)
	return exchangeRates, nil
}
