package providers

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/utils"
)

const (
	accBalanceCommandID              = "AccountBalance"
	mpesaAuthTokenKey                = "mpesa_auth_token"
	reversalOrganizationIdentityType = "11"
	organizationIdentityType         = "4"
	stkPushTransactionType           = "CustomerPayBillOnline"
	transactionStatusQueryCommandID  = "TransactionStatusQuery"
	transactionReversalCommandID     = "TransactionReversal"
	HASH_DECODER_CALLBACK_URL        = "https://optimusapi.strutstechnology.com/api/payments/safaricom/confirmation"
)

type (
	MPesa interface {
		AccountBalance() (*entities.MPesaAccountBalanceResponse, error)
		AuthenticateMPesa() (*entities.MPesaAuthentication, error)
		InitiateSTKPushRequest(form *forms.MPesaSTKPushRequestForm) (*entities.MPesaSTKPushResponse, error)
		TransactionStatusQuery(trxID string) (*entities.MPesaTransactionStatusResponse, error)
		RegisterURL(form *forms.RegisterURLForm) error
		Reversal(mpesaTrxID string, amount float64) (*entities.MPesaReversalResponse, error)
	}

	AppMPesa struct {
		apiBaseURL          string
		basicAuth           string
		mpesaAPIURL         string
		mpesaConsumerKey    string
		mpesaConsumerSecret string
		mpesaPassKey        string
		mpesaShortCode      string
	}
)

func NewMPesa() MPesa {

	return NewMPesaWithCrerdentials(
		os.Getenv("API_BASE_URL"),
		os.Getenv("MPESA_API_URL"),
		os.Getenv("MPESA_CONSUMER_KEY"),
		os.Getenv("MPESA_CONSUMER_SECRET"),
		os.Getenv("MPESA_PASSKEY"),
		os.Getenv("MPESA_SHORTCODE"),
	)
}

func NewMPesaWithCrerdentials(
	apiBaseURL string,
	mpesaAPIURL string,
	mpesaConsumerKey string,
	mpesaConsumerSecret string,
	mpesaPassKey string,
	mpesaShortCode string,
) MPesa {

	auth := mpesaConsumerKey + ":" + mpesaConsumerSecret
	basicAuth := base64.StdEncoding.EncodeToString([]byte(auth))

	return &AppMPesa{
		apiBaseURL:          apiBaseURL,
		basicAuth:           basicAuth,
		mpesaAPIURL:         mpesaAPIURL,
		mpesaConsumerKey:    mpesaConsumerKey,
		mpesaConsumerSecret: mpesaConsumerSecret,
		mpesaPassKey:        mpesaPassKey,
		mpesaShortCode:      mpesaShortCode,
	}
}

func (s *AppMPesa) AccountBalance() (*entities.MPesaAccountBalanceResponse, error) {

	accBalReponse := &entities.MPesaAccountBalanceResponse{}

	mpesaShortCode := os.Getenv("MPESA_SHORTCODE")

	utils.Log.Infof("requesting balance for M-Pesa shortcode=[%v]\n", mpesaShortCode)

	mpesaToken, err := s.getMpesaAuthenticationToken()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return accBalReponse, err
	}

	url := fmt.Sprintf("%v/mpesa/accountbalance/v1/query", s.mpesaAPIURL)
	method := "POST"

	t := time.Now()
	currentTimeStamp := t.Format("**************")

	// base64.encode(Shortcode+Passkey+Timestamp)
	pwdStr := fmt.Sprintf("%v%v%v", s.mpesaShortCode, s.mpesaPassKey, currentTimeStamp)
	password := base64.StdEncoding.EncodeToString([]byte(pwdStr))

	reversalForm := forms.MPesaBalanceForm{
		CommandID:          accBalanceCommandID,
		IdentifierType:     organizationIdentityType,
		Initiator:          s.mpesaConsumerKey,
		PartyA:             mpesaShortCode,
		QueueTimeOutURL:    s.apiBaseURL + "/payments/safaricom/timeout",
		SecurityCredential: password,
		Remarks:            "Account Balance",
		ResultURL:          s.apiBaseURL + "/payments/safaricom/acc_bal",
	}

	reversalPayload, err := json.Marshal(reversalForm)
	if err != nil {
		log.Printf("failed to marshal mpesa acc bal form payload, err=[%v]\n", err)
	}

	payload := strings.NewReader(string(reversalPayload))

	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Printf("failed to create new accBal request, err=[%v]\n", err)
		return accBalReponse, err
	}

	req.Header.Add("Authorization", "Bearer "+mpesaToken)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Printf("failed to send accBal request, err=[%v]\n", err)
		return accBalReponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("failed to read accBal response, err=[%v]\n", err)
		return accBalReponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), accBalReponse)

	if accBalReponse.ResponseCode != "0" {
		log.Printf("failed to send mpesa acc bal request, responseBody=[%+v]\n", responseBody)
	}

	return accBalReponse, nil
}

func (s *AppMPesa) AuthenticateMPesa() (*entities.MPesaAuthentication, error) {

	authToken, err := s.sendMPesaAuthenticationenticationRequest()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return &entities.MPesaAuthentication{}, err
	}

	mpesaAuth := &entities.MPesaAuthentication{
		AccessToken: authToken.AccessToken,
		ExpiresIn:   authToken.ExpiresIn,
	}

	return mpesaAuth, err
}

func (s *AppMPesa) InitiateSTKPushRequest(
	form *forms.MPesaSTKPushRequestForm,
) (*entities.MPesaSTKPushResponse, error) {

	mpesaSTKPushResponse := &entities.MPesaSTKPushResponse{}

	mpesaToken, err := s.getMpesaAuthenticationToken()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return mpesaSTKPushResponse, err
	}

	url := fmt.Sprintf("%v/mpesa/stkpush/v1/processrequest", s.mpesaAPIURL)
	method := "POST"

	t := time.Now()
	currentTimeStamp := t.Format("**************")

	// base64.encode(Shortcode+Passkey+Timestamp)
	pwdStr := fmt.Sprintf("%v%v%v", s.mpesaShortCode, s.mpesaPassKey, currentTimeStamp)
	password := base64.StdEncoding.EncodeToString([]byte(pwdStr))

	stkPushForm := forms.MPesaSTKPushForm{
		BusinessShortCode: s.mpesaShortCode,
		Password:          password,
		Timestamp:         currentTimeStamp,
		TransactionType:   stkPushTransactionType,
		Amount:            form.Amount,
		PartyA:            form.PhoneNumber,
		PartyB:            s.mpesaShortCode,
		PhoneNumber:       form.PhoneNumber,
		CallBackURL:       HASH_DECODER_CALLBACK_URL,
		AccountReference:  form.AccountReference,
		TransactionDesc:   form.TransactionDescription,
	}

	stkPushPayload, err := json.Marshal(stkPushForm)
	if err != nil {
		log.Printf("failed to marshal stkPush form payload, err=[%v]\n", err)
	}

	payload := strings.NewReader(string(stkPushPayload))

	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Printf("failed to create new STK push request, err=[%v]\n", err)
		return mpesaSTKPushResponse, err
	}

	req.Header.Add("Authorization", "Bearer "+mpesaToken)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Printf("failed to send STK push request, err=[%v]\n", err)
		return mpesaSTKPushResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("failed to read STK push response, err=[%v]\n", err)
		return mpesaSTKPushResponse, err
	}

	responseBody := string(body)

	utils.Log.Infof("stk push resp=[%+v]", responseBody)

	json.Unmarshal([]byte(responseBody), mpesaSTKPushResponse)

	return mpesaSTKPushResponse, nil
}

func (s *AppMPesa) TransactionStatusQuery(
	trxID string,
) (*entities.MPesaTransactionStatusResponse, error) {

	mpesaTransactionStatusResponse := &entities.MPesaTransactionStatusResponse{}

	mpesaToken, err := s.getMpesaAuthenticationToken()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return mpesaTransactionStatusResponse, err
	}

	url := fmt.Sprintf("%v/mpesa/transactionstatus/v1/query", s.mpesaAPIURL)
	method := "POST"

	t := time.Now()
	currentTimeStamp := t.Format("**************")

	resultURL := fmt.Sprintf("%v/merchant_offering_orders/m_callback", os.Getenv("API_BASE_URL"))
	queueTimeOutURL := fmt.Sprintf("%v/merchant_offering_orders/m_timeout", os.Getenv("API_BASE_URL"))

	// base64.encode(Shortcode+Passkey+Timestamp)
	pwdStr := fmt.Sprintf("%v%v%v", s.mpesaShortCode, s.mpesaPassKey, currentTimeStamp)
	password := base64.StdEncoding.EncodeToString([]byte(pwdStr))

	trxStatusForm := forms.MpesaTransactionStatusForm{
		CommandID:          transactionStatusQueryCommandID,
		PartyA:             s.mpesaShortCode,
		IdentifierType:     organizationIdentityType,
		Initiator:          s.mpesaConsumerKey,
		SecurityCredential: password,
		TransactionID:      trxID,
		ResultURL:          resultURL,
		QueueTimeOutURL:    queueTimeOutURL,
		Remarks:            fmt.Sprintf("Trx status query for trx=[%v]", trxID),
		Occasion:           fmt.Sprintf("Trx status query for trx=[%v]", trxID),
	}

	stkPushPayload, err := json.Marshal(trxStatusForm)
	if err != nil {
		log.Printf("failed to marshal trxStatus form payload, err=[%v]\n", err)
	}

	payload := strings.NewReader(string(stkPushPayload))

	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Printf("failed to create new trxStatus request, err=[%v]\n", err)
		return mpesaTransactionStatusResponse, err
	}

	req.Header.Add("Authorization", "Bearer "+mpesaToken)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Printf("failed to send trxStatus request, err=[%v]\n", err)
		return mpesaTransactionStatusResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("failed to read trxStatus response, err=[%v]\n", err)
		return mpesaTransactionStatusResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), mpesaTransactionStatusResponse)

	return mpesaTransactionStatusResponse, nil
}

func (s *AppMPesa) RegisterURL(form *forms.RegisterURLForm) error {

	utils.Log.Infof("registering M-Pesa URL...\n")
	mpesaToken, err := s.getMpesaAuthenticationToken()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return err
	}

	url := fmt.Sprintf("%v/mpesa/c2b/v1/registerurl", s.mpesaAPIURL)
	method := "POST"

	form.ShortCode = os.Getenv("MPESA_SHORTCODE")
	form.ResponseType = "Completed" // Cancelled / Completed

	registerURLPayload, err := json.Marshal(form)
	if err != nil {
		log.Printf("failed to marshal registerURL form payload, err=[%v]\n", err)
		return utils.NewError(
			err,
			"failed to marshal registerURL form payload",
		)
	}

	payload := strings.NewReader(string(registerURLPayload))

	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Printf("failed to create new registerURL request, err=[%v]\n", err)
		return utils.NewError(
			err,
			"failed to create new registerURL request",
		)
	}

	req.Header.Add("Authorization", "Bearer "+mpesaToken)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Printf("failed to send registerURL request, err=[%v]\n", err)
		return utils.NewError(
			err,
			"failed to register url",
		)
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("failed to read registerURL response, err=[%v]\n", err)
		return utils.NewError(
			err,
			"failed to read registerURL response",
		)
	}

	responseBody := string(body)
	utils.Log.Infof("register M-Pesa URL response=[%+v]\n", responseBody)

	if strings.Contains(strings.ToLower(responseBody), "err") {
		errResponse := &entities.MPesaRegisterURLErrorResponse{}
		json.Unmarshal([]byte(responseBody), errResponse)
		return utils.NewError(
			err,
			"failed to register url",
			errResponse.ErrorMessage,
		)
	}

	mpesaRegisterURLResponse := &entities.MPesaRegisterURLResponse{}
	json.Unmarshal([]byte(responseBody), mpesaRegisterURLResponse)

	if mpesaRegisterURLResponse.ResponseCode != "0" && mpesaRegisterURLResponse.ResponseDescription != "Success" {
		log.Printf("err registering M-Pesa URL, responseBody=[%v]\n", responseBody)
		return utils.NewError(
			err,
			"failed to register url",
			mpesaRegisterURLResponse.ResponseDescription,
		)
	}

	return nil
}

func (s *AppMPesa) Reversal(
	mpesaTrxID string,
	amount float64,
) (*entities.MPesaReversalResponse, error) {

	mpesaReversalResponse := &entities.MPesaReversalResponse{}

	mpesaToken, err := s.getMpesaAuthenticationToken()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return mpesaReversalResponse, err
	}

	url := fmt.Sprintf("%v/mpesa/reversal/v1/request", s.mpesaAPIURL)
	method := "POST"

	t := time.Now()
	currentTimeStamp := t.Format("**************")

	// base64.encode(Shortcode+Passkey+Timestamp)
	pwdStr := fmt.Sprintf("%v%v%v", s.mpesaShortCode, s.mpesaPassKey, currentTimeStamp)
	password := base64.StdEncoding.EncodeToString([]byte(pwdStr))

	reversalForm := forms.MPesaReversalForm{
		Amount:                 1.00,
		CommandID:              transactionReversalCommandID,
		RecieverIdentifierType: reversalOrganizationIdentityType,
		ReceiverParty:          os.Getenv("MPESA_SHORTCODE"),
		Initiator:              s.mpesaConsumerKey,
		SecurityCredential:     password,
		QueueTimeOutURL:        s.apiBaseURL + "/payments/safaricom/timeout",
		Remarks:                "Transaction Reversal",
		ResultURL:              s.apiBaseURL + "/payments/safaricom/result",
		TransactionID:          mpesaTrxID,
	}

	reversalPayload, err := json.Marshal(reversalForm)
	if err != nil {
		log.Printf("failed to marshal reversal form payload, err=[%v]\n", err)
	}

	payload := strings.NewReader(string(reversalPayload))

	client := &http.Client{}

	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		log.Printf("failed to create new reversal request, err=[%v]\n", err)
		return mpesaReversalResponse, err
	}

	req.Header.Add("Authorization", "Bearer "+mpesaToken)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		log.Printf("failed to send reversal request, err=[%v]\n", err)
		return mpesaReversalResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("failed to read reversal response, err=[%v]\n", err)
		return mpesaReversalResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), mpesaReversalResponse)

	if mpesaReversalResponse.ResponseCode != "0" {
		log.Printf("failed to reverse mpesa trx, responseBody=[%+v]\n", responseBody)
	}

	return mpesaReversalResponse, nil
}

func (s *AppMPesa) sendMPesaAuthenticationenticationRequest() (*entities.MPesaAuthentication, error) {

	authToken := &entities.MPesaAuthentication{}
	url := fmt.Sprintf("%v/oauth/v1/generate?grant_type=client_credentials", s.mpesaAPIURL)
	method := "GET"
	client := &http.Client{}

	headerPayload := strings.NewReader("")
	req, err := http.NewRequest(method, url, headerPayload)
	if err != nil {
		log.Printf("err creating new request, err=[%v]\n", err)
		return authToken, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Basic "+s.basicAuth)

	res, err := client.Do(req)
	if err != nil {
		log.Printf("err processing request, err=[%v]\n", err)
		return authToken, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Printf("err processing response, err=[%v]\n", err)
		return authToken, err
	}

	responseBody := string(body)

	utils.Log.Infof("auth resp=[%+v]", responseBody)

	json.Unmarshal([]byte(responseBody), authToken)

	return authToken, nil
}

func (s *AppMPesa) getMpesaAuthenticationToken() (string, error) {

	authToken, err := s.sendMPesaAuthenticationenticationRequest()
	if err != nil {
		log.Printf("failed to authenticate M-Pesa, err=[%v]\n", err)
		return "", err
	}

	return authToken.AccessToken, nil
}
