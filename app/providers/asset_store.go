package providers

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

type AssetStore interface {
	Delete(ctx context.Context, key string) error
	Download(ctx context.Context, key string) ([]byte, error)
	DownloadURL(key string) (string, error)
	Upload(ctx context.Context, b []byte, contentType, key string, public bool) error
	UploadURL(ctx context.Context, key, contentMD5, contentType string, expiry time.Duration) (string, time.Time, error)
	URL(key string) string
}

type S3AssetStore struct {
	environment string
	s3          *s3.S3
	bucket      string
	region      string
}

func NewAssetStore(
	bucket,
	region,
	accessKeyId,
	secretAccessKey,
	environment string,
) *S3AssetStore {

	config := &aws.Config{
		Region: aws.String(region),
		Credentials: credentials.NewStaticCredentials(
			accessKeyId,
			secretAccessKey,
			"",
		),
	}

	if isUsingLocalstack(environment) {
		config.Endpoint = aws.String(os.Getenv("LOCALSTACK_ENDPOINT"))
		config.S3ForcePathStyle = aws.Bool(true)
	}

	s3Session, err := session.NewSession()
	if err != nil {
		panic("failed to initialize AWS S3 session")
	}
	awsS3 := s3.New(s3Session, config)

	return &S3AssetStore{
		environment: environment,
		s3:          awsS3,
		bucket:      bucket,
		region:      region,
	}
}

func isUsingLocalstack(environment string) bool {
	return strings.ToLower(environment) == "development" || strings.ToLower(environment) == "test"
}

func (s *S3AssetStore) Delete(ctx context.Context, key string) error {

	input := &s3.DeleteObjectInput{
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	}

	output, err := s.s3.DeleteObjectWithContext(ctx, input)
	if err != nil {
		return err
	}

	logger.Infof(
		"Successfully deleted file: bucket=[%v], key=[%v] and output=[%v]",
		s.bucket,
		key,
		output.GoString(),
	)

	return nil
}

func (s *S3AssetStore) Download(ctx context.Context, key string) ([]byte, error) {
	inputParams := &s3.GetObjectInput{
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	}

	output, err := s.s3.GetObjectWithContext(ctx, inputParams)
	if err != nil {
		return nil, err
	}

	defer output.Body.Close()

	file, err := io.ReadAll(output.Body)
	if err != nil {
		return nil, err
	}

	return file, nil
}

func (s *S3AssetStore) DownloadURL(key string) (string, error) {

	req, _ := s.s3.GetObjectRequest(
		&s3.GetObjectInput{
			Bucket: aws.String(s.bucket),
			Key:    aws.String(key),
		},
	)

	downloadURL, err := req.Presign(60 * time.Minute)
	if err != nil {
		return downloadURL, err
	}

	if isUsingLocalstack(s.environment) {
		downloadURL = strings.Replace(downloadURL, "http://localstack", "http://localhost", -1)
	}

	return downloadURL, nil
}

func (s *S3AssetStore) Upload(
	ctx context.Context,
	b []byte,
	contentType,
	key string,
	public bool,
) error {

	acl := "private"
	if public {
		acl = "public-read"
	}

	body := bytes.NewReader(b)

	params := &s3.PutObjectInput{
		ACL:           aws.String(acl),
		Body:          body,
		Bucket:        aws.String(s.bucket),
		ContentLength: aws.Int64(body.Size()),
		Key:           aws.String(key),
	}

	if contentType != "" {
		params.ContentType = aws.String(contentType)
	}

	result, err := s.s3.PutObjectWithContext(ctx, params)
	if err != nil {
		return err
	}

	logger.Infof(
		"[AssetStorage Upload] Successfully uploaded file: bucket=[%v], key=[%v], contentType=[%v], size=[%v], etag=[%v]",
		aws.StringValue(params.Bucket),
		aws.StringValue(params.Key),
		aws.StringValue(params.ContentType),
		aws.Int64Value(params.ContentLength),
		aws.StringValue(result.ETag),
	)

	return nil
}

func (s *S3AssetStore) UploadURL(
	ctx context.Context,
	key,
	contentMD5,
	contentType string,
	expiry time.Duration,
) (string, time.Time, error) {

	req, _ := s.s3.PutObjectRequest(&s3.PutObjectInput{
		ACL:    aws.String("public-read"),
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	})

	req.HTTPRequest.Header.Set("Content-MD5", contentMD5)
	req.HTTPRequest.Header.Set("Content-Type", contentType)

	signedURL, err := req.Presign(expiry)
	if err != nil {
		return "", time.Time{}, utils.NewError(
			err,
			"Failed to generate signed URL for key=[%v]",
			key,
		).Notify()
	}

	expiresAt := time.Now().Add(expiry)

	return signedURL, expiresAt, nil
}

// TODO: Fix this to account for localstack
func (s *S3AssetStore) URL(key string) string {
	return fmt.Sprintf(
		"https://%s.s3.%s.amazonaws.com/%s",
		s.bucket,
		s.region,
		key,
	)
}
