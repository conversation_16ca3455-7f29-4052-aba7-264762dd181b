package providers

import (
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"

	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
)

const sendSmsURL = "https://api.africastalking.com/version1/messaging"

type (
	AfricasTalking interface {
		SendSms(phoneNumber entities.PhoneNumber, message string, category entities.MessageCategory) error
	}

	AppAfricasTalking struct {
		apiKey                string
		marketingSenderID     string
		transactionalSenderID string
		username              string
	}
)

func NewAfricasTalking() AfricasTalking {
	return NewAfricasTalkingWithCredentials(
		os.Getenv("AFRICAS_TALKING_API_KEY"),
		os.Getenv("AFRICAS_TALKING_MARKETING_SENDER_ID"),
		os.Getenv("AFRICAS_TALKING_TRANSACTIONAL_SENDER_ID"),
		os.Getenv("AFRICAS_TALKING_USERNAME"),
	)
}

func NewAfricasTalkingWithCredentials(apiKey, marketingSenderID, transactionalSenderID, username string) AfricasTalking {
	return &AppAfricasTalking{
		apiKey:                apiKey,
		marketingSenderID:     marketingSenderID,
		transactionalSenderID: transactionalSenderID,
		username:              username,
	}
}

func (s *AppAfricasTalking) SendSms(phoneNumber entities.PhoneNumber, message string, category entities.MessageCategory) error {

	data := url.Values{}
	data.Set("username", s.username)
	data.Set("to", phoneNumber.String())
	data.Set("message", message)

	if category == entities.MessageCategoryMarketing {
		data.Set("from", s.marketingSenderID)
	} else {
		data.Set("from", s.transactionalSenderID)
	}

	req, err := http.NewRequest("POST", sendSmsURL, strings.NewReader(data.Encode()))
	if err != nil {
		return utils.NewError(
			err,
			"unable to POST Africas Talking send SMS Request.",
		)
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("apiKey", s.apiKey)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process Africa's Talking authentication request.",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process Africa's Talking response body.",
		)
	}

	if res.StatusCode != http.StatusCreated {
		return utils.NewError(
			err,
			"Failed to send sms message via Africas Talking : "+string(body),
		)
	}

	return nil
}
