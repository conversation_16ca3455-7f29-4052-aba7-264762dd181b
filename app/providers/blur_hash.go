package providers

import (
	"bytes"
	"compliance-and-risk-management-backend/app/utils"

	"github.com/buckket/go-blurhash"
	"github.com/disintegration/imaging"
)

type (
	BlurHasher interface {
		BlurImage([]byte) (string, error)
	}

	AppBlurHasher struct {
	}
)

func NewBlurHasher() BlurHasher {
	return &AppBlurHasher{}
}

func (bh *AppBlurHasher) BlurImage(
	data []byte,
) (string, error) {
	ioReader := bytes.NewReader(data)

	image, err := imaging.Decode(ioReader)
	if err != nil {
		return "", utils.NewError(
			err,
			"Error decoding image from byte slice",
		)
	}

	str, err := blurhash.Encode(4, 3, image)
	if err != nil {
		return "", utils.NewError(
			err,
			"Error encoding image to blur hash",
		)
	}

	return str, nil
}
