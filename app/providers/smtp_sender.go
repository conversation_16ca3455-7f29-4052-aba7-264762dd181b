package providers

import (
	"bytes"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"
	"html/template"
	"os"
	"strconv"

	"compliance-and-risk-management-backend/app/entities"

	gomail "gopkg.in/gomail.v2"
)

type (
	SmtpSender interface {
		SendSmtp(to, subject, message string, emailConfig *entities.EmailConfig) error
		SendEmailWithTemplate(application, htmlTemplate, to, subject string, items interface{}, attachments []string) error
	}

	AppSmtpSender struct {
		smtpEmail    string
		smtpHost     string
		smtpPassword string
		smtpPort     string
	}

	EmailContent struct {
		Name    string
		Message string
	}
)

func NewSmtpSender() SmtpSender {
	return NewSmtpSenderWithCredentials(
		os.Getenv("SMTP_EMAIL"),
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PASSWORD"),
		os.<PERSON>env("SMTP_PORT"),
	)
}

func NewSmtpSenderWithCredentials(smtpEmail, smtpHost, smtpPassword, smtpPort string) SmtpSender {
	return &AppSmtpSender{
		smtpEmail:    smtpEmail,
		smtpHost:     smtpHost,
		smtpPassword: smtpPassword,
		smtpPort:     smtpPort,
	}
}

func (s *AppSmtpSender) SendSmtp(to, subject, message string, emailConfig *entities.EmailConfig) error {

	smtpEmail := s.smtpEmail
	smtpPassword := s.smtpPassword
	if emailConfig != nil {
		smtpEmail = emailConfig.SmtpEmail
		smtpPassword = emailConfig.SmtpPassword
	}

	m := gomail.NewMessage()
	m.SetHeader("From", smtpEmail)
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", message)

	smtpPort, err := strconv.Atoi(s.smtpPort)
	if err != nil {
		logger.Errorf("Failed to send email, err=[%v]", err)
	}

	d := gomail.NewDialer(s.smtpHost, smtpPort, smtpEmail, smtpPassword)

	if err := d.DialAndSend(m); err != nil {
		logger.Errorf("Failed to send email, err=[%v]", err)
	}

	return nil
}

func (s *AppSmtpSender) SendEmailWithTemplate(
	application, htmlTemplate, to, subject string,
	items interface{},
	attachments []string,
) error {

	var from string

	if application == "optimus" {
		from = "Optimus <<EMAIL>>"

	} else {
		from = "Optimus <<EMAIL>>"
	}

	result, err := s.parseTemplate(htmlTemplate, items)
	if err != nil {
		logger.Errorf("Failed to parse html template, err=[%v]", err)
		return err
	}

	m := gomail.NewMessage()
	m.SetHeader("From", from)
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", result)

	smtpPort, err := strconv.Atoi(s.smtpPort)
	if err != nil {
		logger.Errorf("Failed to send email, err=[%v]", err)
		return err
	}

	d := gomail.NewDialer(s.smtpHost, smtpPort, s.smtpEmail, s.smtpPassword)

	if len(attachments) > 0 {
		for _, attachment := range attachments {
			m.Attach(attachment)
		}
	}

	if err := d.DialAndSend(m); err != nil {
		logger.Errorf("Failed to send email, err=[%v]", err)
		return err
	}

	utils.Log.Infof("Email sent.\n")
	return nil
}

func (s *AppSmtpSender) parseTemplate(fileName string, data interface{}) (string, error) {
	bodyStr := ""
	t, err := template.ParseFiles(fileName)
	if err != nil {
		return bodyStr, err
	}
	buffer := new(bytes.Buffer)
	if err = t.Execute(buffer, data); err != nil {
		return bodyStr, err
	}

	bodyStr = buffer.String()

	return bodyStr, nil
}
