package providers

import (
	"strings"
	"testing"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	. "github.com/smartystreets/goconvey/convey"
)

func TestUpdateItem_setAndAddAttributesValues(t *testing.T) {
	Convey("DynamoDB_SetAndAddAttributesValues", t, func() {
		updateItem := UpdateItem{
			SetAttributeValues: map[string]*dynamodb.AttributeValue{
				"channel_id": {
					S: aws.String("channel value"),
				},
				"connections": {
					SS: aws.StringSlice([]string{"conn1", "conn2"}),
				},
			},
			RemoveAttributes: nil,
		}

		dynamodbUpdateItem := &dynamodb.UpdateItemInput{
			UpdateExpression:          aws.String(""),
			ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{},
		}

		updateItem.setAndAddAttributesValues(dynamodbUpdateItem)

		So(strings.Contains(*dynamodbUpdateItem.UpdateExpression, "channel_id=:set"), ShouldBeTrue)
		So(strings.Contains(*dynamodbUpdateItem.UpdateExpression, "connections=:set"), ShouldBeTrue)
		So(len(dynamodbUpdateItem.ExpressionAttributeValues), ShouldEqual, 2)
	})
}

func TestUpdateItem_setRemoveAttributes(t *testing.T) {
	Convey("DynamoDB_SetAndAddAttributesValues", t, func() {
		UpdateItem := UpdateItem{
			SetAttributeValues: map[string]*dynamodb.AttributeValue{
				"channel_id": {
					S: aws.String("channel value"),
				},
				"connections": {
					SS: aws.StringSlice([]string{"conn1", "conn2"}),
				},
			},
			RemoveAttributes: []string{"attribute1", "attribute2"},
		}

		dynamodbUpdateItem := &dynamodb.UpdateItemInput{
			UpdateExpression:          aws.String(""),
			ExpressionAttributeValues: nil,
		}

		UpdateItem.setRemoveAttributes(dynamodbUpdateItem)

		So(*dynamodbUpdateItem.UpdateExpression, ShouldEqual, "REMOVE #RM1, #RM2")
		So(len(dynamodbUpdateItem.ExpressionAttributeNames), ShouldEqual, 2)
	})
}
