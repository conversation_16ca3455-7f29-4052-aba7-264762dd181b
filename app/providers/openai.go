package providers

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	coreentities "compliance-and-risk-management-backend/app/entities"
	coreforms "compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"

	"github.com/go-resty/resty/v2"
)

const OPENAI_BASE_URL = "https://api.openai.com/v1"

type (
	OpenAI interface {
		AddMessageToThread(threadID string, thread *coreentities.OpenAIThread) (*coreentities.OpenAIChatResponse, error)
		CheackAIThreadRunStatus(threadID string, runID string) (*coreentities.OpenAIThreadRunResponse, error)
		CreateAIAssistant(form *coreforms.CreateOpenAIAssistantForm) (*coreentities.OpenAIAssistant, error)
		CreateAIThread(form *coreforms.CreateOpenAIThreadMessageForm) (*coreentities.OpenAIThread, error)
		CreateAIThreadMessage(threadID string, form *coreforms.CreateOpenAIMessageForm) (*coreentities.OpenAIThreadMessage, error)
		CreateAIThreadRun(threadID string, assistantID string) (*coreentities.OpenAIThreadRunResponse, error)
		FilterThreadMessages(threadID string, filter *coreentities.PaginationFilter) (*coreentities.OpenAIThreadMessages, error)
		ListAIAssistants() (*coreentities.OpenAIAssistantList, error)
		RetrieveAIAssistant(threadID string) (*coreentities.OpenAIAssistant, error)
		RetrieveAIThread(threadID string) (*coreentities.OpenAIThread, error)
		RetrieveAIThreadRun(threadID, runID string) (*coreentities.OpenAIThreadRunResponse, error)
		SendRequest(request string) (*coreentities.OpenAIChatResponse, error)
		UpdateAIAssistant(aiAssistantID string, form *coreforms.UpdateOpenAIAssistantForm) (*coreentities.OpenAIAssistant, error)
	}

	AppOpenAI struct {
		apiKey string
	}
)

func NewOpenAI(openAIApiKey string) OpenAI {

	appOpenAI := &AppOpenAI{
		apiKey: openAIApiKey,
	}

	return appOpenAI
}

func (s *AppOpenAI) AddMessageToThread(
	threadID string,
	thread *coreentities.OpenAIThread,
) (
	*coreentities.OpenAIChatResponse, error,
) {

	aiResponse := &coreentities.OpenAIChatResponse{}

	apiURL := fmt.Sprintf("%v/threads/%v/messages", OPENAI_BASE_URL, threadID)

	assitantPayload, err := json.Marshal(thread)
	if err != nil {
		log.Printf("failed to marshal openai assitant payload, err=[%v]\n", err)
	}
	payload := strings.NewReader(string(assitantPayload))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to add message to thread",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}

func (s *AppOpenAI) CheackAIThreadRunStatus(
	threadID string,
	runID string,
) (
	*coreentities.OpenAIThreadRunResponse, error,
) {

	aiResponse := &coreentities.OpenAIThreadRunResponse{}

	apiURL := fmt.Sprintf("%v/threads/%v/runs/%v", OPENAI_BASE_URL, threadID, runID)

	req, err := http.NewRequest("POST", apiURL, nil)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to check AI thread run status",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread run status request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread run status response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}

func (s *AppOpenAI) CreateAIAssistant(form *coreforms.CreateOpenAIAssistantForm) (
	*coreentities.OpenAIAssistant, error,
) {

	aiResponse := &coreentities.OpenAIAssistant{}

	apiURL := fmt.Sprintf("%v/assistants", OPENAI_BASE_URL)

	assitantPayload, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal openai assitant payload, err=[%v]\n", err)
		return aiResponse, err
	}
	payload := strings.NewReader(string(assitantPayload))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to create AI assitant",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	if aiResponse.Error != nil {
		logger.Errorf("error creating ai assistant, err=[%+v]", aiResponse)
		return aiResponse, errors.New("error creating ai assistant")
	}

	return aiResponse, nil
}

func (s *AppOpenAI) CreateAIThread(form *coreforms.CreateOpenAIThreadMessageForm) (
	*coreentities.OpenAIThread, error,
) {

	aiResponse := &coreentities.OpenAIThread{}

	apiURL := fmt.Sprintf("%v/threads", OPENAI_BASE_URL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal openai thread message payload, err=[%v]\n", err)
		return aiResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to create thread",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}

func (s *AppOpenAI) CreateAIThreadMessage(
	threadID string,
	form *coreforms.CreateOpenAIMessageForm,
) (
	*coreentities.OpenAIThreadMessage, error,
) {

	aiResponse := &coreentities.OpenAIThreadMessage{}

	apiURL := fmt.Sprintf("%v/threads/%v/messages", OPENAI_BASE_URL, threadID)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal openai thread message payload, err=[%v]\n", err)
		return aiResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to create AI thread message",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread message request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread message response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}

func (s *AppOpenAI) CreateAIThreadRun(
	threadID string,
	assistantID string,
) (
	*coreentities.OpenAIThreadRunResponse, error,
) {

	aiResponse := &coreentities.OpenAIThreadRunResponse{}

	apiURL := fmt.Sprintf("%v/threads/%v/runs", OPENAI_BASE_URL, threadID)

	form := map[string]string{
		"assistant_id": assistantID,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal openai thread run payload, err=[%v]\n", err)
		return aiResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to create AI thread run",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread run request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai thread run response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}

func (s *AppOpenAI) FilterThreadMessages(
	threadID string,
	filter *coreentities.PaginationFilter,
) (
	*coreentities.OpenAIThreadMessages, error,
) {

	threadMessages := &coreentities.OpenAIThreadMessages{}

	per := 20
	if filter.Per != 0 {
		per = filter.Per
	}

	order := "asc"
	if filter.OrderBy != "" {
		order = filter.OrderBy
	}

	apiURL := fmt.Sprintf(
		"%v/threads/%v/messages?order=%v&limit=%v",
		OPENAI_BASE_URL,
		threadID,
		order,
		per,
	)

	if filter.After != "" {
		apiURL += fmt.Sprintf("&after=%v", filter.After)
	}

	if filter.Before != "" {
		apiURL += fmt.Sprintf("&before=%v", filter.Before)
	}

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return threadMessages, utils.NewError(
			err,
			"unable to retrieve thread messages",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return threadMessages, utils.NewError(
			err,
			"unable to process openai thread messages request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return threadMessages, utils.NewError(
			err,
			"unable to process openai thread messages response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &threadMessages)

	return threadMessages, nil
}

func (s *AppOpenAI) ListAIAssistants() (
	*coreentities.OpenAIAssistantList, error,
) {

	openAIAssistants := &coreentities.OpenAIAssistantList{}

	apiURL := fmt.Sprintf("%v/assistants", OPENAI_BASE_URL)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return openAIAssistants, utils.NewError(
			err,
			"unable to retrieve AI assistants",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return openAIAssistants, utils.NewError(
			err,
			"unable to process openai AI assistants request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return openAIAssistants, utils.NewError(
			err,
			"unable to process openai AI assistants response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &openAIAssistants)

	return openAIAssistants, nil
}

func (s *AppOpenAI) RetrieveAIAssistant(assistantID string) (
	*coreentities.OpenAIAssistant, error,
) {

	aiAssistant := &coreentities.OpenAIAssistant{}

	apiURL := fmt.Sprintf("%v/assistants/%v", OPENAI_BASE_URL, assistantID)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return aiAssistant, utils.NewError(
			err,
			"unable to retrieve AI assistant",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiAssistant, utils.NewError(
			err,
			"unable to process openai AI assistant request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiAssistant, utils.NewError(
			err,
			"unable to process openai AI assistant response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiAssistant)

	return aiAssistant, nil
}

func (s *AppOpenAI) RetrieveAIThread(threadID string) (
	*coreentities.OpenAIThread, error,
) {

	aiThread := &coreentities.OpenAIThread{}

	apiURL := fmt.Sprintf("%v/threads/%v", OPENAI_BASE_URL, threadID)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return aiThread, utils.NewError(
			err,
			"unable to retrieve AI thread",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiThread, utils.NewError(
			err,
			"unable to process openai AI thread request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiThread, utils.NewError(
			err,
			"unable to process openai AI thread response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiThread)

	return aiThread, nil
}

func (s *AppOpenAI) RetrieveAIThreadRun(
	threadID string,
	runID string,
) (
	*coreentities.OpenAIThreadRunResponse, error,
) {

	response := &coreentities.OpenAIThreadRunResponse{}

	apiURL := fmt.Sprintf("%v/threads/%v/runs/%v", OPENAI_BASE_URL, threadID, runID)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to retrieve AI thread",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process openai AI thread request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process openai AI thread response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &response)

	return response, nil
}

func (s *AppOpenAI) SendRequest(request string) (
	*coreentities.OpenAIChatResponse, error,
) {

	aiResponse := &coreentities.OpenAIChatResponse{}
	client := resty.New()

	apiEndpoint := fmt.Sprintf("%v/chat/completions", OPENAI_BASE_URL)

	response, err := client.R().
		SetAuthToken(s.apiKey).
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]interface{}{
			"model":      "gpt-3.5-turbo",
			"messages":   []interface{}{map[string]interface{}{"role": "system", "content": request}},
			"max_tokens": 50,
		}).
		Post(apiEndpoint)

	if err != nil {
		log.Fatalf("Error while sending send the request: %v", err)
	}

	body := response.Body()

	openAIChatResponse := &coreentities.OpenAIChatResponse{}
	err = json.Unmarshal(body, &openAIChatResponse)
	if err != nil {
		fmt.Println("Error while decoding JSON response:", err)
		return aiResponse, err
	}

	content := openAIChatResponse.Choices[0].Message.Content
	fmt.Println(content)

	return openAIChatResponse, nil
}

func (s *AppOpenAI) UpdateAIAssistant(
	aiAssistantID string,
	form *coreforms.UpdateOpenAIAssistantForm,
) (
	*coreentities.OpenAIAssistant, error,
) {

	aiResponse := &coreentities.OpenAIAssistant{}

	apiURL := fmt.Sprintf("%v/assistants/%v", OPENAI_BASE_URL, aiAssistantID)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal openai assitant update payload, err=[%v]\n", err)
		return aiResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to update AI assitant",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+s.apiKey)
	req.Header.Add("OpenAI-Beta", "assistants=v1")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant update request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return aiResponse, utils.NewError(
			err,
			"unable to process openai assitant update response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &aiResponse)

	return aiResponse, nil
}
