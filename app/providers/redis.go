package providers

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gomodule/redigo/redis"
)

type (
	Redis interface {
		Float64(key string) (float64, error)
		Get(key string) (interface{}, error)
		Int64(key string) (int64, error)
		IsErrNil(err error) bool
		Set(key string, val interface{}) (interface{}, error)
		SetEx(key string, ttl time.Duration, val interface{}) (interface{}, error)
		String(key string) (string, error)
	}

	RedisConfig struct {
		IdleTimeout time.Duration
		MaxActive   int
		MaxIdle     int
	}

	AppRedis struct {
		pool *redis.Pool
	}
)

func NewRedisProvider(
	config *RedisConfig,
) *AppRedis {
	return NewRedisProviderWithParams(
		os.Getenv("REDIS_HOST"),
		os.<PERSON>env("REDIS_PORT"),
		os.Getenv("REDIS_PASSWORD"),
		config,
	)
}

func NewRedisProviderWithParams(
	host,
	port,
	password string,
	config *RedisConfig,
) *AppRedis {
	idleTimeout := 2 * time.Minute
	maxActive := 200
	maxIdle := 5

	if config != nil {
		if int64(config.IdleTimeout) != 0 {
			idleTimeout = config.IdleTimeout
		}

		if config.MaxActive != 0 {
			maxActive = config.MaxActive
		}

		if config.MaxIdle != 0 {
			maxIdle = config.MaxIdle
		}
	}

	redisPool := &redis.Pool{
		IdleTimeout: idleTimeout,
		MaxActive:   maxActive,
		MaxIdle:     maxIdle,
		Dial: func() (redis.Conn, error) {
			c, err := redis.Dial(
				"tcp",
				fmt.Sprintf("%s:%s", host, port),
			)
			if err != nil {
				log.Printf("unable to connect to redis, err=[%v]\n", err.Error())
			}

			if len(password) > 0 {
				_, err := c.Do("AUTH", password)
				if err != nil {
					log.Printf("unable to authenticate to redis, err=[%v]\n", err.Error())
				}
			}

			return c, err
		},
	}

	return &AppRedis{
		pool: redisPool,
	}
}

func (p *AppRedis) Float64(
	key string,
) (float64, error) {
	return redis.Float64(p.Get(key))
}

func (p *AppRedis) Get(
	key string,
) (interface{}, error) {
	return p.do("GET", key)
}

func (p *AppRedis) Int64(
	key string,
) (int64, error) {
	return redis.Int64(p.Get(key))
}

func (p *AppRedis) IsErrNil(
	err error,
) bool {
	return err == redis.ErrNil
}

func (p *AppRedis) RedisPool() *redis.Pool {
	return p.pool
}

func (p *AppRedis) Set(
	key string,
	val interface{},
) (interface{}, error) {
	return p.do("SET", key, val)
}

func (p *AppRedis) SetEx(
	key string,
	ttl time.Duration,
	val interface{},
) (interface{}, error) {
	return p.do("SETEX", key, ttl.Seconds(), val)
}

func (p *AppRedis) String(
	key string,
) (string, error) {
	return redis.String(p.Get(key))
}

func (p *AppRedis) do(
	commandName string,
	args ...interface{},
) (interface{}, error) {
	conn := p.pool.Get()
	defer conn.Close()

	return conn.Do(commandName, args...)
}
