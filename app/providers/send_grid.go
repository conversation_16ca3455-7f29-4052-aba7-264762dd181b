package providers

import (
	"os"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

type (
	EmailInformation struct {
		Name  string
		Email string
	}

	Message struct {
		Content string
		Subject string
	}

	SendGrid interface {
		SendEmail(to *EmailInformation, message *Message) error
	}

	AppSendGrid struct {
		client            *sendgrid.Client
		senderInformation *mail.Email
	}
)

func NewSendGrid() SendGrid {
	return NewSendGridWithCredentials(
		os.Getenv("SENDGRID_API_KEY"),
		os.Getenv("SENDGRID_EMAIL_SENDER_NAME"),
		os.Getenv("SENDGRID_EMAIL_SENDER_ADDRESS"),
	)
}

func NewSendGridWithCredentials(
	sendGridApiKey,
	emailSenderName,
	emailSenderAddress string,
) SendGrid {
	return &AppSendGrid{
		client:            sendgrid.NewSendClient(sendGridApiKey),
		senderInformation: mail.NewEmail(emailSenderName, emailSenderAddress),
	}
}

func (s *AppSendGrid) SendEmail(to *EmailInformation, message *Message) error {

	_, err := s.client.Send(mail.NewSingleEmail(
		s.senderInformation,
		message.Subject,
		mail.NewEmail(to.Name, to.Email),
		" ",
		message.Content,
	))

	return err
}
