package providers

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"
	"encoding/json"
	"fmt"

	"io"
	"net/http"
	"os"
	"strings"
)

type (
	VFMS interface {
		GetNonTaxItems() ([]*entities.VFMSItem, error)
		PostSale(*forms.VFMSSaleForm) (*entities.VFMSSalesResponse, error)
	}

	AppVFMS struct {
		apiURL        string
		integrationID string
		tokenID       string
	}
)

func NewVFMS() VFMS {

	appVFMS := &AppVFMS{
		apiURL:        os.Getenv("VFMS_API_URL"),
		integrationID: os.Getenv("VFMS_INTEGRATION_ID"),
		tokenID:       os.Getenv("VFMS_TOKEN_ID"),
	}

	return appVFMS
}

func (s *AppVFMS) GetNonTaxItems() ([]*entities.VFMSItem, error) {

	nonTaxItems := []*entities.VFMSItem{}

	apiURL := fmt.Sprintf("%v/getNonTaxItems/", s.apiURL)

	req, err := http.NewRequest("POST", apiURL, nil)
	if err != nil {
		return nonTaxItems, utils.NewError(
			err,
			"unable to add message to thread",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("vfms-request-type", "NON_TAX_ITEMS ")
	req.Header.Add("vfms-intergration-id", s.integrationID)
	req.Header.Add("vfms-token-id", s.tokenID)
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return nonTaxItems, utils.NewError(
			err,
			"unable to process VFMS request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nonTaxItems, utils.NewError(
			err,
			"unable to process VFMS response body.",
		)
	}

	responseBodyString := string(body)
	// utils.Log.Infof("VFMS getNonTaxItems response=[%v]\n", responseBodyString)
	utils.PrettyPrintJSON(body)

	json.Unmarshal([]byte(responseBodyString), &nonTaxItems)

	return nonTaxItems, nil
}

func (s *AppVFMS) PostSale(
	form *forms.VFMSSaleForm,
) (*entities.VFMSSalesResponse, error) {

	salesResponse := &entities.VFMSSalesResponse{}

	apiURL := fmt.Sprintf("%v/sales/", s.apiURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal VFMS sales form payload, err=[%v]\n", err)
		return salesResponse, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return salesResponse, utils.NewError(
			err,
			"unable to add message to thread",
		)
	}

	utils.Log.Infof("-----------------VFMS PostSale request--------------------\n")
	utils.PrettyPrintJSON(payloadBytes)
	utils.Log.Infof("-----------------------------------------------------------\n\n")

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("vfms-request-type", "NORMAL_SALES ")
	req.Header.Add("vfms-intergration-id", s.integrationID)
	req.Header.Add("vfms-token-id", s.tokenID)
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return salesResponse, utils.NewError(
			err,
			"unable to process VFMS request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return salesResponse, utils.NewError(
			err,
			"unable to process VFMS response body.",
		)
	}

	responseBodyString := string(body)
	fmt.Println("-------------------VFMS PostSale response------------------")
	utils.PrettyPrintJSON(body)
	utils.Log.Infof("-----------------------------------------------------------\n\n")

	json.Unmarshal([]byte(responseBodyString), &salesResponse)

	return salesResponse, nil
}
