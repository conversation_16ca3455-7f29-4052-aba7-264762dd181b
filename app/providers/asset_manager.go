package providers

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"mime"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"
	"strings"
	"time"

	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
)

const (
	maxContentSize = 25 * 1024 * 1024 // 25 MB
)

var assetExpiryDuration = time.Hour

type (
	AssetManager interface {
		AssetUploadURL(ctx context.Context, asset *entities.Asset) (*entities.AssetUploadURL, error)
		BuildAsset(form *forms.CreateAssetForm, allowableContentTypes ...entities.ContentType) (entities.Asset, error)
		DeleteAsset(ctx context.Context, asset *entities.Asset) error
		DownloadAsset(ctx context.Context, asset *entities.Asset) ([]byte, error)
		GenerateAndUploadThumbnail(ctx context.Context, asset *entities.Asset, b []byte) error
		PopulateAssetAndThumbnailKeyAndURL(ctx context.Context, asset *entities.Asset, assetKeyInfo *entities.AssetKeyInfo) error
		UpdateAssetStatus(ctx context.Context, asset *entities.Asset, form *forms.UpdateAssetStatusForm) error
		UploadAsset(ctx context.Context, asset *entities.Asset, b64Data string) error
	}

	AppAssetManager struct {
		assetStore       AssetStore
		blurHash         BlurHasher
		imaging          Imaging
		uploadAssetStore AssetStore
	}
)

func NewAssetManager(
	assetStore,
	uploadAssetStore AssetStore,
	blurHash BlurHasher,
	imaging Imaging,
) AssetManager {
	return &AppAssetManager{
		assetStore:       assetStore,
		blurHash:         blurHash,
		imaging:          imaging,
		uploadAssetStore: uploadAssetStore,
	}
}

func (am *AppAssetManager) AssetUploadURL(ctx context.Context, asset *entities.Asset) (*entities.AssetUploadURL, error) {

	assetUploadURL := &entities.AssetUploadURL{}

	signedURL, expiresAt, err := am.uploadAssetStore.UploadURL(
		ctx,
		asset.Key,
		asset.ContentMD5,
		asset.ContentType,
		assetExpiryDuration,
	)
	if err != nil {
		return assetUploadURL, utils.NewError(
			err,
			"Failed to generate signed URL to upload asset=[%v]",
			asset.ID,
		).Notify()
	}

	assetUploadURL.Asset = asset
	assetUploadURL.ExpiresAt = expiresAt.Unix()
	assetUploadURL.UploadURL = signedURL

	return assetUploadURL, nil
}

func (am *AppAssetManager) BuildAsset(form *forms.CreateAssetForm, allowableContentTypes ...entities.ContentType) (entities.Asset, error) {

	var asset entities.Asset

	if !form.Async && form.Data == nil {
		return asset, utils.NewErrorWithCode(
			errors.New("image data not provided"),
			utils.ErrorCodeInvalidArgument,
			"Failed to build asset on sync upload",
		)
	}

	if form.ContentSize > maxContentSize {
		return asset, utils.NewErrorWithCode(
			errors.New("maximum content size exceeded"),
			utils.ErrorCodeInvalidArgument,
			"Failed to build asset with content size=[%v]",
			form.ContentSize,
		)
	}

	validContentType := false
	for _, allowableContentType := range allowableContentTypes {
		if strings.EqualFold(form.ContentType, allowableContentType.String()) {
			validContentType = true
			break
		}
	}

	if !validContentType {
		return asset, utils.NewErrorWithCode(
			errors.New("invalid content type"),
			utils.ErrorCodeInvalidArgument,
			"Failed to build asset with content_type=[%v]",
			form.ContentType,
		)
	}

	extensions, err := mime.ExtensionsByType(form.ContentType)
	if err == nil && extensions == nil {
		err = errors.New("invalid content type")
	}

	if err != nil {
		return asset, utils.NewErrorWithCode(
			err,
			utils.ErrorCodeInvalidArgument,
			"Failed to build asset with content_type=[%v]",
			form.ContentType,
		)
	}

	// generate blur hash if asset is being uploaded synchronously
	if !form.Async && form.Data != nil {
		isImage, err := am.imaging.CheckIsImage(form.ContentType)
		if err != nil {
			return asset, utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to valid content type=[%v] before generating blur hash",
				form.ContentType,
			)
		}

		if isImage {
			data, err := base64.StdEncoding.DecodeString(utils.StringValue(form.Data))
			if err != nil {
				return asset, utils.NewError(
					err,
					"Failed to decode base64 image data for asset=[%v]",
					asset.Key,
				)
			}

			blurHashStr, err := am.blurHash.BlurImage(data)
			if err != nil {
				return asset, utils.NewError(
					err,
					"Failed to generate blur hash string",
				)
			}

			asset.BlurHash = utils.String(blurHashStr)
		}
	}

	asset.ContentMD5 = form.ContentMD5
	asset.ContentSize = form.ContentSize
	asset.ContentType = form.ContentType
	asset.Description = form.Description
	asset.Duration = form.Duration
	asset.Name = form.Name
	asset.Status = entities.AssetStatusPending
	asset.ThumbnailStatus = entities.AssetStatusPending
	asset.ImageLength = form.ImageLength
	asset.ImageWidth = form.ImageWidth

	return asset, nil
}

func (am *AppAssetManager) DeleteAsset(ctx context.Context, asset *entities.Asset) error {

	err := am.assetStore.Delete(ctx, asset.Key)
	if err != nil {
		return err
	}

	if asset.ThumbnailKey != nil {
		return am.assetStore.Delete(ctx, utils.StringValue(asset.ThumbnailKey))
	}

	return nil
}

// TODO: We should avoid using this since this would incur costs.
// We should look into updating the existing lambda function so it takes in an input S3 key and an output S3 key
// The Lambda function should download the asset from the input S3, generate thumbnail and upload to output S3 key
// S3 data transfers between AWS transfers do not add costs.
func (am *AppAssetManager) DownloadAsset(ctx context.Context, asset *entities.Asset) ([]byte, error) {
	b, err := am.assetStore.Download(ctx, asset.Key)
	if err != nil {
		return b, utils.NewError(
			err,
			"Failed to download asset[%v] from s3",
			asset.ID,
		).Notify()
	}

	return b, nil
}

func (am *AppAssetManager) GenerateAndUploadThumbnail(
	ctx context.Context,
	asset *entities.Asset,
	b []byte,
) error {

	// TODO remove this after implementing video thumbnail
	// also make CheckIsImage() private
	isImage, err := am.imaging.CheckIsImage(asset.ContentType)
	if err != nil {
		return utils.NewError(
			err,
			"failed to check if asset is an image",
		)
	}

	if !isImage {
		return utils.NewError(
			errors.New("not implemented"),
			"generating video thumbnail not implemented yet",
		)
	}

	thumbnailInfo, err := am.imaging.GenerateThumbnail(b, asset.ContentType)
	if err != nil {
		return utils.NewError(
			err,
			"failed to generate asset thumbnail",
		)
	}

	err = am.assetStore.Upload(ctx, thumbnailInfo.Data, asset.ContentType, utils.StringValue(asset.ThumbnailKey), true)
	if err != nil {
		return utils.NewError(
			err,
			"failed to upload asset thumbnail",
		)
	}

	asset.ThumbnailContentSize = utils.Int(len(thumbnailInfo.Data))
	asset.ThumbnailContentType = utils.String(thumbnailInfo.ContentType)
	asset.ThumbnailImageLength = utils.Int(thumbnailInfo.Length)
	asset.ThumbnailImageWidth = utils.Int(thumbnailInfo.Width)
	asset.ThumbnailStatus = entities.AssetStatusActive
	asset.ThumbnailUploadedAt = utils.Time(time.Now())

	return nil
}

func (am *AppAssetManager) PopulateAssetAndThumbnailKeyAndURL(
	ctx context.Context,
	asset *entities.Asset,
	assetKeyInfo *entities.AssetKeyInfo,
) error {

	extensions, err := mime.ExtensionsByType(asset.ContentType)
	if err == nil && extensions == nil {
		err = errors.New("invalid content type")
	}

	if err != nil {
		return utils.NewErrorWithCode(
			err,
			utils.ErrorCodeInvalidArgument,
			"Failed to populate key and url for contentType=[%v]",
			asset.ContentType,
		).Notify()
	}

	uuid := utils.GenerateAssetUUID()

	key := fmt.Sprintf(
		"assets/%s/%d/%s/original/%s%s",
		assetKeyInfo.CoreResourceName,
		assetKeyInfo.CoreResourceId,
		assetKeyInfo.SecondaryResourceName,
		uuid,
		extensions[0],
	)

	thumbnailKey := fmt.Sprintf(
		"assets/%s/%d/%s/thumbnail/%s%s",
		assetKeyInfo.CoreResourceName,
		assetKeyInfo.CoreResourceId,
		assetKeyInfo.SecondaryResourceName,
		uuid,
		extensions[0],
	)

	asset.Key = key
	asset.ThumbnailKey = utils.String(thumbnailKey)
	asset.URL = am.assetStore.URL(key)
	asset.ThumbnailURL = am.assetStore.URL(thumbnailKey)

	return nil
}

func (am *AppAssetManager) UpdateAssetStatus(
	ctx context.Context,
	asset *entities.Asset,
	form *forms.UpdateAssetStatusForm,
) error {

	if !form.Status.IsValid() {
		return utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid asset upload status"),
			utils.ErrorCodeInvalidForm,
			"Asset upload status is invalid",
			"invalid upload status=[%v]",
			form.Status,
		)
	}

	if form.Status.IsSuccess() {
		asset.Status = entities.AssetStatusActive
	} else {
		asset.Status = entities.AssetStatusFailed
	}

	asset.Reason = form.Reason

	if asset.Status.IsActive() {
		asset.UploadedAt = utils.Time(time.Now())

		isImage, err := am.imaging.CheckIsImage(asset.ContentType)
		if err != nil {
			return utils.NewError(
				err,
				"Failed to valid content type=[%v] before generating blur hash",
				asset.ContentType,
			)
		}

		if !isImage {
			// TODO remove this condition after generating video thumbnails is implemented
			return nil
		}

		// TODO Invoke Lambda function to create thumbnail
		data, err := am.DownloadAsset(ctx, asset)
		if err != nil {
			return utils.NewError(
				err,
				"Failed to download asset for thumbnail generation",
			)
		}

		// generate blur hash if asset is being uploaded asynchronously
		if isImage {
			blurHashStr, err := am.blurHash.BlurImage(data)
			if err != nil {
				return utils.NewError(
					err,
					"Failed to generate blur hash string",
				)
			}

			asset.BlurHash = utils.String(blurHashStr)
		}

		err = am.GenerateAndUploadThumbnail(ctx, asset, data)
		if err != nil {
			return utils.NewError(
				err,
				"Failed to generate asset thumbnail",
			)
		}

		asset.ThumbnailStatus = entities.AssetStatusActive
	} else {
		asset.ThumbnailFailedAt = utils.Time(time.Now())
		asset.ThumbnailStatus = entities.AssetStatusFailed
	}

	return nil
}

func (am *AppAssetManager) UploadAsset(
	ctx context.Context,
	asset *entities.Asset,
	b64Data string,
) error {

	data, err := base64.StdEncoding.DecodeString(b64Data)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to decode base64 image data for asset=[%v]",
			asset.Key,
		)
	}

	err = am.assetStore.Upload(ctx, data, asset.ContentType, asset.Key, true)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to upload asset=[%v]",
			asset.Key,
		)
	}

	asset.Status = entities.AssetStatusActive
	asset.UploadedAt = utils.Time(time.Now())

	err = am.GenerateAndUploadThumbnail(ctx, asset, data)
	if err != nil {
		logger.Errorf("Failed to generate and upload thumbnail for asset=[%v] because=[%v]", asset.Key, err)
		asset.ThumbnailStatus = entities.AssetStatusFailed
	}

	return nil
}
