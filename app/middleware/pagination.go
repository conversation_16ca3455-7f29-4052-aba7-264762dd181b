package middleware

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v3"
)

const layout = "2006-01-02"

func PaginationFilterFromContext(ctx *gin.Context) (*entities.PaginationFilter, error) {

	filter := &entities.PaginationFilter{}
	var err error

	page := 1
	pageQuery := strings.TrimSpace(ctx.Query("page"))
	if pageQuery != "" {
		page, err = strconv.Atoi(pageQuery)
		if err != nil {
			return filter, err
		}

		if page < 1 {
			page = 1
		}
	}

	per := 20
	perQuery := strings.TrimSpace(ctx.Query("per"))
	if perQuery != "" {
		per, err = strconv.Atoi(perQuery)
		if err != nil {
			return filter, err
		}
	}

	offset := 0
	if page > 1 {
		offset = (page - 1) * per
	}

	search := ""
	searchQuery := strings.TrimSpace(ctx.Query("search"))
	if len(searchQuery) > 0 {
		search = searchQuery
	}

	var startDate null.Time
	startDateQuery := strings.TrimSpace(ctx.Query("start_date"))
	if startDateQuery != "" {
		startDateTime, err := convertStringDateToTime(startDateQuery)
		if err != nil {
			return filter, utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse startDate=[%v] for pagination filter",
				startDateQuery,
			)
		}
		startDate = null.TimeFrom(startDateTime)
	}

	var endDate null.Time
	endDateQuery := strings.TrimSpace(ctx.Query("end_date"))
	if endDateQuery != "" {
		endDateTime, err := convertStringDateToTime(endDateQuery)
		if err != nil {
			return filter, utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse endDate=[%v] for pagination filter",
				endDateQuery,
			)
		}
		endDate = null.TimeFrom(endDateTime)
	}

	documentType := "invoice"
	documentTypeQuery := strings.TrimSpace(ctx.Query("document_type"))
	if len(documentTypeQuery) > 0 {
		documentType = documentTypeQuery
	}

	var isEmailSent null.Bool
	isEmailSentQuery := strings.TrimSpace(ctx.Query("is_email_sent"))
	if isEmailSentQuery != "" {

		if strings.ToLower(isEmailSentQuery) == "true" {
			isEmailSent = null.BoolFrom(true)

		} else if strings.ToLower(isEmailSentQuery) == "false" {
			isEmailSent = null.BoolFrom(false)
		}
	}

	var isZReported null.Bool
	isZReportedQuery := strings.TrimSpace(ctx.Query("is_z_reported"))
	if isZReportedQuery != "" {

		if strings.ToLower(isZReportedQuery) == "true" {
			isZReported = null.BoolFrom(true)

		} else if strings.ToLower(isZReportedQuery) == "false" {
			isZReported = null.BoolFrom(false)
		}
	}

	storeIdStr := ctx.Request.Header.Get("x-store-id")
	storeId := utils.ConvertStringToInt64(storeIdStr)
	if storeId > 0 {
		filter.StoreID = storeId
	}

	filter.IsEmailSent = isEmailSent
	filter.IsZReported = isZReported
	filter.Limit = per
	filter.Offset = offset
	filter.Page = page
	filter.Per = per
	filter.Search = search
	filter.StartDate = startDate
	filter.DocumentType = documentType
	filter.EndDate = endDate

	return filter, nil
}

func convertStringDateToTime(dateString string) (time.Time, error) {
	return time.ParseInLocation(layout, dateString, utils.LocKenya)
}
