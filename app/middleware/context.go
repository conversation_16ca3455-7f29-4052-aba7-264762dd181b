package middleware

import (
	"strings"

	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/web/auth"
	"compliance-and-risk-management-backend/app/web/ctxhelper"

	"github.com/gin-gonic/gin"
)

// Legacy header. Will remove later.
const applicationTypeHeaderKey = "x-compliance-application"
const clientIdentifierHeaderKey = "x-client-identifier"
const clientVersionHeaderKey = "x-client-version"
const strutsApiKey = "x-hash-api-key"
const userAgentHeaderKey = "user-agent"
const xForwardedFor = "x-forwarded-for"
const xEnvoyExternalAddress = "x-envoy-external-address"

func SetupAppContext(sessionAuthenticator auth.SessionAuthenticator) gin.HandlerFunc {
	return func(c *gin.Context) {

		apiKey := c.Request.Header.Get(strutsApiKey)
		ctx := ctxhelper.WithAPIKey(c.Request.Context(), apiKey)

		applicationType := c.Request.Header.Get(applicationTypeHeaderKey)
		ctx = ctxhelper.WithApplicationType(ctx, applicationType)

		clientIdentifier := c.Request.Header.Get(clientIdentifierHeaderKey)
		ctx = ctxhelper.WithClientIdentifier(ctx, clientIdentifier)

		clientVersion := c.Request.Header.Get(clientVersionHeaderKey)
		ctx = ctxhelper.WithClientVersion(ctx, clientVersion)

		ipAddress := c.Request.Header.Get(xEnvoyExternalAddress)
		if ipAddress == "" {
			ipsList := strings.Split(c.Request.Header.Get(xForwardedFor), ",")
			ipAddress = strings.TrimSpace(ipsList[0])
		}
		ctx = ctxhelper.WithIpAddress(ctx, ipAddress)

		userAgent := c.Request.Header.Get(userAgentHeaderKey)
		ctx = ctxhelper.WithUserAgent(ctx, userAgent)

		tokenInfo, err := sessionAuthenticator.TokenInfoFromRequest(c.Request)
		if err != nil {
			if !strings.Contains(err.Error(), "token contains an invalid number of segments") {
				logger.Warnf("unable to parse token info from request: %v", err)
			}
		} else {
			ctx = ctxhelper.WithTokenInfo(ctx, tokenInfo)
		}

		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}
