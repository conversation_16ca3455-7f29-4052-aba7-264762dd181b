package middleware

import (
	"errors"
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/auth"
	"compliance-and-risk-management-backend/app/web/ctxhelper"

	"github.com/gin-gonic/gin"
)

func AllowOnlyActiveUser(
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) func(ctx *gin.Context) {

	return validateSessionApplicationType(transactionsDB, sessionAuthenticator, sessionService, nil, false)
}

func AllowOnlyAdmin(
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) func(c *gin.Context) {

	adminApplicationType := []entities.ApplicationType{entities.ApplicationTypeAdmin}
	return validateSessionApplicationType(transactionsDB, sessionAuthenticator, sessionService, adminApplicationType, false)
}

func AllowOnlyApiKeyUser(
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
) func(ctx *gin.Context) {

	return func(c *gin.Context) {

		ctx := c.Request.Context()

		// Get User by apiKey: x-hash-api-key
		authKey := c.Request.Header.Get(strutsApiKey)

		apiKey, err := apiKeyRepository.FindByKey(ctx, authKey)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeLoginRequired,
				"Failed to validate session, login required",
			)

			c.JSON(appError.HttpStatus(), appError.JsonResponse())
			c.Abort()
			return
		}

		err = apiKeyRepository.UpdateLastUsedAt(ctx, apiKey)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeLoginRequired,
				"Failed to update api key last used at",
			)

			c.JSON(appError.HttpStatus(), appError.JsonResponse())
			c.Abort()
			return
		}

		tokenInfo := &entities.TokenInfo{
			APIKeyID:        apiKey.ID,
			APIKey:          authKey,
			ApplicationType: entities.ApplicationTypeUser,
			Status:          "active",
			StoreID:         *apiKey.StoreID,
			UserID:          apiKey.UserID,
		}

		ctx = ctxhelper.WithTokenInfo(ctx, tokenInfo)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

func containsApplicationType(applicationTypes []entities.ApplicationType, requestApplicationType entities.ApplicationType) bool {
	for _, applicationType := range applicationTypes {
		if applicationType == requestApplicationType {
			return true
		}
	}
	return false
}

func validateSessionApplicationType(
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
	applicationTypes []entities.ApplicationType,
	validateAnonymousSessions bool,
) func(c *gin.Context) {

	return func(c *gin.Context) {

		ctx := c.Request.Context()
		tokenInfo := ctxhelper.TokenInfo(ctx)

		err := validateSession(c, transactionsDB, sessionAuthenticator, sessionService)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeLoginRequired,
				"Failed to validate session, login required",
			)

			c.JSON(appError.HttpStatus(), appError.JsonResponse())
			c.Abort()
			return
		}

		if applicationTypes != nil && !containsApplicationType(applicationTypes, tokenInfo.ApplicationType) {
			appError := utils.NewError(
				errors.New("application type not allowed"),
				"Failed to validate applicationType",
			)
			appError.AddErrorMessage("Failed to validate application type")

			c.JSON(appError.HttpStatus(), appError.JsonResponse())
			c.Abort()
			return
		}

		if tokenInfo.RequiresRefresh() {
			_, err := sessionAuthenticator.RefreshTokenFromRequest(ctx, transactionsDB, tokenInfo, c.Writer)
			if err != nil {
				appError := utils.NewError(
					err,
					"Failed to refresh token from request",
				)
				appError.AddErrorMessage("Failed to refresh token from request")

				c.JSON(appError.HttpStatus(), appError.JsonResponse())
				c.Abort()
				return
			}
		}
	}
}

func validateSession(
	c *gin.Context,
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) error {

	ctx := c.Request.Context()
	tokenInfo := ctxhelper.TokenInfo(ctx)

	session, err := sessionService.FullSession(ctx, transactionsDB, tokenInfo.SessionID)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to get user session info for sessionID=[%v]",
			tokenInfo.SessionID,
		)
	}

	if session.UserID != tokenInfo.UserID {
		return utils.NewErrorWithCode(
			errors.New("invalid user id"),
			utils.ErrorCodeRoleForbidden,
			"Failed to check user role for sessionID=[%v] with userID=[%v], and user=[%v]",
			tokenInfo.SessionID,
			session.UserID,
			tokenInfo.UserID,
		)
	}

	// if !session.UserStatus.IsActive() {
	// 	return utils.NewErrorWithCode(
	// 		errors.New("status is not active"),
	// 		utils.ErrorCodeInvalidUserStatus,
	// 		"Failed to check for active user=[%v]",
	// 		tokenInfo.UserID,
	// 	)
	// }

	if !session.DeactivatedAt.IsZero() {
		return utils.NewErrorWithCode(
			errors.New("session is not active"),
			utils.ErrorCodeSessionExpired,
			"Failed to check for active user for sessionID=[%v]",
			tokenInfo.SessionID,
		)
	}

	return nil
}
