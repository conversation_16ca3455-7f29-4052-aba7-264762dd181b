package models

import "time"

// InvoiceReport struct
type InvoiceReport struct {
	InvoiceDate   string  `json:"invoice_date"`
	InvoiceNumber string  `json:"invoice_number"`
	GrossAmount   float64 `json:"gross_amount"`
	Vat           float64 `json:"vat"`
	GrandTotal    float64 `json:"grand_total"`
	Signature     string  `json:"signature"`
}

// InvoiceSignature struct
type InvoiceSignature struct {
	InvoiceNumber string `json:"invoice_number"`
	Signature     string `json:"signature"`
}

type ReportsFilter struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	Status    string    `json:"status"`
	Query     string    `json:"query"`
}

// Result - db result
type Result struct {
	Total float64 `json:"total"`
}

// JSONResponse : Success response
// swagger:response jsonResponse
type JSONResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// BadRequestResponse 400 - bad request - invalid details / wrong data type in request.
// swagger:response badRequestResponse
type BadRequestResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// ForbiddenResponse 403 - you are forbidden from accessing this resource.
// swagger:response forbidddenResponse
type ForbiddenResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// NotFoundResponse 404 - resource not found.
// swagger:response notFoundResponse
type NotFoundResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// UsersRepsApiReport struct
type UsersRepsApiReport struct {
	UsersCount int64  `json:"users_count"`
	Limit      int64  `json:"limit"`
	Offset     int64  `json:"offset"`
	NumPages   int64  `json:"num_pages"`
	Search     string `json:"search"`
	Users      []User `json:"users"`
}

// InvoicesRepsApiReport struct
type InvoicesRepsApiReport struct {
	InvoicesCount int64     `json:"invoices_count"`
	Limit         int64     `json:"limit"`
	Offset        int64     `json:"offset"`
	NumPages      int64     `json:"num_pages"`
	Search        string    `json:"search"`
	Invoices      []Invoice `json:"invoices"`
}

// SummaryReport struct
type SummaryReport struct {
	AllInvoicesCount              int64   `json:"all_invoices_count"`
	AllInvoicesVatSum             float64 `json:"all_invoices_vat_sum"`
	AllInvoicesTotalSum           float64 `json:"all_invoices_total_sum"`
	InvoicesThatFailedToSignCount int64   `json:"invoices_failed_to_sign_count"`
}

type DashboardInvoiceChartReport struct {
	Date         string `json:"date"`
	RecordsCount int64  `json:"records_count"`
}
