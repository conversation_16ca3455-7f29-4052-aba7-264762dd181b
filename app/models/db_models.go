package models

import (
	"time"

	"github.com/jinzhu/gorm"
	// for postgres connectivity
	_ "github.com/jinzhu/gorm/dialects/postgres"
)

type User struct {
	ID             int       `json:"id"`
	DateCreated    time.Time `json:"date_created"`
	DateUpdated    time.Time `json:"date_updated"`
	Apikey         string    `json:"apikey"`
	CreationTime   time.Time `json:"creation_time"`
	Email          string    `json:"email"`
	ExpiryTime     string    `json:"expiry_time"`
	Firstname      string    `json:"firstname"`
	InTrash        string    `json:"in_trash"`
	Lastname       string    `json:"lastname"`
	Msisdn         string    `json:"msisdn"`
	Name           string    `json:"name"`
	Password       string    `json:"password"`
	Phone          string    `json:"phone"`
	Status         string    `json:"status"`
	Token          string    `json:"token"`
	Username       string    `json:"username"`
	ValidationCode string    `json:"validation_code"`
}

type Invoice struct {
	ID                   int                   `json:"id"`
	CustomerEmail        string                `json:"customer_email"`
	CustomerTIN          string                `json:"customer_tin"`
	CustomerName         string                `json:"customer_name"`
	CustomerNumber       string                `json:"customer_number"`
	CustomerPhone        string                `json:"customer_phone"`
	DateCreated          time.Time             `json:"date_created"`
	FileName             string                `json:"file_name"`
	InvoiceDate          string                `json:"invoice_date"`
	InvoiceNumber        string                `json:"invoice_number"`
	IsEmailSent          bool                  `json:"is_email_sent"`
	Vat                  string                `json:"vat"`
	GrandTotal           string                `json:"grand_total"`
	GrossAmount          string                `json:"gross_amount"`
	Signature            string                `json:"signature"`
	OriginalFileName     string                `json:"original_file_name"`
	LastLine             string                `json:"last_line"`
	SignaturePositioning *SignaturePositioning `json:"-"`
	VerificationURL      string                `json:"verification_url"`
}

type Activity struct {
	ID           int       `json:"id"`
	UserID       int       `json:"user_id"`
	ActivityType string    `json:"activity_type"`
	Description  string    `json:"description"`
	SourceIP     string    `json:"source_ip"`
	ResourceID   int       `json:"resource_id"`
	CreatedOn    string    `json:"created_on"`
	DateCreated  time.Time `json:"date_created"`
}

type SysMsg struct {
	ID               int       `json:"id"`
	DateCreated      time.Time `json:"date_created"`
	MsgType          string    `json:"msg_type"` // EMAIL or SMS
	Recipient        string    `json:"recipient"`
	Subject          string    `json:"subject"`
	Message          string    `json:"message"`
	NotificationType string    `json:"notification_type"`
	Status           int       `json:"status"` // 0 - New, 1 - Processing, 2 - Sent, 3 - Failed
	SgMemberID       int       `json:"sg_member_id"`
	DateSent         time.Time `json:"date_sent"`
	TrxID            string    `json:"trx_id"`
}

type Notification struct {
	ID                   int         `json:"id"`
	Date                 time.Time   `json:"date"`
	NotificationType     string      `json:"notification_type"`
	NotificationTypeID   int         `json:"notification_type_id"`   // Description below this struct
	Status               int         `json:"status"`                 //  # 0 - New, 1 - Rejected, 2- Accepted
	InvitationRecieverID int         `json:"invitation_reciever_id"` // user id
	RecieverID           int         `json:"reciever_id"`            // SG Member ID
	SenderID             int         `json:"sender_id"`
	NotificationData     interface{} `json:"notification_data"`
}

// DBMigrate will create and migrate the tables, and then make the some relationships if necessary
func DBMigrate(db *gorm.DB) *gorm.DB {
	db.AutoMigrate(&User{}, &Invoice{}, &Activity{})
	// db.models(&User{}).AddForeignKey("org_id", "organizations(id)", "CASCADE", "CASCADE")
	return db
}

// http://doc.gorm.io/models.html#conventions
