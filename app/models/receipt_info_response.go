package models

import "encoding/xml"

type ReceiptInfoResponse struct {
	XMLName        xml.Name `xml:"EFDMS"`
	RCTACK         RCTACK   `xml:"RC<PERSON>CK" json:"-"`
	<PERSON>FDM<PERSON><PERSON>NATURE string   `xml:"EFDM<PERSON><PERSON>NATURE" json:"EFDM<PERSON><PERSON><PERSON>TURE"`
}

type RC<PERSON>CK struct {
	XMLName xml.Name `xml:"RC<PERSON>CK"`
	RCTNUM  string   `xml:"RCTNUM" json:"RCTNUM"`
	DATE    string   `xml:"DATE" json:"DATE"`
	TIME    string   `xml:"TIME" json:"TIME"`
	ACKCODE string   `xml:"ACKCODE" json:"AC<PERSON>CODE"`
	ACKMSG  string   `xml:"ACKMSG" json:"ACKMSG"`
}
