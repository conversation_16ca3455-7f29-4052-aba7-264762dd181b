package strutssurveys

import (
	"database/sql/driver"
)

type UserStatus string

const (
	UserStatusActive     UserStatus = "active"
	UserStatusInactive   UserStatus = "inactive"
	UserStatusUnverified UserStatus = "unverified"
)

// <PERSON>an implements the Scanner interface.
func (s *UserStatus) Scan(value interface{}) error {
	*s = UserStatus(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s UserStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s UserStatus) String() string {
	return string(s)
}

func (s UserStatus) IsValid() bool {
	return s == UserStatusActive ||
		s == UserStatusInactive ||
		s == UserStatusUnverified
}

func (s UserStatus) IsActive() bool {
	return s == UserStatusActive
}
