package strutssurveys

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type (
	SequentialIdentifier struct {
		ID int64 `db:"id" json:"id"`
	}

	Timestamps struct {
		CreatedAt time.Time `db:"created_at" json:"created_at"`
		UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
	}

	User struct {
		SequentialIdentifier
		AuthenticationToken string       `json:"_"`
		Email               string       `json:"email"`
		FirstName           string       `json:"first_name"`
		LastName            string       `json:"last_name"`
		OrganizationID      int64        `json:"organization_id"`
		PaymentPackageID    int64        `json:"payment_package_id"`
		PhoneNumberID       int64        `json:"phone_number_id"`
		PhoneNumber         *PhoneNumber `json:"phone_number"`
		Username            string       `json:"username"`
		Password            string       `json:"-"`
		Status              UserStatus   `json:"status"`
		NationalID          string       `json:"national_id"`
		DeletedAt           null.Time    `db:"deleted_at" json:"-"`
		Timestamps
	}

	PhoneNumber struct {
		SequentialIdentifier
		CountryCode string `db:"country_code" json:"country_code"`
		Number      string `db:"number" json:"number"`
		Timestamps
	}

	InvoiceSigningAnalyticsResponse struct {
		IsLicenseValid bool `json:"is_license_valid"`
	}
)
