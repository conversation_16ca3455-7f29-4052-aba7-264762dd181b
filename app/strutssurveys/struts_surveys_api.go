package strutssurveys

import (
	"bytes"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"

	txns_db "compliance-and-risk-management-backend/app/database"
)

const (
	API_BASE_URL   string = "https://api.strutssurveys.com/api"
	ENCRYPTION_KEY string = "EuXqfey1AeDwoa4224dfjakksimonVFD21yW3ejQHOqb5Da8jKwdV6BiWpo9uBgiujBCL9p2F01H2021O6zke4zBN5"
)

func strutsAuthenticate() (*User, error) {

	user := &User{}
	url := API_BASE_URL + "/sessions"
	method := "POST"

	loginPayload := &LoginPayload{
		Email:    "<EMAIL>",
		Password: "bz!e*=$pMy8SQ2LC",
	}

	payloadBuf := new(bytes.Buffer)
	json.NewEncoder(payloadBuf).Encode(loginPayload)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payloadBuf)

	if err != nil {
		fmt.Println(err)
		return user, err
	}

	req.Header.Add("x-compliance-application", "user")
	req.Header.Add("x-client-identifier", "web")
	req.Header.Add("x-forwarded-for", "localhost")
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return user, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return user, err
	}

	if err := json.Unmarshal(body, user); err != nil {
		utils.Log.Infof("Can not unmarshal JSON, err=[%v]\n", err)
	}

	authToken := res.Header.Get("X-Surveys-Token")
	user.AuthenticationToken = authToken

	return user, nil
}

func SendAnalyticalData(
	operations txns_db.TransactionsSQLOperations,
	invoiceRepository repos.InvoiceRepository,
) {

	user, err := strutsAuthenticate()
	if err != nil {
		utils.Log.Infof("ssa auth err=[%v]\n", err)
	}

	ctx := context.Background()

	summariesReportSummary, err := invoiceRepository.GetZReportTotals(ctx, operations, false)
	if err != nil {
		utils.Log.Infof("z-report summary err=[%v]\n", err)
	}

	encryptedData := encryptAnalyticalData(summariesReportSummary)

	form := &InvoiceSigningAnalyticsForm{
		License: os.Getenv("LICENSE"),
		RawText: encryptedData,
		UserID:  user.ID,
	}

	url := API_BASE_URL + "/analytics"
	method := "POST"

	payloadBuf := new(bytes.Buffer)
	json.NewEncoder(payloadBuf).Encode(form)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payloadBuf)

	if err != nil {
		fmt.Println(err)
		return
	}

	req.Header.Add("x-compliance-application", "user")
	req.Header.Add("x-client-identifier", "web")
	req.Header.Add("x-surveys-token", user.AuthenticationToken)
	req.Header.Add("x-forwarded-for", "localhost")
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return
	}

	response := &InvoiceSigningAnalyticsResponse{}
	if err := json.Unmarshal(body, response); err != nil {
		utils.Log.Infof("Can not unmarshal JSON, err=[%v]\n", err)
	}

	utils.Log.Infof("ilv=[%v], uid=[%v]\n", response.IsLicenseValid, user.ID)
}

func encryptAnalyticalData(zReportTotals entities.ZReportTotals) string {
	strData := ""
	strData += fmt.Sprintf("%v|", zReportTotals.Count)
	strData += fmt.Sprintf("%v|", zReportTotals.NetAmount)
	strData += fmt.Sprintf("%v|", zReportTotals.VatAmount)
	strData += fmt.Sprintf("%v|", zReportTotals.PmtAmount)
	strData += fmt.Sprintf("%v|", zReportTotals.TotalAmount)
	strData += fmt.Sprintf("%v|", 2) // Amounts precision

	// utils.Log.Infof("strData=[%v]\n", strData)

	encryptedData, err := utils.EncryptString(strData, ENCRYPTION_KEY)
	if err != nil {
		utils.Log.Infof("ssa enc err=[%v]\n", err)
	}

	// utils.Log.Infof("encryptedData=[%v]\n", encryptedData)

	return encryptedData
}
