package ctxhelper

import (
	"context"

	"compliance-and-risk-management-backend/app/entities"

	"github.com/getsentry/sentry-go"
)

func NewContextWithInfo(ctx context.Context) context.Context {
	newCtx := context.Background()

	newCtx = context.WithValue(newCtx, entities.ContextKeyTokenInfo, TokenInfo(ctx))

	hub := sentry.GetHubFromContext(ctx)
	if hub != nil {
		newCtx = sentry.SetHubOnContext(newCtx, hub)
	}

	newCtx = context.WithValue(newCtx, entities.ContextKeyClientIdentifier, ClientIdentifier(ctx))

	newCtx = context.WithValue(newCtx, entities.ContextKeyApplicationType, ApplicationType(ctx))

	newCtx = context.WithValue(newCtx, entities.ContextKeyClientVersion, ClientVersion(ctx))

	newCtx = context.WithValue(newCtx, entities.ContextKeyIP<PERSON><PERSON><PERSON>, IPAddress(ctx))

	newCtx = context.WithValue(newCtx, entities.ContextKeyRequestId, RequestId(ctx))

	return newCtx
}
