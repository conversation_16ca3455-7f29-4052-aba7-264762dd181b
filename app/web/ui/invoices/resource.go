package invoices

import (
	"context"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/globals"
	"strconv"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func filterInvoices(
	appRouter *gin.Engine,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(context.Background(), transactionsDB)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		filter := &entities.PaginationFilter{}
		invoiceList, err := invoiceService.FilterInvoices(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter invoices",
			})
			return
		}

		appRouter.Delims("{[{", "}]}")
		appRouter.SetFuncMap(template.FuncMap{
			"searchInvoices": searchInvoices,
			"formatAsDate":   formatAsDate,
		})

		ctx.HTML(http.StatusOK, "invoices.html", gin.H{
			"content":            "Invoices",
			"user":               user,
			"dashboardAnalytics": dashboardAnalytics,
			"invoiceList":        invoiceList,
			"now":                time.Date(2017, 07, 01, 0, 0, 0, 0, time.UTC),
		})

	}
}

func getInvoice(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	invoiceService services.InvoiceService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)

		invoiceIDStr := ctx.Param("id")
		invoiceID, err := strconv.ParseInt(invoiceIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse invoiceID=[%v], err=[%v]\n", invoiceIDStr, err)
		}

		invoice, err := invoiceService.FindInvoiceByID(context.Background(), invoiceID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter invoices",
			})
			return
		}

		pdfURL := fmt.Sprintf("%v/api/invoices/%v/pdf", os.Getenv("BASE_URL"), invoice.ID)

		ctx.HTML(http.StatusOK, "invoice.html", gin.H{
			"content": "Invoice #" + invoice.InvoiceNumber,
			"user":    user,
			"invoice": invoice,
			"pdfURL":  pdfURL,
		})

	}
}

func searchInvoices() {
	log.Printf("searching invoices...\n")
}

func formatAsDate(t time.Time) string {
	year, month, day := t.Date()
	return fmt.Sprintf("%d/%02d/%02d", year, month, day)
}
