package login

import (
	"compliance-and-risk-management-backend/app/database"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/", homePage(transactionsDB, userService))
		unauthenticatedAPI.GET("/login", loginPage(transactionsDB, userService))
		unauthenticatedAPI.POST("/login", loginPagePostHandler(transactionsDB, userService))
	}
}
