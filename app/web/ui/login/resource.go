package login

import (
	"context"
	"log"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/services"
	"net/http"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	globals "compliance-and-risk-management-backend/globals"
	helpers "compliance-and-risk-management-backend/helpers"
)

func homePage(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)
		ctx.HTML(http.StatusOK, "index.html", gin.H{
			"content": "",
			"user":    user,
		})
	}
}

func loginPage(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)
		if user != nil {
			ctx.HTML(http.StatusBadRequest, "login.html", gin.H{"content": "Please logout first"})
			return
		}

		username := ctx.PostForm("username")
		password := ctx.PostForm("password")

		if helpers.EmptyUserPass(username, password) {
			ctx.HTML(http.StatusBadRequest, "login.html", gin.H{"content_info": "Welcome, login to proceed."})
			return
		}

		session.Set(globals.Userkey, username)
		if err := session.Save(); err != nil {
			ctx.HTML(http.StatusInternalServerError, "login.html", gin.H{"content": "Failed to save session"})
			return
		}

		ctx.Redirect(http.StatusMovedPermanently, "/dashboard")
	}
}

func loginPagePostHandler(
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		// user := session.Get(globals.Userkey)
		// if user != nil {
		// 	ctx.HTML(http.StatusBadRequest, "login.html", gin.H{"content": "Please logout first"})
		// 	return
		// }

		username := ctx.PostForm("username")
		password := ctx.PostForm("password")

		if helpers.EmptyUserPass(username, password) {
			ctx.HTML(http.StatusBadRequest, "login.html", gin.H{"content_warn": "Username and password required!"})
			return
		}

		loginForm := &forms.UserLoginForm{
			Email:    username,
			Password: password,
		}

		appUser, token, err := userService.LoginUser(context.Background(), transactionsDB, loginForm)
		if err != nil || appUser.ID == 0 || token == nil {
			ctx.HTML(http.StatusUnauthorized, "login.html", gin.H{"content_warn": "Incorrect username or password"})
			return
		}

		session.Set(globals.Userkey, username)
		session.Set(globals.AppUser, appUser)
		session.Set(globals.UserID, appUser.ID)

		if err := session.Save(); err != nil {
			log.Printf("failed to save session, err=[%v]\n", err)
			ctx.HTML(http.StatusInternalServerError, "login.html", gin.H{"content_warn": "Failed to save session"})
			return
		}

		ctx.Redirect(http.StatusMovedPermanently, "/dashboard")
	}
}
