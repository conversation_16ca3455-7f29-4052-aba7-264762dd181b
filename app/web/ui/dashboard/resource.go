package dashboard

import (
	"context"
	"html/template"
	"math/rand"
	"net/http"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/globals"
	"compliance-and-risk-management-backend/helpers"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/go-echarts/go-echarts/v2/charts"
	"github.com/go-echarts/go-echarts/v2/opts"
)

const (
	ModChart = "chart"
	ModPage  = "page"
)

func getDashboardAnalytics(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(context.Background(), transactionsDB)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		// page := components.NewPage()
		// create a new bar instance
		bar := charts.NewBar()
		// set some global options like Title/Legend/ToolTip or anything else
		bar.SetGlobalOptions(charts.WithTitleOpts(opts.Title{
			Title:    "Invoice signing over time",
			Subtitle: "Invoice processing over time",
		}))

		// Put data into instance
		bar.SetXAxis([]string{"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"}).
			AddSeries("Category A", generateBarItems()).
			AddSeries("Category B", generateBarItems())

		bar.Renderer = helpers.NewSnippetRenderer(bar, bar.Validate)
		var htmlSnippet template.HTML = helpers.RenderToHtml(bar)

		ctx.HTML(http.StatusOK, "dashboard.html", gin.H{
			"content":            "Dashboard",
			"user":               user,
			"dashboardAnalytics": dashboardAnalytics,
			"bar":                htmlSnippet,
		})
	}
}

func generateBarItems() []opts.BarData {
	items := make([]opts.BarData, 0)
	for i := 0; i < 7; i++ {
		items = append(items, opts.BarData{Value: rand.Intn(300)})
	}
	return items
}
