package sales

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func makeSale(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var saleForm forms.CreateSaleForm
		err := ctx.Bind(&saleForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind sale form while creating sale",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		sale, err := saleService.CreateSale(ctx.Request.Context(), transactionsDB, &saleForm, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to create sale. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, sale)
	}
}

func downloadSalesExcel(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering sales",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Sales-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = saleService.DownloadSales(context.Background(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download sales",
			})
			return
		}

		// ctx.JSON(http.StatusOK, saleList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterSales(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering sales",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		saleList, err := saleService.FilterSales(ctx.Request.Context(), transactionsDB, filter, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter sales ",
			})
			return
		}

		ctx.JSON(http.StatusOK, saleList)
	}
}

func filterSalesForStore(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering sales",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		filter.StoreID = storeId

		saleList, err := saleService.FilterSalesForStore(ctx.Request.Context(), transactionsDB, storeId, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter sales for store",
			})
			return
		}

		ctx.JSON(http.StatusOK, saleList)
	}
}

func getSale(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		saleIDStr := ctx.Param("id")
		saleID, err := strconv.ParseInt(saleIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse saleID=[%v], err=[%v]\n", saleIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		sale, err := saleService.FindSaleByID(ctx.Request.Context(), transactionsDB, saleID, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to get sale: " + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, sale)
	}
}

func updateSale(
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		saleIDStr := ctx.Param("id")
		saleID, err := strconv.ParseInt(saleIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse saleID=[%v], err=[%v]\n", saleIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		var form forms.UpdateSaleForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind sale form while updating sale",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		sale, err := saleService.UpdateSale(ctx.Request.Context(), transactionsDB, saleID, &form, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update sale",
			})
			return
		}

		ctx.JSON(http.StatusOK, sale)
	}
}
