package analytics

import (
	"net/http"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func getDashboardAnalytics(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	storeRepository repos.StoreRepository,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(ctx.Request.Context(), transactionsDB)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		ctx.JSON(http.StatusOK, dashboardAnalytics)
		return
	}
}
