package user_expenses

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createUserExpense(
	transactionsDB *database.AppTransactionsDB,
	userExpenseService services.UserExpenseService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var userExpenseForm forms.CreateUserExpenseForm
		err := ctx.Bind(&userExpenseForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind userExpense form while creating userExpense",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userExpense, err := userExpenseService.CreateUserExpense(ctx.Request.Context(), transactionsDB, &userExpenseForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save userExpense. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, userExpense)
	}
}

func filterUserExpenses(
	transactionsDB *database.AppTransactionsDB,
	userExpenseService services.UserExpenseService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering userExpenses",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userExpenseList, err := userExpenseService.FilterUserExpenses(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter userExpenses",
			})
			return
		}

		ctx.JSON(http.StatusOK, userExpenseList)
	}
}

func getUserExpense(
	userExpenseService services.UserExpenseService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userExpenseIDStr := ctx.Param("id")
		userExpenseID, err := strconv.ParseInt(userExpenseIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userExpenseID=[%v], err=[%v]\n", userExpenseIDStr, err)
		}

		userExpense, err := userExpenseService.FindUserExpenseByID(ctx.Request.Context(), userExpenseID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find userExpense by id=[%v]",
				userExpenseIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userExpense)
	}
}

func deleteUserExpense(
	userExpenseService services.UserExpenseService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userExpenseIDStr := ctx.Param("id")
		userExpenseID, err := strconv.ParseInt(userExpenseIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userExpenseID=[%v], err=[%v]\n", userExpenseIDStr, err)
		}

		userExpense, err := userExpenseService.DeleteUserExpense(ctx.Request.Context(), userExpenseID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete userExpense by id=[%v]",
				userExpenseIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userExpense)
	}
}

func updateUserExpense(
	transactionsDB *database.AppTransactionsDB,
	userExpenseService services.UserExpenseService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userExpenseIDStr := ctx.Param("id")
		userExpenseID, err := strconv.ParseInt(userExpenseIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userExpenseID=[%v], err=[%v]\n", userExpenseIDStr, err)
		}

		var form forms.UpdateUserExpenseForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind userExpense form while updating userExpense",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userExpense, err := userExpenseService.UpdateUserExpense(ctx.Request.Context(), transactionsDB, userExpenseID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update userExpense by id=[%v]",
				userExpenseIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userExpense)
	}
}
