package user_expenses

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userExpenseService services.UserExpenseService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/user_expenses", createUserExpense(transactionsDB, userExpenseService))
		protectedAPI.GET("/user_expenses", filterUserExpenses(transactionsDB, userExpenseService))
		protectedAPI.GET("/user_expenses/:id", getUserExpense(userExpenseService))
		protectedAPI.PUT("/user_expenses/:id", updateUserExpense(transactionsDB, userExpenseService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.DELETE("/user_expenses/:id", deleteUserExpense(userExpenseService))
	}
}
