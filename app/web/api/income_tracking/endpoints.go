package income_tracking

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	incomeTrackingService services.IncomeTrackingService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.POST("/income_trackings", createIncomeTracking(transactionsDB, incomeTrackingService))
		adminAPI.GET("/income_trackings", filterIncomeTrackings(transactionsDB, incomeTrackingService))
		adminAPI.GET("/income_trackings/:id", getIncomeTrackingById(incomeTrackingService))
		adminAPI.PUT("/income_trackings/:id", updateIncomeTracking(transactionsDB, incomeTrackingService))
	}
}
