package etims_stock

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createEtimsStock(
	transactionsDB *database.AppTransactionsDB,
	etimsStockService services.EtimsStockService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {
		var form forms.CreateEtimsStockItemsForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"failed to bind form while creating ETIMS stock",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		invoice, err := etimsStockService.CreateEtimsStock(ctx.Request.Context(), transactionsDB, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"failed to crete ETIMS stock SarNo=[%v]",
				form.SarNo,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, invoice)
	}
}

func filterEtimsStock(
	transactionsDB *database.AppTransactionsDB,
	etimsStockService services.EtimsStockService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"failed to parse pagination filter while filtering ETIMS Stock",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		etimsStockList, err := etimsStockService.FilterEtimsStock(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter ETIMS Stock",
			})
			return
		}

		ctx.JSON(http.StatusOK, etimsStockList)
	}
}

func getEtimsStock(
	transactionsDB *database.AppTransactionsDB,
	etimsStockService services.EtimsStockService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		stockIDStr := ctx.Param("id")
		stockID, err := strconv.ParseInt(stockIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse stockID=[%v], err=[%v]\n", stockIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		stock, err := etimsStockService.FindByID(ctx.Request.Context(), transactionsDB, stockID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find stock by id=[%v]",
				stockIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, stock)
	}
}
