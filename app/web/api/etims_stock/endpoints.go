package etims_stock

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsStockService services.EtimsStockService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/stock", createEtimsStock(transactionsDB, etimsStockService))
		protectedAPI.GET("/etims_vscu/stock", filterEtimsStock(transactionsDB, etimsStockService))
		protectedAPI.GET("/etims_vscu/stock/:id", getEtimsStock(transactionsDB, etimsStockService))
	}
}
