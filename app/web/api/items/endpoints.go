package items

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	itemService services.ItemService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/items", createItem(transactionsDB, itemService))
		protectedAPI.GET("/items", filterItems(transactionsDB, itemService))
		protectedAPI.GET("/items/:id", getItem(transactionsDB, itemService))
		protectedAPI.PUT("/items/:id", updateItem(transactionsDB, itemService))
		protectedAPI.GET("/items/download", downloadItemsExcel(transactionsDB, itemService))
	}
}
