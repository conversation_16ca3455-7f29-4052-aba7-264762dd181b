package user_incomes

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userIncomeService services.UserIncomeService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/user_incomes", createUserIncome(transactionsDB, userIncomeService))
		protectedAPI.GET("/user_incomes", filterUserIncomes(transactionsDB, userIncomeService))
		protectedAPI.GET("/user_incomes/:id", getUserIncome(userIncomeService))
		protectedAPI.PUT("/user_incomes/:id", updateUserIncome(transactionsDB, userIncomeService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.DELETE("/user_incomes/:id", deleteUserIncome(userIncomeService))
	}
}
