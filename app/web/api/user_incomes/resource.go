package user_incomes

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createUserIncome(
	transactionsDB *database.AppTransactionsDB,
	userIncomeService services.UserIncomeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var userIncomeForm forms.CreateUserIncomeForm
		err := ctx.Bind(&userIncomeForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind userIncome form while creating userIncome",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userIncome, err := userIncomeService.CreateUserIncome(ctx.Request.Context(), transactionsDB, &userIncomeForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save userIncome. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, userIncome)
	}
}

func filterUserIncomes(
	transactionsDB *database.AppTransactionsDB,
	userIncomeService services.UserIncomeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering userIncomes",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userIncomeList, err := userIncomeService.FilterUserIncomes(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter userIncomes",
			})
			return
		}

		ctx.JSON(http.StatusOK, userIncomeList)
	}
}

func getUserIncome(
	userIncomeService services.UserIncomeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userIncomeIDStr := ctx.Param("id")
		userIncomeID, err := strconv.ParseInt(userIncomeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userIncomeID=[%v], err=[%v]\n", userIncomeIDStr, err)
		}

		userIncome, err := userIncomeService.FindUserIncomeByID(ctx.Request.Context(), userIncomeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find userIncome by id=[%v]",
				userIncomeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userIncome)
	}
}

func deleteUserIncome(
	userIncomeService services.UserIncomeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userIncomeIDStr := ctx.Param("id")
		userIncomeID, err := strconv.ParseInt(userIncomeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userIncomeID=[%v], err=[%v]\n", userIncomeIDStr, err)
		}

		userIncome, err := userIncomeService.DeleteUserIncome(ctx.Request.Context(), userIncomeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete userIncome by id=[%v]",
				userIncomeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userIncome)
	}
}

func updateUserIncome(
	transactionsDB *database.AppTransactionsDB,
	userIncomeService services.UserIncomeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		userIncomeIDStr := ctx.Param("id")
		userIncomeID, err := strconv.ParseInt(userIncomeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse userIncomeID=[%v], err=[%v]\n", userIncomeIDStr, err)
		}

		var form forms.UpdateUserIncomeForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind userIncome form while updating userIncome",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userIncome, err := userIncomeService.UpdateUserIncome(ctx.Request.Context(), transactionsDB, userIncomeID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update userIncome by id=[%v]",
				userIncomeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, userIncome)
	}
}
