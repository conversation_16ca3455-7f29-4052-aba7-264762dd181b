package expense_types

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createExpenseType(
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var expenseTypeForm forms.CreateExpenseTypeForm
		err := ctx.Bind(&expenseTypeForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind expenseType form while creating expenseType",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		expenseType, err := expenseTypeService.CreateExpenseType(ctx.Request.Context(), transactionsDB, &expenseTypeForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save expenseType. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, expenseType)
	}
}

func filterExpenseTypes(
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering expenseTypes",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		expenseTypeList, err := expenseTypeService.FilterExpenseTypes(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter expenseTypes",
			})
			return
		}

		ctx.JSON(http.StatusOK, expenseTypeList)
	}
}

func getExpenseType(
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		expenseTypeIDStr := ctx.Param("id")
		expenseTypeID, err := strconv.ParseInt(expenseTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse expenseTypeID=[%v], err=[%v]\n", expenseTypeIDStr, err)
		}

		expenseType, err := expenseTypeService.FindExpenseTypeByID(ctx.Request.Context(), expenseTypeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find expenseType by id=[%v]",
				expenseTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, expenseType)
	}
}

func deleteExpenseType(
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		expenseTypeIDStr := ctx.Param("id")
		expenseTypeID, err := strconv.ParseInt(expenseTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse expenseTypeID=[%v], err=[%v]\n", expenseTypeIDStr, err)
		}

		expenseType, err := expenseTypeService.DeleteExpenseType(ctx.Request.Context(), expenseTypeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete expenseType by id=[%v]",
				expenseTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, expenseType)
	}
}

func updateExpenseType(
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		expenseTypeIDStr := ctx.Param("id")
		expenseTypeID, err := strconv.ParseInt(expenseTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse expenseTypeID=[%v], err=[%v]\n", expenseTypeIDStr, err)
		}

		var form forms.UpdateExpenseTypeForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind expenseType form while updating expenseType",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		expenseType, err := expenseTypeService.UpdateExpenseType(ctx.Request.Context(), transactionsDB, expenseTypeID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update expenseType by id=[%v]",
				expenseTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, expenseType)
	}
}
