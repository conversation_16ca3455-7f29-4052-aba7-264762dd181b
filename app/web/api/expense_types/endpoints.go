package expense_types

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	expenseTypeService services.ExpenseTypeService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/expense_types", filterExpenseTypes(transactionsDB, expenseTypeService))
		protectedAPI.GET("/expense_types/:id", getExpenseType(transactionsDB, expenseTypeService))
		protectedAPI.PUT("/expense_types/:id", updateExpenseType(transactionsDB, expenseTypeService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/expense_types", createExpenseType(transactionsDB, expenseTypeService))
		adminAPI.DELETE("/expense_types/:id", deleteExpenseType(transactionsDB, expenseTypeService))
	}
}
