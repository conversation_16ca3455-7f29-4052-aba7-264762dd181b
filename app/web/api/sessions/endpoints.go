package sessions

import (
	"compliance-and-risk-management-backend/app/database"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.POST("/sessions", loginUser(transactionsDB, userService))
		unauthenticatedAPI.GET("/health", healthCheck)
	}

}
