package users

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userService services.UserService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/users", filterUsers(transactionsDB, userService))
		protectedAPI.GET("/users/:id", getUser(transactionsDB, userService))
		protectedAPI.PUT("/users", updateUser(transactionsDB, userService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.POST("/users", registerUser(transactionsDB, userService))
	}

}
