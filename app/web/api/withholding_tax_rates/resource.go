package withholding_tax_rates

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createWithholdingTaxRate(
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var withholdingTaxRateForm forms.CreateWithholdingTaxRateForm
		err := ctx.Bind(&withholdingTaxRateForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind withholdingTaxRate form while creating withholdingTaxRate",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		withholdingTaxRate, err := withholdingTaxRateService.CreateWithholdingTaxRate(ctx.Request.Context(), transactionsDB, &withholdingTaxRateForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save withholdingTaxRate. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, withholdingTaxRate)
	}
}

func filterWithholdingTaxRates(
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering withholdingTaxRates",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		withholdingTaxRateList, err := withholdingTaxRateService.FilterWithholdingTaxRates(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter withholdingTaxRates",
			})
			return
		}

		ctx.JSON(http.StatusOK, withholdingTaxRateList)
	}
}

func getWithholdingTaxRate(
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		withholdingTaxRateIDStr := ctx.Param("id")
		withholdingTaxRateID, err := strconv.ParseInt(withholdingTaxRateIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse withholdingTaxRateID=[%v], err=[%v]\n", withholdingTaxRateIDStr, err)
		}

		withholdingTaxRate, err := withholdingTaxRateService.FindWithholdingTaxRateByID(ctx.Request.Context(), withholdingTaxRateID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find withholdingTaxRate by id=[%v]",
				withholdingTaxRateIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, withholdingTaxRate)
	}
}

func deleteWithholdingTaxRate(
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		withholdingTaxRateIDStr := ctx.Param("id")
		withholdingTaxRateID, err := strconv.ParseInt(withholdingTaxRateIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse withholdingTaxRateID=[%v], err=[%v]\n", withholdingTaxRateIDStr, err)
		}

		withholdingTaxRate, err := withholdingTaxRateService.DeleteWithholdingTaxRate(ctx.Request.Context(), withholdingTaxRateID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete withholdingTaxRate by id=[%v]",
				withholdingTaxRateIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, withholdingTaxRate)
	}
}

func updateWithholdingTaxRate(
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		withholdingTaxRateIDStr := ctx.Param("id")
		withholdingTaxRateID, err := strconv.ParseInt(withholdingTaxRateIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse withholdingTaxRateID=[%v], err=[%v]\n", withholdingTaxRateIDStr, err)
		}

		var form forms.UpdateWithholdingTaxRateForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind withholdingTaxRate form while updating withholdingTaxRate",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		withholdingTaxRate, err := withholdingTaxRateService.UpdateWithholdingTaxRate(ctx.Request.Context(), transactionsDB, withholdingTaxRateID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update withholdingTaxRate by id=[%v]",
				withholdingTaxRateIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, withholdingTaxRate)
	}
}
