package withholding_tax_rates

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	withholdingTaxRateService services.WithholdingTaxRateService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/withholding_tax_rates", createWithholdingTaxRate(transactionsDB, withholdingTaxRateService))
		protectedAPI.GET("/withholding_tax_rates", filterWithholdingTaxRates(transactionsDB, withholdingTaxRateService))
		protectedAPI.GET("/withholding_tax_rates/:id", getWithholdingTaxRate(transactionsDB, withholdingTaxRateService))
		protectedAPI.PUT("/withholding_tax_rates/:id", updateWithholdingTaxRate(transactionsDB, withholdingTaxRateService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.DELETE("/withholding_tax_rates/:id", deleteWithholdingTaxRate(transactionsDB, withholdingTaxRateService))
	}
}
