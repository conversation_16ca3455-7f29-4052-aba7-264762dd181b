package customers

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createCustomer(
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var customerForm forms.CreateCustomerForm
		err := ctx.Bind(&customerForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind customer form while creating customer",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		customer, err := customerService.CreateCustomer(ctx.Request.Context(), transactionsDB, &customerForm, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save customer. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, customer)
	}
}

func downloadCustomersExcel(
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering categories.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Customers-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		filter.StoreID = storeId

		_, err = customerService.DownloadCustomers(context.Background(), transactionsDB, filter, excelFilePath, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download categories",
			})
			return
		}

		// ctx.JSON(http.StatusOK, customerList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterCustomers(
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering customers",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		filter.StoreID = storeId

		customerList, err := customerService.FilterCustomers(context.Background(), transactionsDB, filter, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter customers",
			})
			return
		}

		ctx.JSON(http.StatusOK, customerList)
	}
}

func getCustomer(
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		customerIDStr := ctx.Param("id")
		customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse customerID=[%v], err=[%v]\n", customerIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		customer, err := customerService.FindCustomerByID(context.Background(), customerID, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to fetch customer",
			})
			return
		}

		ctx.JSON(http.StatusOK, customer)
	}
}

func updateCustomer(
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		customerIDStr := ctx.Param("id")
		customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse customerID=[%v], err=[%v]\n", customerIDStr, err)
		}

		var form forms.UpdateCustomerForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind customer form while updating customer",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		customer, err := customerService.UpdateCustomer(ctx.Request.Context(), transactionsDB, customerID, &form, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update customer",
			})
			return
		}

		ctx.JSON(http.StatusOK, customer)
	}
}
