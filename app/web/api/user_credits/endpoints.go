package user_credits

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	userCreditService services.UserCreditService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	b2bAPI := routerGroup.Group("")
	{
		b2bAPI.POST("/user_credits/topup", topUpUserCredits(transactionsDB, userCreditService))
	}
}
