package tax_types

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createTaxType(
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var taxTypeForm forms.CreateTaxTypeForm
		err := ctx.Bind(&taxTypeForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind taxType form while creating taxType",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxType, err := taxTypeService.CreateTaxType(ctx.Request.Context(), transactionsDB, &taxTypeForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save taxType. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, taxType)
	}
}

func filterTaxTypes(
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering taxTypes",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxTypeList, err := taxTypeService.FilterTaxTypes(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter taxTypes",
			})
			return
		}

		ctx.JSON(http.StatusOK, taxTypeList)
	}
}

func getTaxType(
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxTypeIDStr := ctx.Param("id")
		taxTypeID, err := strconv.ParseInt(taxTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxTypeID=[%v], err=[%v]\n", taxTypeIDStr, err)
		}

		taxType, err := taxTypeService.FindTaxTypeByID(ctx.Request.Context(), taxTypeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find taxType by id=[%v]",
				taxTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxType)
	}
}

func deleteTaxType(
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxTypeIDStr := ctx.Param("id")
		taxTypeID, err := strconv.ParseInt(taxTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxTypeID=[%v], err=[%v]\n", taxTypeIDStr, err)
		}

		taxType, err := taxTypeService.DeleteTaxType(ctx.Request.Context(), taxTypeID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete taxType by id=[%v]",
				taxTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxType)
	}
}

func updateTaxType(
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxTypeIDStr := ctx.Param("id")
		taxTypeID, err := strconv.ParseInt(taxTypeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxTypeID=[%v], err=[%v]\n", taxTypeIDStr, err)
		}

		var form forms.UpdateTaxTypeForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind taxType form while updating taxType",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxType, err := taxTypeService.UpdateTaxType(ctx.Request.Context(), transactionsDB, taxTypeID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update taxType by id=[%v]",
				taxTypeIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxType)
	}
}
