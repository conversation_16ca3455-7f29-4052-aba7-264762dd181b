package tax_types

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	taxTypeService services.TaxTypeService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/tax_types", createTaxType(transactionsDB, taxTypeService))
		protectedAPI.GET("/tax_types", filterTaxTypes(transactionsDB, taxTypeService))
		protectedAPI.GET("/tax_types/:id", getTaxType(transactionsDB, taxTypeService))
		protectedAPI.PUT("/tax_types/:id", updateTaxType(transactionsDB, taxTypeService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.DELETE("/tax_types/:id", deleteTaxType(transactionsDB, taxTypeService))
	}
}
