package credit_notes

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	creditNoteService services.CreditNoteService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/credit_notes", signCreditNote(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_vscu/credit_notes", filterCreditNotes(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_vscu/credit_notes/:id", getCreditNote(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_vscu/credit_notes/download", downloadCreditNotesExcel(transactionsDB, creditNoteService))

		// ToDo: Update functions to process the following endpoints via ETIMS OSCU and not VSCU
		protectedAPI.POST("/etims_oscu/credit_notes", signCreditNote(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_oscu/credit_notes", filterCreditNotes(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_oscu/credit_notes/:id", getCreditNote(transactionsDB, creditNoteService))
		protectedAPI.GET("/etims_oscu/credit_notes/download", downloadCreditNotesExcel(transactionsDB, creditNoteService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/credit_notes/:id/pdf", getCreditNotePDFFile(transactionsDB, creditNoteService))
	}
}
