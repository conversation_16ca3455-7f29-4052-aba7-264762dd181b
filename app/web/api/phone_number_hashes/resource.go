package phone_number_hashes

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createPhoneNumberHash(
	transactionsDB *database.AppTransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var phoneNumberHashForm forms.CreatePhoneNumberHashForm
		err := ctx.Bind(&phoneNumberHashForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind phoneNumberHash form while creating phoneNumberHash",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		phoneNumberHash, err := phoneNumberHashService.CreatePhoneNumberHash(ctx.Request.Context(), transactionsDB, &phoneNumberHashForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save phoneNumberHash. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}

func filterphoneNumberHashes(
	transactionsDB *database.AppTransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering phoneNumberHashes",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		phoneNumberHashList, err := phoneNumberHashService.FilterPhoneNumberHashes(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter phoneNumberHashes",
			})
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHashList)
	}
}

func getPhoneNumberHashById(
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		phoneNumberHashIDStr := ctx.Param("id")
		phoneNumberHashID, err := strconv.ParseInt(phoneNumberHashIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse phoneNumberHashID=[%v], err=[%v]\n", phoneNumberHashIDStr, err)
		}

		phoneNumberHash, err := phoneNumberHashService.FindByID(ctx.Request.Context(), phoneNumberHashID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find phoneNumberHash by id=[%v]",
				phoneNumberHashIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}

func getPhoneNumberFromHash(
	transactionsDB *database.AppTransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		hash := ctx.Param("hash")

		phoneNumberHash, err := phoneNumberHashService.FindByHash(ctx.Request.Context(), transactionsDB, hash)
		if err != nil {
			appError := utils.NewError(
				err,
				"failed to find phoneNumber by hash=[%v]",
				hash,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}

func getPhoneNumberHashByPhoneNumber(
	transactionsDB database.TransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		phoneNumber := ctx.Param("phoneNumber")

		phoneNumberHash, err := phoneNumberHashService.FindByPhoneNumber(ctx.Request.Context(), transactionsDB, phoneNumber)
		if err != nil {
			appError := utils.NewError(
				err,
				"failed to find phoneNumberHash by phoneNumber=[%v]",
				phoneNumber,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}

func updatePhoneNumberHash(
	transactionsDB *database.AppTransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		phoneNumberHashIDStr := ctx.Param("id")
		phoneNumberHashID, err := strconv.ParseInt(phoneNumberHashIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse phoneNumberHashID=[%v], err=[%v]\n", phoneNumberHashIDStr, err)
		}

		var form forms.UpdatePhoneNumberHashForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"failed to bind phoneNumberHash form while updating phoneNumberHash",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		phoneNumberHash, err := phoneNumberHashService.UpdatePhoneNumberHash(ctx.Request.Context(), transactionsDB, phoneNumberHashID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"failed to update phoneNumberHash by id=[%v]",
				phoneNumberHashIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}

func uploadMultiplePhoneNumberHashes(
	transactionsDB *database.AppTransactionsDB,
	phoneNumberHashService services.PhoneNumberHashService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form forms.UploadMultiplePhoneNumberHashesForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"failed to bind phoneNumberHash upload form while bulk uploading hashes",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		phoneNumberHash, err := phoneNumberHashService.UploadMultiplePhoneNumberHashes(ctx.Request.Context(), transactionsDB, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save phoneNumberHash. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, phoneNumberHash)
	}
}
