package etims_vscu

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsVSCUService services.EtimsVSCUService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_vscu/invoices", signInvoice(transactionsDB, etimsVSCUService))
		protectedAPI.GET("/etims_vscu/invoices", filterInvoices(transactionsDB, apiKeyService))
		protectedAPI.GET("/etims_vscu/invoices/:id", getInvoice(transactionsDB, apiKeyService, etimsVSCUService))
	}
}
