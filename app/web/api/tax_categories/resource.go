package tax_categories

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createTaxCategory(
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var taxCategoryForm forms.CreateTaxCategoryForm
		err := ctx.Bind(&taxCategoryForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind taxCategory form while creating taxCategory",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxCategory, err := taxCategoryService.CreateTaxCategory(ctx.Request.Context(), transactionsDB, &taxCategoryForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save taxCategory. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, taxCategory)
	}
}

func filterTaxCategories(
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering taxCategories",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxCategoryList, err := taxCategoryService.FilterTaxCategories(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter taxCategories",
			})
			return
		}

		ctx.JSON(http.StatusOK, taxCategoryList)
	}
}

func getTaxCategory(
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxCategoryIDStr := ctx.Param("id")
		taxCategoryID, err := strconv.ParseInt(taxCategoryIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxCategoryID=[%v], err=[%v]\n", taxCategoryIDStr, err)
		}

		taxCategory, err := taxCategoryService.FindTaxCategoryByID(ctx.Request.Context(), taxCategoryID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find taxCategory by id=[%v]",
				taxCategoryIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxCategory)
	}
}

func deleteTaxCategory(
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxCategoryIDStr := ctx.Param("id")
		taxCategoryID, err := strconv.ParseInt(taxCategoryIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxCategoryID=[%v], err=[%v]\n", taxCategoryIDStr, err)
		}

		taxCategory, err := taxCategoryService.DeleteTaxCategory(ctx.Request.Context(), taxCategoryID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete taxCategory by id=[%v]",
				taxCategoryIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxCategory)
	}
}

func updateTaxCategory(
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		taxCategoryIDStr := ctx.Param("id")
		taxCategoryID, err := strconv.ParseInt(taxCategoryIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse taxCategoryID=[%v], err=[%v]\n", taxCategoryIDStr, err)
		}

		var form forms.UpdateTaxCategoryForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind taxCategory form while updating taxCategory",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		taxCategory, err := taxCategoryService.UpdateTaxCategory(ctx.Request.Context(), transactionsDB, taxCategoryID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update taxCategory by id=[%v]",
				taxCategoryIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, taxCategory)
	}
}
