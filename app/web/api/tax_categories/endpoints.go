package tax_categories

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	taxCategoryService services.TaxCategoryService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/tax_categories", createTaxCategory(transactionsDB, taxCategoryService))
		protectedAPI.GET("/tax_categories", filterTaxCategories(transactionsDB, taxCategoryService))
		protectedAPI.GET("/tax_categories/:id", getTaxCategory(transactionsDB, taxCategoryService))
		protectedAPI.PUT("/tax_categories/:id", updateTaxCategory(transactionsDB, taxCategoryService))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.DELETE("/tax_categories/:id", deleteTaxCategory(transactionsDB, taxCategoryService))
	}
}
