package receivings

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	receivingService services.ReceivingService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/receivings", createReceiving(transactionsDB, receivingService))
		protectedAPI.GET("/receivings", filterReceivings(transactionsDB, receivingService))
		protectedAPI.GET("/receivings/:id", getReceiving(transactionsDB, receivingService))
		protectedAPI.PUT("/receivings/:id", updateReceiving(transactionsDB, receivingService))
		protectedAPI.GET("/receivings/download", downloadReceivingsExcel(transactionsDB, receivingService))
	}
}
