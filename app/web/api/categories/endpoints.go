package categories

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/categories", createCategory(transactionsDB, categoryService))
		protectedAPI.GET("/categories", filterCategories(transactionsDB, categoryService))
		protectedAPI.GET("/categories/:id", getCategory(transactionsDB, categoryService))
		protectedAPI.PUT("/categories/:id", updateCategory(transactionsDB, categoryService))
		protectedAPI.GET("/categories/download", downloadCategoriesExcel(transactionsDB, categoryService))
	}
}
