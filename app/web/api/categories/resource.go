package categories

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createCategory(
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var categoryForm forms.CreateCategoryForm
		err := ctx.Bind(&categoryForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind category form while creating category",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		category, err := categoryService.CreateCategory(ctx.Request.Context(), transactionsDB, &categoryForm, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save category. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, category)
	}
}

func downloadCategoriesExcel(
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering categories.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Categories-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = categoryService.DownloadCategories(context.Background(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download categories",
			})
			return
		}

		// ctx.JSON(http.StatusOK, categoryList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterCategories(
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering categories.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		categoryList, err := categoryService.FilterCategories(context.Background(), transactionsDB, filter, storeId)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter categories",
			})
			return
		}

		ctx.JSON(http.StatusOK, categoryList)
	}
}

func getCategory(
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		categoryIDStr := ctx.Param("id")
		categoryID, err := strconv.ParseInt(categoryIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse categoryID=[%v], err=[%v]\n", categoryIDStr, err)
		}

		category, err := categoryService.FindCategoryByID(context.Background(), categoryID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to fetch category",
			})
			return
		}

		ctx.JSON(http.StatusOK, category)
	}
}

func updateCategory(
	transactionsDB *database.AppTransactionsDB,
	categoryService services.CategoryService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		categoryIDStr := ctx.Param("id")
		categoryID, err := strconv.ParseInt(categoryIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse categoryID=[%v], err=[%v]\n", categoryIDStr, err)
		}

		var form forms.UpdateCategoryForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind category form while creating category",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		category, err := categoryService.UpdateCategory(ctx.Request.Context(), transactionsDB, categoryID, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update category",
			})
			return
		}

		ctx.JSON(http.StatusOK, category)
	}
}
