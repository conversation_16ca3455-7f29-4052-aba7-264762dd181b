package suppliers

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createSupplier(
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var supplierForm forms.CreateSupplierForm
		err := ctx.Bind(&supplierForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind supplier form while creating supplier",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		supplier, err := supplierService.CreateSupplier(ctx.Request.Context(), transactionsDB, &supplierForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save supplier. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, supplier)
	}
}

func downloadSuppliersExcel(
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering suppliers.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Suppliers-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = supplierService.DownloadSuppliers(context.Background(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download suppliers",
			})
			return
		}

		// ctx.JSON(http.StatusOK, supplierList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterSuppliers(
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering suppliers.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		supplierList, err := supplierService.FilterSuppliers(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter suppliers",
			})
			return
		}

		ctx.JSON(http.StatusOK, supplierList)
	}
}

func getSupplier(
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		supplierIDStr := ctx.Param("id")
		supplierID, err := strconv.ParseInt(supplierIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse supplierID=[%v], err=[%v]\n", supplierIDStr, err)
		}

		supplier, err := supplierService.FindSupplierByID(context.Background(), supplierID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to find supplier",
			})
			return
		}

		ctx.JSON(http.StatusOK, supplier)
	}
}

func updateSupplier(
	transactionsDB *database.AppTransactionsDB,
	supplierService services.SupplierService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		supplierIDStr := ctx.Param("id")
		supplierID, err := strconv.ParseInt(supplierIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse supplierID=[%v], err=[%v]\n", supplierIDStr, err)
		}

		var form forms.UpdateSupplierForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind supplier form while creating supplier",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		supplier, err := supplierService.UpdateSupplier(ctx.Request.Context(), transactionsDB, supplierID, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update supplier",
			})
			return
		}

		ctx.JSON(http.StatusOK, supplier)
	}
}
