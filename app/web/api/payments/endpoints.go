package payments

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	txnsDB *database.AppTransactionsDB,
	paymentService services.PaymentService,
	paymentTransactionsService services.PaymentTransactionsService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(txnsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/payments/stk/push", initiateMPesaSTKPush(txnsDB, paymentService))
	}

	verifiedAdminUserAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(txnsDB, sessionAuthenticator, sessionService))
	{
		verifiedAdminUserAPI.GET("/payments/safaricom/acc_bal", requestSafaricomPaybillAccBalance(txnsDB, paymentTransactionsService))
		verifiedAdminUserAPI.POST("/payments/safaricom/register_url", registerMPesaCallbackURL(txnsDB, paymentTransactionsService))
		verifiedAdminUserAPI.POST("/payments/clients/register_mpesa_urls", registerClientsMPesaCallbackURLs(txnsDB, paymentTransactionsService))

	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/payments/mpesa/phone_decode/:hash", decodeMpesaPhonenumberHash(txnsDB, paymentService))
		unauthenticatedAPI.POST("/payments/safaricom/acc_bal", processSafaricomPaybillAccBalance(txnsDB))
		unauthenticatedAPI.POST("/payments/safaricom/confirmation", processSafaricomPaymentConfirmation(txnsDB, paymentService))
		unauthenticatedAPI.POST("/payments/safaricom/validation", processSafaricomPaymentValidation(txnsDB, paymentService))
		unauthenticatedAPI.POST("/payments/safaricom/stk_callback", processSafaricomSTKCallback(txnsDB))
		unauthenticatedAPI.POST("/payments/safaricom/result", processSafaricomSTKResult(txnsDB))
		unauthenticatedAPI.POST("/payments/safaricom/timeout", processSafaricomTimeout(txnsDB))
	}
}
