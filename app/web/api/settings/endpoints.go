package settings

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	settingService services.SettingService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/settings", saveSetting(transactionsDB, settingService))
		protectedAPI.GET("/settings", filterSettings(transactionsDB, settingService))
		protectedAPI.GET("/settings/:id", getSetting(transactionsDB, settingService))
		protectedAPI.PUT("/settings/:id", saveSetting(transactionsDB, settingService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/settings/:id/pdf", saveSetting(transactionsDB, settingService))
	}
}
