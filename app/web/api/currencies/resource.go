package currencies

import (
	"net/http"
	"strconv"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/services"
)

func filterCurrencies(
	transactionsDB database.TransactionsSQLOperations,
	currencyService services.CurrencyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering currencies.",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		currencyList, err := currencyService.FilterCurrencies(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to filter currencies by filter=[%v]",
				filter,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, currencyList)
	}
}

func getCurrency(
	transactionsDB database.TransactionsSQLOperations,
	currencyService services.CurrencyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		currencyID, err := strconv.ParseInt(ctx.Params.ByName("id"), 0, 64)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to convert app request id to int64=[%v]",
				ctx.Param("id"),
			)

			webutils.HandleError(ctx, appError)
			return
		}

		currency, err := currencyService.FindCurrencyByID(ctx.Request.Context(), transactionsDB, currencyID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find currency by id=[%v]",
				currencyID,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, currency)
	}
}
