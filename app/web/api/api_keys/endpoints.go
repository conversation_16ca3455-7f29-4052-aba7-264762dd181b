package api_keys

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/api_keys", createAPIKey(transactionsDB, apiKeyService))
		protectedAPI.GET("/api_keys", filterAPIKeys(transactionsDB, apiKeyService))
		protectedAPI.GET("/api_keys/:id", getAPIKey(transactionsDB, apiKeyService))
		protectedAPI.PUT("/api_keys/:id", update<PERSON><PERSON><PERSON><PERSON>(transactionsDB, apiKeyService))
		protectedAPI.DELETE("/api_keys/:id", deleteAPIKey(transactionsDB, apiKeyService))
		protectedAPI.POST("/api_keys_t", testAPIKey(transactionsDB, apiKeyService))
	}
}
