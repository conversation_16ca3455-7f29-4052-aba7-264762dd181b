package instagram

import (
	"io"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
)

func processInstagramCallback(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		body, _ := io.ReadAll(ctx.Request.Body)
		requestBody := string(body)

		challenge := ctx.Query("hub.challenge")
		verifyToken := ctx.Query("hub.verify_token")

		log.Printf("instagram callback, requestBody=[%v], challenge=[%v], verifyToken=[%v]\n", requestBody, challenge, verifyToken)

		// lmNEt4MTkzbEptaVpKcm9hZAGF

		if verifyToken == "lmNEt4MTkzbEptaVpKcm9hZAGF" {
			// ctx.JSON(http.StatusOK, challenge)
			ctx.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(challenge))
			return
		}

		ctx.JSON(401, gin.H{
			"message": "unable to verify request",
		})
	}
}
