package instagram

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/instagram/callback_url", processInstagramCallback(transactionsDB, apiKeyService))
	}
}
