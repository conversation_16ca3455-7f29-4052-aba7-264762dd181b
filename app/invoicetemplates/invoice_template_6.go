package invoicetemplates

import (
	"bufio"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
)

// ProcessTemplateSixInvoice - ProcessTemplateSixInvoice
func ProcessTemplateSixInvoice(textFile string) models.Invoice {

	log.Println("Processing Template SIX Invoice File >>>> ", textFile)
	invoice := models.Invoice{}

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	var lineNumber = 1
	var lastLine = ""

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)

		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)

		lines = append(lines, invoiceLine)
		log.Println("[TMPL_6] invoiceLine ["+utils.ConvertIntToString(lineNumber)+"]  >>> ", invoiceLine)

		if strings.Contains(invoiceLine, "INVOICE") && strings.Contains(invoiceLine, "TO") {
			log.Println("[TMPL_6] :::::::::::::::   Processing invoice number line >>> ", invoiceLine)

			lineArr := strings.Split(invoiceLine, " ")
			index := utils.IndexOf("INVOICE", lineArr)
			log.Println(index)

			invoiceNumber := lineArr[index+2]

			log.Println("[TMPL_6] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

			// Apr 1, 2019
			dateIndex := utils.IndexOf("DATE", lineArr)
			// 2. April 2019
			dateString := lineArr[dateIndex+2] + " " + lineArr[dateIndex+1] + " " + lineArr[dateIndex+3]
			formatedDate := utils.FormatDateTypeThreeString(dateString)
			invoice.InvoiceDate = formatedDate

		} else if strings.Contains(invoiceLine, "AMOUNT") && strings.Contains(invoiceLine, "DUE") {

			log.Println("[TMPL_6] :::::::::::::::   Processing invoice amount line >>> ", invoiceLine)

			lineArr := strings.Split(invoiceLine, " ")
			index := utils.IndexOf("DUE", lineArr)
			log.Println(index)

			grandTotal := lineArr[index+1]

			log.Println("[TMPL_6] grandTotal >>> ", grandTotal)

			vat := "0.00"
			grandTotal = strings.Replace(grandTotal, ",", "", -1)

			log.Println("[TMPL_6] invoice VAT >>> ", vat)
			log.Println("[TMPL_6] invoice Grand Total >>> ", grandTotal)

			invoice.Vat = vat
			invoice.GrandTotal = grandTotal

		} else if strings.Contains(invoiceLine, "#") && strings.Contains(invoiceLine, "/") {
			invoiceSignature := strings.Replace(invoiceLine, " ", "", -1)
			invoice.Signature = invoiceSignature
		}

		if len(trimedInvoiceLine) > 1 && !strings.Contains(invoiceLine, "#") {
			lastLine = invoiceLine
		}

		lineNumber++
	}

	lastLine = strings.TrimSpace(lastLine)
	log.Println("[TMPL_6] lastLine >>> ", lastLine)

	return invoice
}
