package invoicetemplates

import (
	"bufio"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
)

// ProcessTemplateFiveInvoice - ProcessTemplateFiveInvoice
func ProcessTemplateFiveInvoice(textFile string) models.Invoice {

	log.Println("Processing Template FIVE Invoice File >>>> ", textFile)
	invoice := models.Invoice{}

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	var lineNumber = 1
	var lastLine = ""

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)

		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)

		lines = append(lines, invoiceLine)
		log.Println("[TMPL_5] invoiceLine ["+utils.ConvertIntToString(lineNumber)+"]  >>> ", invoiceLine)

		if strings.Contains(invoiceLine, "INVOICE") && strings.Contains(invoiceLine, "NO") {
			log.Println(":::::::::::::::   Processing invoice number line >>> ", invoiceLine)

			lineArr := strings.Split(invoiceLine, " ")
			index := utils.IndexOf("INVOICE", lineArr)
			log.Println(index)

			invoiceNumber := lineArr[index+2]

			log.Println("[TMPL_5] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

		} else if strings.Contains(invoiceLine, "PAGE") {

			invoiceLine = strings.TrimSpace(invoiceLine)
			dateArr := strings.Split(invoiceLine, " ")

			dateString := dateArr[len(dateArr)-1]
			formatedDate := utils.FormatDateTypeOneString(dateString)
			invoice.InvoiceDate = formatedDate

		} else if strings.Contains(invoiceLine, "#") && strings.Contains(invoiceLine, "/") {
			invoiceSignature := strings.Replace(invoiceLine, " ", "", -1)
			invoice.Signature = invoiceSignature
		}

		if len(trimedInvoiceLine) > 1 && !strings.Contains(invoiceLine, "#") {
			lastLine = invoiceLine
		}

		lineNumber++
	}

	lastLine = strings.TrimSpace(lastLine)
	log.Println("[TMPL_5] lastLine >>> ", lastLine)

	lineArr := strings.Split(lastLine, " ")
	lastLineArrLen := len(lineArr)
	log.Println("lastLineArrLen  >>> ", lastLineArrLen)

	vat := ""
	grandTotal := ""

	if lastLineArrLen > 1 {
		vat, grandTotal = lineArr[0], lineArr[1]
	} else {
		vat = "0.00"
		grandTotal = strings.Replace(lastLine, ",", "", -1)
	}

	vat = strings.Replace(vat, ",", "", -1)
	grandTotal = strings.Replace(grandTotal, ",", "", -1)

	log.Println("[TMPL_5] invoice VAT >>> ", vat)
	log.Println("[TMPL_5] invoice Grand Total >>> ", grandTotal)

	invoice.Vat = vat
	invoice.GrandTotal = grandTotal

	return invoice
}
