package invoicetemplates

import (
	"bufio"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
)

// ProcessTemplateFourInvoice - ProcessTemplateFourInvoice
func ProcessTemplateFourInvoice(textFile string) models.Invoice {

	log.Println("Processing Template FOUR Invoice File >>>> ", textFile)
	invoice := models.Invoice{}

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	var lineNumber = 1
	var invoiceNumberLine = 0
	var vatTzsLineNumber = 0

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)
		lines = append(lines, invoiceLine)
		log.Println("[TMPL_4] invoiceLine ["+utils.ConvertIntToString(lineNumber)+"]  >>> ", invoiceLine)

		if strings.Contains(invoiceLine, "INVOICE NO.") {
			invoiceNumberLine = lineNumber + 2
		} else if lineNumber == invoiceNumberLine {

			log.Println(":::::::::::::::   Processing invoice number line >>> ", invoiceLine)
			lineArr := strings.Split(invoiceLine, " ")
			arrLen := len(lineArr)
			log.Println("Invoice no arr len : ", arrLen)
			invoiceNumber := lineArr[0]
			log.Println("[TMPL_4] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

			// 19/07/2020
			invoiceDate := lineArr[arrLen-2]
			log.Println("[TMPL_3] invoiceDate   >>> ", invoiceDate)

			formatedDate := utils.FormatDateTypeOneString(invoiceDate)
			invoice.InvoiceDate = formatedDate

		} else if strings.Contains(invoiceLine, "VAT") && strings.Contains(invoiceLine, "TZS") && strings.Contains(invoiceLine, "TOTAL") {
			vatTzsLineNumber = lineNumber + 2
		} else if lineNumber == vatTzsLineNumber {

			log.Println("[TMPL_4] invoice totals line >>> ", invoiceLine)
			lineArr := strings.Split(invoiceLine, " ")

			log.Println("Vat Tzs Totals arr len : ", len(lineArr))
			vat, grandTotal := lineArr[0], lineArr[1]

			vat = strings.Replace(vat, ",", "", -1)
			grandTotal = strings.Replace(grandTotal, ",", "", -1)

			log.Println("[TMPL_4] invoice VAT >>> ", vat)
			log.Println("[TMPL_4] invoice Grand Total >>> ", grandTotal)

			invoice.Vat = vat
			invoice.GrandTotal = grandTotal

		} else if strings.Contains(invoiceLine, "#") && strings.Contains(invoiceLine, "/") {
			invoiceSignature := strings.Replace(invoiceLine, " ", "", -1)
			invoice.Signature = invoiceSignature
		}

		lineNumber++
	}

	return invoice
}
