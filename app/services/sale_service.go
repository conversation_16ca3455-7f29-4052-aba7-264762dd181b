package services

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"errors"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type SaleService interface {
	CreateSale(ctx context.Context, transactionsDB txns_db.TransactionsDB, sale *forms.CreateSaleForm, storeId int64) (*entities.Sale, error)
	DownloadSales(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.SaleList, error)
	FilterSales(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, storeId int64) (*entities.SaleList, error)
	FilterSalesForStore(ctx context.Context, operations txns_db.TransactionsSQLOperations, storeID int64, filter *entities.PaginationFilter) (*entities.SaleList, error)
	FindSaleByID(ctx context.Context, operations txns_db.TransactionsSQLOperations, saleID int64, storeId int64) (*entities.Sale, error)
	GetSaleByUUID(ctx context.Context, saleNumber string) (*entities.Sale, error)
	SaveSale(ctx context.Context, operations txns_db.TransactionsSQLOperations, sale *entities.Sale) error
	UpdateSale(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateSaleForm, int64) (*entities.Sale, error)
}

type AppSaleService struct {
	currencyRepository     repos.CurrencyRepository
	itemRepository         repos.ItemRepository
	organizationRepository repos.OrganizationRepository
	saleItemRepository     repos.SaleItemRepository
	saleRepository         repos.SaleRepository
	storeRepository        repos.StoreRepository
	userRepository         repos.UserRepository
}

func NewSaleService(
	currencyRepository repos.CurrencyRepository,
	itemRepository repos.ItemRepository,
	organizationRepository repos.OrganizationRepository,
	saleItemRepository repos.SaleItemRepository,
	saleRepository repos.SaleRepository,
	storeRepository repos.StoreRepository,
	userRepository repos.UserRepository,
) SaleService {
	return &AppSaleService{
		currencyRepository:     currencyRepository,
		itemRepository:         itemRepository,
		organizationRepository: organizationRepository,
		saleItemRepository:     saleItemRepository,
		saleRepository:         saleRepository,
		storeRepository:        storeRepository,
		userRepository:         userRepository,
	}
}

func (s *AppSaleService) CreateSale(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	form *forms.CreateSaleForm,
	storeId int64,
) (*entities.Sale, error) {

	sale := &entities.Sale{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Sale{}, err
	}

	// organization, err := s.organizationRepository.FindByID(ctx, form.OrganizationID)
	// if err != nil {
	// 	return sale, errors.New("organization by id not found")
	// }

	currency, err := s.currencyRepository.FindByID(ctx, transactionsDB, form.CurrencyID)
	if err != nil {
		return sale, errors.New("currency by id not found")
	}

	store, err := s.storeRepository.FindByID(ctx, form.StoreID)
	if err != nil {
		return sale, errors.New("store by id not found")
	}

	vatAmtInt64, err := utils.ConvertCurrencyAmountFromFloat64ToInt64(form.Vat, currency.Scale)
	if err != nil {
		return sale, errors.New("failed to convert vat amt to int64")
	}

	totalAmtInt64, err := utils.ConvertCurrencyAmountFromFloat64ToInt64(form.Total, currency.Scale)
	if err != nil {
		return sale, errors.New("failed to convert total amt to int64")
	}

	displayAmount := utils.ConvertCurrencyAmountFromInt64ToFloat64Str(totalAmtInt64, int32(currency.Scale), currency.Scale)

	amountTendered, err := utils.ConvertCurrencyAmountFromFloat64ToInt64(form.AmountTendered, currency.Scale)
	if err != nil {
		return sale, errors.New("failed to convert amount tendered to int64")
	}

	changeAmt, err := utils.ConvertCurrencyAmountFromFloat64ToInt64(form.Change, currency.Scale)
	if err != nil {
		return sale, errors.New("failed to convert change amt to int64")
	}

	sale = &entities.Sale{
		AmountTendered:     amountTendered,
		Change:             changeAmt,
		CreatedBy:          user.ID,
		CurrencyID:         currency.ID,
		CustomerID:         form.CustomerID,
		OrganizationID:     user.OrganizationID,
		PaymentMethod:      form.PaymentMethod,
		Scale:              int64(currency.Scale),
		Status:             entities.SaleStatusPending.String(),
		StoreID:            store.ID,
		Subtotal:           form.Subtotal,
		Total:              totalAmtInt64,
		TotalAmountDisplay: displayAmount,
		UUID:               utils.GenerateSaleUUID(),
		Vat:                vatAmtInt64,
	}

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		// Create sale
		err = s.saleRepository.Save(ctx, operations, sale)
		if err != nil {
			return err
		}

		saleItems := make([]*entities.SaleItem, 0)

		for _, sateItemForm := range form.SaleItems {

			// ToDo calculate item total with discount consideration

			saleItem := &entities.SaleItem{
				CurrencyID:     currency.ID,
				CreatedBy:      user.ID,
				Discount:       sateItemForm.Discount,
				ItemID:         sateItemForm.ItemID,
				OrganizationID: 1,
				Quantity:       sateItemForm.Quantity,
				SaleID:         sale.ID,
				Scale:          int64(currency.Scale),
				Total:          sateItemForm.Total,
			}

			saleItems = append(saleItems, saleItem)
		}

		// Save multiple sale items
		err = s.saleItemRepository.SaveMultiple(ctx, operations, saleItems)
		if err != nil {
			return err
		}

		sale.SaleItems = saleItems

		return nil
	})

	if err != nil {
		utils.Log.Infof("failed to create sale, err=[%v] \n", err)
	}

	sale.Currency = currency
	// sale.Organization = organization
	sale.Store = store

	return sale, nil
}

func (s *AppSaleService) DownloadSales(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.SaleList, error) {

	saleList := &entities.SaleList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.saleRepository.CountSalesForUser(ctx, operations, tokenInfo.UserID, filter)
	if err != nil {
		return saleList, err
	}

	sales, err := s.saleRepository.FilterSalesForUser(ctx, operations, tokenInfo.UserID, filter)
	if err != nil {
		return saleList, err
	}

	saleList.Sales = sales

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	saleList.Pagination = pagination

	// utils.CreateAndAppendSalesDataToExcelFile(filePath, sales)

	return saleList, nil
}

func (s *AppSaleService) FilterSales(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) (*entities.SaleList, error) {

	saleList := &entities.SaleList{}

	// tokenInfo := ctxhelper.TokenInfo(ctx)
	// userID := tokenInfo.UserID

	// ToDo: Check that the user has access permissions to the respective store

	count, err := s.saleRepository.CountSalesForStore(ctx, operations, storeId, filter)
	if err != nil {
		return saleList, err
	}

	sales, err := s.saleRepository.FilterSalesForStore(ctx, operations, storeId, filter)
	if err != nil {
		return saleList, err
	}

	err = s.populateSaleItems(ctx, operations, storeId, filter, sales)
	if err != nil {
		return saleList, err
	}

	saleList.Sales = sales

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	saleList.Pagination = pagination

	return saleList, nil
}

func (s *AppSaleService) FilterSalesForStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	filter *entities.PaginationFilter,
) (*entities.SaleList, error) {

	saleList := &entities.SaleList{}

	count, err := s.saleRepository.CountSalesForStore(ctx, operations, storeID, filter)
	if err != nil {
		return saleList, err
	}

	sales, err := s.saleRepository.FilterSalesForStore(ctx, operations, storeID, filter)
	if err != nil {
		return saleList, err
	}

	err = s.populateSaleItems(ctx, operations, storeID, filter, sales)
	if err != nil {
		return saleList, err
	}

	saleList.Sales = sales

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	saleList.Pagination = pagination

	return saleList, nil
}

func (s *AppSaleService) FindSaleByID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleID int64,
	storeId int64,
) (*entities.Sale, error) {

	// Validate store id from token

	sale, err := s.saleRepository.FindByID(ctx, saleID, storeId)
	if err != nil {
		return sale, err
	}

	filter := &entities.PaginationFilter{
		Offset: 0,
	}

	// Populate sale items
	saleItemsCount, err := s.saleItemRepository.CountSaleItems(ctx, filter, saleID)
	if err != nil {
		return sale, err
	}

	filter.Limit = saleItemsCount

	saleItems, err := s.saleItemRepository.FilterSaleItemsForSaleId(ctx, operations, saleID, filter, storeId)
	if err != nil {
		return sale, err
	}

	saleItemsMap := make(map[int64]*entities.Item)
	itemIds := []int64{}
	for _, saleItem := range saleItems {
		itemIds = append(itemIds, saleItem.ItemID)
		saleItemsMap[saleItem.ItemID] = &entities.Item{}
		saleItem.Item = saleItemsMap[saleItem.ItemID]
	}

	items, err := s.itemRepository.GetItemsByIds(ctx, operations, itemIds, storeId)
	if err != nil {
		return sale, err
	}

	for _, item := range items {
		saleItemsMap[item.ID] = item
	}

	for _, saleItem := range saleItems {
		saleItem.Item = saleItemsMap[saleItem.ItemID]
		saleItem.ItemName = saleItemsMap[saleItem.ItemID].Name
	}

	sale.SaleItems = saleItems

	return sale, nil
}

func (s *AppSaleService) GetSaleByUUID(
	ctx context.Context,
	uuid string,
) (*entities.Sale, error) {

	sale, err := s.saleRepository.FindByUUID(ctx, uuid)
	if err != nil {
		return sale, err
	}

	return sale, nil
}

func (s *AppSaleService) SaveSale(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	sale *entities.Sale,
) error {

	err := s.saleRepository.Save(ctx, operations, sale)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppSaleService) UpdateSale(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleID int64,
	form *forms.UpdateSaleForm,
	storeId int64,
) (*entities.Sale, error) {

	sale, err := s.saleRepository.FindByID(ctx, saleID, storeId)
	if err != nil {
		return sale, errors.New("unable to find sale")
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Sale{}, err
	}

	if sale.OrganizationID != user.OrganizationID {
		return &entities.Sale{}, errors.New("user does not have permission to update organization sale")
	}

	sale.AmountTendered = form.AmountTendered
	sale.Change = form.Change

	if form.AmountTendered > 0 {
		sale.AmountTendered = form.AmountTendered
	}

	if form.Change > 0 {
		sale.Change = form.Change
	}

	err = s.saleRepository.Save(ctx, operations, sale)
	if err != nil {
		return sale, err
	}

	return sale, nil
}

func (s *AppSaleService) populateSaleItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeId int64,
	filter *entities.PaginationFilter,
	sales []*entities.Sale,
) error {

	utils.Log.Infof("numSales=[%v]\n", len(sales))

	saleItemsMap := make(map[int64][]*entities.SaleItem)
	saleIds := []int64{}
	for _, sale := range sales {
		saleIds = append(saleIds, sale.ID)
	}

	utils.Log.Infof("saleIds=[%+v]\n", saleIds)

	saleItems, err := s.saleItemRepository.FilterSaleItemsForSaleIds(ctx, operations, saleIds, filter, storeId)
	if err != nil {
		return err
	}

	utils.Log.Infof("num saleItems=[%v]\n", len(saleItems))

	itemsMap := make(map[int64]*entities.Item)
	itemIds := []int64{}
	for _, saleItem := range saleItems {
		itemIds = append(itemIds, saleItem.ItemID)
		itemsMap[saleItem.ItemID] = &entities.Item{}
		saleItem.Item = itemsMap[saleItem.ItemID]

		saleItemsMap[saleItem.SaleID] = append(saleItemsMap[saleItem.SaleID], saleItem)
	}

	utils.Log.Infof("itemIds=[%+v]\n", itemIds)

	items, err := s.itemRepository.GetItemsByIds(ctx, operations, itemIds, storeId)
	if err != nil {
		return err
	}

	utils.Log.Infof("num items=[%v]\n", len(items))

	for _, item := range items {
		itemsMap[item.ID] = item
	}

	for _, saleItem := range saleItems {
		saleItem.Item = itemsMap[saleItem.ItemID]
		saleItem.ItemName = itemsMap[saleItem.ItemID].Name
	}

	for _, sale := range sales {
		sale.SaleItems = saleItemsMap[sale.ID]
	}

	return nil
}
