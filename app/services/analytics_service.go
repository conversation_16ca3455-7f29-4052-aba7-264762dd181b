package services

import (
	"context"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"time"

	"gopkg.in/guregu/null.v3"
)

type AnalyticsService interface {
	GetDashboardAnalytics(ctx context.Context, operations txns_db.TransactionsSQLOperations) (*entities.DashboardAnalytics, error)
}

type AppAnalyticsService struct {
	apiKeyRepository          repos.APIKeyRepository
	categoryRepository        repos.CategoryRepository
	customerRepository        repos.CustomerRepository
	excelFileUploadRepository repos.ExcelFileUploadRepository
	itemRepository            repos.ItemRepository
	invoiceRepository         repos.InvoiceRepository
	organizationRepository    repos.OrganizationRepository
	saleRepository            repos.SaleRepository
	storeRepository           repos.StoreRepository
	userCreditRepository      repos.UserCreditRepository
	userRepository            repos.UserRepository
}

func NewAnalyticsService(
	apiKeyRepository repos.APIKeyRepository,
	categoryRepository repos.CategoryRepository,
	customerRepository repos.CustomerRepository,
	excelFileUploadRepository repos.ExcelFileUploadRepository,
	itemRepository repos.ItemRepository,
	invoiceRepository repos.InvoiceRepository,
	organizationRepository repos.OrganizationRepository,
	saleRepository repos.SaleRepository,
	storeRepository repos.StoreRepository,
	userCreditRepository repos.UserCreditRepository,
	userRepository repos.UserRepository,
) AnalyticsService {
	return &AppAnalyticsService{
		apiKeyRepository:          apiKeyRepository,
		categoryRepository:        categoryRepository,
		customerRepository:        customerRepository,
		excelFileUploadRepository: excelFileUploadRepository,
		itemRepository:            itemRepository,
		invoiceRepository:         invoiceRepository,
		organizationRepository:    organizationRepository,
		saleRepository:            saleRepository,
		storeRepository:           storeRepository,
		userCreditRepository:      userCreditRepository,
		userRepository:            userRepository,
	}
}

func (s *AppAnalyticsService) GetDashboardAnalytics(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) (*entities.DashboardAnalytics, error) {

	dashboardAnalytics := &entities.DashboardAnalytics{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	storeID := tokenInfo.StoreID

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		log.Printf("failed to find user by id=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	dashboardAnalytics.AccountNumber = user.AccountNumber

	organization, err := s.organizationRepository.GetOrganizationByID(user.OrganizationID)
	if err != nil {
		log.Printf("failed to retrieve organization for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return dashboardAnalytics, err
	}

	filter := &entities.PaginationFilter{
		OrganizationID: organization.ID,
		StoreID:        defaultStore.ID,
		UserID:         tokenInfo.UserID,
	}

	// Count API Keys
	apiKeysCount, err := s.apiKeyRepository.CountAPIKeysForUser(ctx, user.ID, filter)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve user api keys count, err=[%v]\n", err)
		return dashboardAnalytics, err
	}
	dashboardAnalytics.APIKeysCount = apiKeysCount

	// Get user credits
	userCredits, err := s.userCreditRepository.FindByUserID(ctx, user.ID)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve user credits balance, err=[%v]\n", err)
		return dashboardAnalytics, err
	}
	dashboardAnalytics.UserCredits = userCredits.CreditBalance

	filter.SearchCriteria = ""
	chartSummary := []entities.ChartSummary{}
	months := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}
	filter.Show = ""
	if filter.Year == 0 {
		filter.Year = time.Now().Year()
	}

	for _, month := range months {

		// TODO: Save the below data periodically in a single table for analytics and fetch the data from there

		chartSummaryData := entities.ChartSummary{}
		chartSummaryData.Month = month
		filter.Month = month

		currentDate := time.Date(filter.Year, time.Month(month), 1, 0, 0, 0, 0, utils.LocKenya)
		filter.StartDate = null.TimeFrom(currentDate)
		filter.EndDate = null.TimeFrom(currentDate.AddDate(0, 1, 0))

		// All invoices count
		allInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.RequestsCount = allInvoicesCount

		// Success
		filter.SearchCriteria = "signed_invoices"
		successInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.ProcessedSuccessfully = successInvoicesCount

		// Failed
		filter.SearchCriteria = "invoices_failed_to_sign"
		failedInvoicesCount, err := s.invoiceRepository.CountInvoices(ctx, filter)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count invoices for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.Failed = failedInvoicesCount

		totalSales, err := s.saleRepository.CountSales(ctx, filter, storeID)
		if err != nil {
			return dashboardAnalytics, utils.NewDatabaseError(
				err,
				"Failed to count sales for dashboard chart report with filter=[%v]",
				filter,
			)
		}

		chartSummaryData.TotalRequests = totalSales

		chartSummary = append(chartSummary, chartSummaryData)
		filter.SearchCriteria = ""
	}

	dashboardAnalytics.ChartSummary = chartSummary

	return dashboardAnalytics, nil
}
