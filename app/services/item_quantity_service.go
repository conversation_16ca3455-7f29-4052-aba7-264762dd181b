package services

import (
	"context"
	"errors"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type ItemQuantityService interface {
	CreateItemQuantity(ctx context.Context, operations txns_db.TransactionsSQLOperations, item *forms.CreateItemQuantityForm) (*entities.ItemQuantity, error)
	DownloadItemQuantity(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.ItemQuantityList, error)
	FilterItemQuantities(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.ItemQuantityList, error)
	FindItemQuantityByID(ctx context.Context, itemID int64) (*entities.ItemQuantity, error)
	UpdateItemQuantity(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateItemQuantityForm) (*entities.ItemQuantity, error)
}

type AppItemQuantityService struct {
	itemRepository         repos.ItemRepository
	organizationRepository repos.OrganizationRepository
	itemQuantityRepository repos.ItemQuantityRepository
	userRepository         repos.UserRepository
}

func NewItemQuantityService(
	itemRepository repos.ItemRepository,
	organizationRepository repos.OrganizationRepository,
	itemQuantityRepository repos.ItemQuantityRepository,
	userRepository repos.UserRepository,
) ItemQuantityService {
	return &AppItemQuantityService{
		itemRepository:         itemRepository,
		organizationRepository: organizationRepository,
		itemQuantityRepository: itemQuantityRepository,
		userRepository:         userRepository,
	}
}

func (s *AppItemQuantityService) CreateItemQuantity(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateItemQuantityForm,
) (*entities.ItemQuantity, error) {

	itemQuantity := &entities.ItemQuantity{}

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.ItemQuantity{}, err
	// }

	// organization, err := s.organizationRepository.FindByID(ctx, user.OrganizationID)
	// if err != nil {
	// 	return itemQuantity, errors.New("organization by id not found")
	// }

	item, err := s.itemRepository.FindByID(ctx, form.ItemID, form.ItemID)
	if err != nil {
		return itemQuantity, errors.New("item with id not found")
	}

	itemQuantity.ItemID = item.ID
	itemQuantity.Quantity = form.Quantity

	err = s.itemQuantityRepository.Save(ctx, itemQuantity)
	if err != nil {
		return itemQuantity, err
	}

	return itemQuantity, nil
}

func (s *AppItemQuantityService) DownloadItemQuantity(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.ItemQuantityList, error) {

	itemList := &entities.ItemQuantityList{}

	count := s.itemQuantityRepository.CountItemQuantities(ctx, filter)

	itemQuantities, err := s.itemQuantityRepository.FilterItemQuantities(ctx, operations, filter)
	if err != nil {
		return itemList, err
	}

	itemList.ItemQuantities = itemQuantities

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	itemList.Pagination = pagination

	// TODO: Implement
	// utils.CreateAndAppendItemQuantityDataToExcelFile(filePath, items)

	return itemList, nil
}

func (s *AppItemQuantityService) FilterItemQuantities(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ItemQuantityList, error) {

	itemQuantitiesList := &entities.ItemQuantityList{}

	count := s.itemQuantityRepository.CountItemQuantities(ctx, filter)

	itemQuantities, err := s.itemQuantityRepository.FilterItemQuantities(ctx, operations, filter)
	if err != nil && !utils.IsErrNoRows(err) {
		return itemQuantitiesList, err
	}

	itemQuantitiesList.ItemQuantities = itemQuantities

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	itemQuantitiesList.Pagination = pagination

	return itemQuantitiesList, nil
}

func (s *AppItemQuantityService) FindItemQuantityByID(
	ctx context.Context,
	itemQuantityID int64,
) (*entities.ItemQuantity, error) {

	itemQuantity, err := s.itemQuantityRepository.FindByID(ctx, itemQuantityID)
	if err != nil {
		return itemQuantity, err
	}

	return itemQuantity, nil
}

func (s *AppItemQuantityService) UpdateItemQuantity(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemQuantityID int64,
	form *forms.UpdateItemQuantityForm,
) (*entities.ItemQuantity, error) {

	itemQuantity, err := s.itemQuantityRepository.FindByID(ctx, itemQuantityID)
	if err != nil {
		return itemQuantity, errors.New("unable to find itemQuantity")
	}

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.ItemQuantity{}, err
	// }

	// if itemQuantity.OrganizationID != user.OrganizationID {
	// 	return &entities.ItemQuantity{}, errors.New("user does not have permission to update organization itemQuantity")
	// }

	itemQuantity.Quantity = form.Quantity

	err = s.itemQuantityRepository.Save(ctx, itemQuantity)
	if err != nil {
		return itemQuantity, err
	}

	return itemQuantity, nil
}
