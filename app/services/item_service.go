package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"database/sql"
	"errors"
	"strings"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type ItemService interface {
	CreateItem(ctx context.Context, operations txns_db.TransactionsSQLOperations, item *forms.CreateItemForm) (*entities.Item, error)
	DownloadItems(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string, storeId int64) (*entities.ItemList, error)
	FilterItems(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, storeId int64) (*entities.ItemList, error)
	FindItemByID(ctx context.Context, itemID int64, storeId int64) (*entities.Item, error)
	GetItemByName(ctx context.Context, itemNumber string) (*entities.Item, error)
	SaveItem(ctx context.Context, operations txns_db.TransactionsSQLOperations, item *entities.Item) error
	UpdateItem(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateItemForm, int64) (*entities.Item, error)
}

type AppItemService struct {
	etimsVSCU              providers.EtimsVSCU
	etimsItemRepository    repos.EtimsItemRepository
	itemQuantityRepository repos.ItemQuantityRepository
	itemRepository         repos.ItemRepository
	organizationRepository repos.OrganizationRepository
	storeRepository        repos.StoreRepository
	userRepository         repos.UserRepository
}

func NewItemService(
	etimsVSCU providers.EtimsVSCU,
	etimsItemRepository repos.EtimsItemRepository,
	itemQuantityRepository repos.ItemQuantityRepository,
	itemRepo repos.ItemRepository,
	organizationRepository repos.OrganizationRepository,
	storeRepository repos.StoreRepository,
	userRepository repos.UserRepository,
) ItemService {
	return &AppItemService{
		etimsVSCU:              etimsVSCU,
		etimsItemRepository:    etimsItemRepository,
		itemQuantityRepository: itemQuantityRepository,
		itemRepository:         itemRepo,
		organizationRepository: organizationRepository,
		storeRepository:        storeRepository,
		userRepository:         userRepository,
	}
}

func (s *AppItemService) CreateItem(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateItemForm,
) (*entities.Item, error) {

	item := &entities.Item{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Item{}, err
	}

	item, err = s.itemRepository.FindByNameAndStore(ctx, form.Name, form.StoreId)
	if err != nil && err != sql.ErrNoRows {
		utils.Log.Infof("unable to find item, err=[%v]\n", err)
		return item, errors.New("item already exists")
	}

	kraETIMSCode := utils.GenerateKRAItemCode(form.Name, 20)

	if form.ETIMSCode != "" && len(strings.TrimSpace(form.ETIMSCode)) == 20 {
		kraETIMSCode = form.ETIMSCode
	}

	item.ETIMSCode = kraETIMSCode
	item.Name = form.Name
	item.CategoryID = form.CategoryID
	item.Description = form.Description
	item.CreatedBy = user.ID
	item.OrganizationID = user.OrganizationID
	item.StoreID = form.StoreId
	item.CostPrice = form.CostPrice
	item.RetailPrice = form.RetailPrice
	item.AvatarURL = form.AvatarURL

	err = s.itemRepository.Save(ctx, item)
	if err != nil {
		return item, err
	}

	store, err := s.storeRepository.FindByID(ctx, form.StoreId)
	if err != nil {
		return item, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	storeCountryCode := strings.ToUpper(store.CountryCode)
	if storeCountryCode == "KE" || storeCountryCode == "KEN" {
		err = s.createItemOnKRAEtims(ctx, store, item, form)
		if err != nil {
			return item, err
		}
	}

	return item, nil
}

func (s *AppItemService) DownloadItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
	storeId int64,
) (*entities.ItemList, error) {

	itemList := &entities.ItemList{}

	count, err := s.itemRepository.CountItems(ctx, filter, storeId)
	if err != nil {
		return itemList, err
	}

	items, err := s.itemRepository.FilterItems(ctx, operations, filter, storeId)
	if err != nil {
		return itemList, err
	}

	itemList.Items = items

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	itemList.Pagination = pagination

	// utils.CreateAndAppendItemsDataToExcelFile(filePath, items)

	return itemList, nil
}

func (s *AppItemService) FilterItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) (*entities.ItemList, error) {

	itemList := &entities.ItemList{}

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	// defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	// if err != nil && utils.IsErrNoRows(err) {
	// 	log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
	// 	return itemList, err
	// }

	filter.StoreID = storeId

	count, err := s.itemRepository.CountItems(ctx, filter, storeId)
	if err != nil {
		logger.Errorf("failed to retrieve items count, err=[%v]\n", err)
		return itemList, err
	}

	items, err := s.itemRepository.FilterItems(ctx, operations, filter, storeId)
	if err != nil {
		logger.Errorf("failed to filter items, err=[%v]\n", err)
		return itemList, err
	}

	itemList.Items = items

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	itemList.Pagination = pagination

	return itemList, nil
}

func (s *AppItemService) FindItemByID(
	ctx context.Context,
	itemID int64,
	storeID int64,
) (*entities.Item, error) {

	item, err := s.itemRepository.FindByID(ctx, itemID, storeID)
	if err != nil {
		return item, err
	}

	return item, nil
}

func (s *AppItemService) GetItemByName(
	ctx context.Context,
	itemName string,
) (*entities.Item, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	// user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	// if err != nil {
	// 	return &entities.Item{}, err
	// }

	// organization, err := s.organizationRepository.FindByID(ctx, user.OrganizationID)
	// if err != nil {
	// 	return &entities.Item{}, errors.New("organization by id not found")
	// }

	item, err := s.itemRepository.FindByNameAndStore(ctx, itemName, tokenInfo.StoreID)
	if err != nil {
		return item, err
	}

	return item, nil
}

func (s *AppItemService) SaveItem(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	item *entities.Item,
) error {

	err := s.itemRepository.Save(ctx, item)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppItemService) UpdateItem(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemID int64,
	form *forms.UpdateItemForm,
	storeId int64,
) (*entities.Item, error) {

	item, err := s.itemRepository.FindByID(ctx, itemID, storeId)
	if err != nil {
		return item, errors.New("unable to find item")
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Item{}, err
	}

	if item.OrganizationID != user.OrganizationID {
		return &entities.Item{}, errors.New("user does not have permission to update organization item")
	}

	if form.Name != "" {
		item.Name = form.Name
	}

	if form.CategoryID > 0 {
		item.CategoryID = form.CategoryID
	}

	if form.Description != "" {
		item.Description = form.Description
	}

	if form.ETIMSCode != "" {
		item.ETIMSCode = form.ETIMSCode
	}

	if form.TaxPercent > 0 {
		item.TaxPercent = form.TaxPercent
	}

	if form.AvatarURL != "" {
		item.AvatarURL = form.AvatarURL
	}

	if form.CostPrice > 0 {
		item.CostPrice = form.CostPrice
	}

	if form.RetailPrice > 0 {
		item.RetailPrice = form.RetailPrice
	}

	err = s.itemRepository.Save(ctx, item)
	if err != nil {
		return item, err
	}

	return item, nil
}

func (s *AppItemService) createItemOnKRAEtims(
	ctx context.Context,
	store *entities.Store,
	item *entities.Item,
	form *forms.CreateItemForm,
) error {

	addInfo := strings.TrimSpace(item.Name)

	if len(addInfo) > 7 {
		addInfo = addInfo[0:7]
	}

	etimsItemForm := &forms.CreateEtimsItemForm{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		ItemCd:      item.ETIMSCode,
		ItemClsCd:   utils.GenerateKRAItemCode(item.Name, 5),
		ItemNm:      item.Name,
		ItemStdNm:   item.Name,
		AddInfo:     addInfo,
		ItemTyCd:    entities.EtimsProductTypeService.String(),
		DftPrc:      item.RetailPrice,
		GrpPrcL1:    form.RetailPrice,
		GrpPrcL2:    form.RetailPrice,
		GrpPrcL3:    form.RetailPrice,
		GrpPrcL4:    form.RetailPrice,
		GrpPrcL5:    form.RetailPrice,
		SftyQty:     form.Quantity,
		TaxTyCd:     entities.EtimsTaxType(form.TaxTypeCode).String(), // A, B, C, D, E
		IsrcAplcbYn: "N",
		ModrID:      "Admin",
		ModrNm:      "Admin",
		OrgnNatCd:   "KE",
		PkgUnitCd:   "BG",
		QtyUnitCd:   "U",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		UseYn:       "Y",
	}

	etimsItem := &entities.EtimsItem{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		ItemCd:      etimsItemForm.ItemCd,
		ItemClsCd:   etimsItemForm.ItemClsCd,
		ItemTyCd:    etimsItemForm.ItemTyCd,
		ItemNm:      etimsItemForm.ItemNm,
		ItemStdNm:   etimsItemForm.ItemStdNm,
		OrgnNatCd:   etimsItemForm.OrgnNatCd,
		PkgUnitCd:   etimsItemForm.PkgUnitCd,
		QtyUnitCd:   etimsItemForm.QtyUnitCd,
		TaxTyCd:     etimsItemForm.TaxTyCd,
		BtchNo:      etimsItemForm.BtchNo,
		Bcd:         etimsItemForm.Bcd,
		DftPrc:      etimsItemForm.DftPrc,
		GrpPrcL1:    etimsItemForm.GrpPrcL1,
		GrpPrcL2:    etimsItemForm.GrpPrcL2,
		GrpPrcL3:    etimsItemForm.GrpPrcL3,
		GrpPrcL4:    etimsItemForm.GrpPrcL4,
		GrpPrcL5:    etimsItemForm.GrpPrcL5,
		AddInfo:     etimsItemForm.AddInfo,
		SftyQty:     etimsItemForm.SftyQty,
		IsrcAplcbYn: etimsItemForm.IsrcAplcbYn,
		UseYn:       etimsItemForm.UseYn,
		RegrNm:      etimsItemForm.RegrNm,
		RegrID:      etimsItemForm.RegrID,
		ModrNm:      etimsItemForm.ModrNm,
		ModrID:      etimsItemForm.ModrID,
	}

	// Save locally
	err := s.etimsItemRepository.Save(ctx, etimsItem)
	if err != nil {
		utils.Log.Infof("failed to save etims item, err=[%v] \n", err)
		return err
	}

	utils.PrintStructToConsole(etimsItemForm)

	// Post to ETIMS
	response, err := s.etimsVSCU.SaveItem(etimsItemForm)
	if err != nil {
		utils.Log.Infof("failed to save item on etims, err=[%v] \n", err)
		return err
	}

	utils.Log.Infof("response.ResultCd=[%v]\n", response.ResultCd)

	return nil
}
