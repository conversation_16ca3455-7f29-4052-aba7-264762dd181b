package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type TaxTypeService interface {
	CreateTaxType(ctx context.Context, operations txns_db.TransactionsSQLOperations, taxType *forms.CreateTaxTypeForm) (*entities.TaxType, error)
	DeleteTaxType(ctx context.Context, taxTypeID int64) (*entities.TaxType, error)
	DownloadTaxTypes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.TaxTypeList, error)
	FilterTaxTypes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.TaxTypeList, error)
	FindTaxTypeByID(ctx context.Context, taxTypeID int64) (*entities.TaxType, error)
	GetTaxTypeByName(ctx context.Context, taxTypeNumber string) (*entities.TaxType, error)
	SaveTaxType(ctx context.Context, operations txns_db.TransactionsSQLOperations, taxType *entities.TaxType) error
	UpdateTaxType(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateTaxTypeForm) (*entities.TaxType, error)
}

type AppTaxTypeService struct {
	taxTypeRepository      repos.TaxTypeRepository
	organizationRepository repos.OrganizationRepository
	userRepository         repos.UserRepository
}

func NewTaxTypeService(
	organizationRepository repos.OrganizationRepository,
	taxTypeRepo repos.TaxTypeRepository,
	userRepository repos.UserRepository,
) TaxTypeService {
	return &AppTaxTypeService{
		organizationRepository: organizationRepository,
		taxTypeRepository:      taxTypeRepo,
		userRepository:         userRepository,
	}
}

func (s *AppTaxTypeService) CreateTaxType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateTaxTypeForm,
) (*entities.TaxType, error) {

	taxType := &entities.TaxType{}

	taxType.Name = form.Name
	taxType.Acronym = form.Acronym

	err := s.taxTypeRepository.Save(ctx, taxType)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create taxType with name=[%v]",
			form.Name,
		)
	}

	return taxType, nil
}

func (s *AppTaxTypeService) DeleteTaxType(
	ctx context.Context,
	taxTypeID int64,
) (*entities.TaxType, error) {

	taxType, err := s.taxTypeRepository.FindByID(ctx, taxTypeID)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete taxType by id=[%v]",
			taxTypeID,
		)
	}

	err = s.taxTypeRepository.Delete(ctx, taxType)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete taxType by id=[%v]",
			taxTypeID,
		)
	}

	return taxType, nil
}

func (s *AppTaxTypeService) DownloadTaxTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.TaxTypeList, error) {

	taxTypeList := &entities.TaxTypeList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.taxTypeRepository.CountTaxTypes(ctx, filter)
	if err != nil {
		return taxTypeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count taxTypes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxTypes, err := s.taxTypeRepository.FilterTaxTypes(ctx, operations, filter)
	if err != nil {
		return taxTypeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter taxTypes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxTypeList.TaxTypes = taxTypes

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	taxTypeList.Pagination = pagination

	// utils.CreateAndAppendTaxTypesDataToExcelFile(filePath, taxTypes)

	return taxTypeList, nil
}

func (s *AppTaxTypeService) FilterTaxTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.TaxTypeList, error) {

	taxTypeList := &entities.TaxTypeList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.taxTypeRepository.CountTaxTypes(ctx, filter)
	if err != nil {
		return taxTypeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count taxTypes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxTypes, err := s.taxTypeRepository.FilterTaxTypes(ctx, operations, filter)
	if err != nil {
		return taxTypeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter taxTypes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxTypeList.TaxTypes = taxTypes

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	taxTypeList.Pagination = pagination

	return taxTypeList, nil
}

func (s *AppTaxTypeService) FindTaxTypeByID(
	ctx context.Context,
	taxTypeID int64,
) (*entities.TaxType, error) {

	taxType, err := s.taxTypeRepository.FindByID(ctx, taxTypeID)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxType by id=[%v]",
			taxTypeID,
		)
	}

	return taxType, nil
}

func (s *AppTaxTypeService) GetTaxTypeByName(
	ctx context.Context,
	taxTypeName string,
) (*entities.TaxType, error) {

	taxType, err := s.taxTypeRepository.FindByName(ctx, taxTypeName)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxType by name=[%v]",
			taxTypeName,
		)
	}

	return taxType, nil
}

func (s *AppTaxTypeService) SaveTaxType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	taxType *entities.TaxType,
) error {

	err := s.taxTypeRepository.Save(ctx, taxType)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save taxType by name=[%v]",
			taxType.Name,
		)
	}

	return nil
}

func (s *AppTaxTypeService) UpdateTaxType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	taxTypeID int64,
	form *forms.UpdateTaxTypeForm,
) (*entities.TaxType, error) {

	taxType, err := s.taxTypeRepository.FindByID(ctx, taxTypeID)
	if err != nil {
		return taxType, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update taxType by id=[%v]",
			taxTypeID,
		)
	}

	if form.Name > "" {
		taxType.Name = form.Name
	}

	if form.Acronym > "" {
		taxType.Acronym = form.Acronym
	}

	err = s.taxTypeRepository.Save(ctx, taxType)
	if err != nil {
		return taxType, err
	}

	return taxType, nil
}
