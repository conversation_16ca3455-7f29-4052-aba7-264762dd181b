package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type WithholdingTaxRateService interface {
	CreateWithholdingTaxRate(ctx context.Context, operations txns_db.TransactionsSQLOperations, withholdingTaxRate *forms.CreateWithholdingTaxRateForm) (*entities.WithholdingTaxRate, error)
	DeleteWithholdingTaxRate(ctx context.Context, withholdingTaxRateID int64) (*entities.WithholdingTaxRate, error)
	DownloadWithholdingTaxRates(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.WithholdingTaxRateList, error)
	FilterWithholdingTaxRates(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.WithholdingTaxRateList, error)
	FindWithholdingTaxRateByID(ctx context.Context, withholdingTaxRateID int64) (*entities.WithholdingTaxRate, error)
	GetWithholdingTaxRateByName(ctx context.Context, withholdingTaxRateNumber string) (*entities.WithholdingTaxRate, error)
	SaveWithholdingTaxRate(ctx context.Context, operations txns_db.TransactionsSQLOperations, withholdingTaxRate *entities.WithholdingTaxRate) error
	UpdateWithholdingTaxRate(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateWithholdingTaxRateForm) (*entities.WithholdingTaxRate, error)
}

type AppWithholdingTaxRateService struct {
	taxCategoryRepository        repos.TaxCategoryRepository
	taxTypeRepository            repos.TaxTypeRepository
	userRepository               repos.UserRepository
	withholdingTaxRateRepository repos.WithholdingTaxRateRepository
}

func NewWithholdingTaxRateService(
	taxCategoryRepository repos.TaxCategoryRepository,
	taxTypeRepository repos.TaxTypeRepository,
	userRepository repos.UserRepository,
	withholdingTaxRateRepo repos.WithholdingTaxRateRepository,
) WithholdingTaxRateService {
	return &AppWithholdingTaxRateService{
		taxCategoryRepository:        taxCategoryRepository,
		taxTypeRepository:            taxTypeRepository,
		userRepository:               userRepository,
		withholdingTaxRateRepository: withholdingTaxRateRepo,
	}
}

func (s *AppWithholdingTaxRateService) CreateWithholdingTaxRate(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateWithholdingTaxRateForm,
) (*entities.WithholdingTaxRate, error) {

	withholdingTaxRate := &entities.WithholdingTaxRate{}

	withholdingTaxRate.Name = form.Name

	err := s.withholdingTaxRateRepository.Save(ctx, withholdingTaxRate)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create withholdingTaxRate with name=[%v]",
			form.Name,
		)
	}

	return withholdingTaxRate, nil
}

func (s *AppWithholdingTaxRateService) DeleteWithholdingTaxRate(
	ctx context.Context,
	withholdingTaxRateID int64,
) (*entities.WithholdingTaxRate, error) {

	withholdingTaxRate, err := s.withholdingTaxRateRepository.FindByID(ctx, withholdingTaxRateID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete withholdingTaxRate by id=[%v]",
			withholdingTaxRateID,
		)
	}

	err = s.withholdingTaxRateRepository.Delete(ctx, withholdingTaxRate)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete withholdingTaxRate by id=[%v]",
			withholdingTaxRateID,
		)
	}

	return withholdingTaxRate, nil
}

func (s *AppWithholdingTaxRateService) DownloadWithholdingTaxRates(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.WithholdingTaxRateList, error) {

	withholdingTaxRateList := &entities.WithholdingTaxRateList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.withholdingTaxRateRepository.CountWithholdingTaxRates(ctx, filter)
	if err != nil {
		return withholdingTaxRateList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count withholdingTaxRates for user=[%v]",
			tokenInfo.UserID,
		)
	}

	withholdingTaxRates, err := s.withholdingTaxRateRepository.FilterWithholdingTaxRates(ctx, operations, filter)
	if err != nil {
		return withholdingTaxRateList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter withholdingTaxRates for user=[%v]",
			tokenInfo.UserID,
		)
	}

	withholdingTaxRateList.WithholdingTaxRates = withholdingTaxRates

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	withholdingTaxRateList.Pagination = pagination

	// utils.CreateAndAppendWithholdingTaxRatesDataToExcelFile(filePath, withholdingTaxRates)

	return withholdingTaxRateList, nil
}

func (s *AppWithholdingTaxRateService) FilterWithholdingTaxRates(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.WithholdingTaxRateList, error) {

	withholdingTaxRateList := &entities.WithholdingTaxRateList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.withholdingTaxRateRepository.CountWithholdingTaxRates(ctx, filter)
	if err != nil {
		return withholdingTaxRateList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count withholdingTaxRates for user=[%v]",
			tokenInfo.UserID,
		)
	}

	withholdingTaxRates, err := s.withholdingTaxRateRepository.FilterWithholdingTaxRates(ctx, operations, filter)
	if err != nil {
		return withholdingTaxRateList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter withholdingTaxRates for user=[%v]",
			tokenInfo.UserID,
		)
	}

	withholdingTaxRateList.WithholdingTaxRates = withholdingTaxRates

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	withholdingTaxRateList.Pagination = pagination

	return withholdingTaxRateList, nil
}

func (s *AppWithholdingTaxRateService) FindWithholdingTaxRateByID(
	ctx context.Context,
	withholdingTaxRateID int64,
) (*entities.WithholdingTaxRate, error) {

	withholdingTaxRate, err := s.withholdingTaxRateRepository.FindByID(ctx, withholdingTaxRateID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find withholdingTaxRate by id=[%v]",
			withholdingTaxRateID,
		)
	}

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, withholdingTaxRate.TaxCategoryID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategory by id=[%v]",
			withholdingTaxRate.TaxCategoryID,
		)
	}

	withholdingTaxRate.TaxCategory = taxCategory

	applicableTaxType, err := s.taxTypeRepository.FindByID(ctx, taxCategory.ApplicableTaxTypeID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxType by id=[%v]",
			taxCategory.ApplicableTaxTypeID,
		)
	}

	taxCategory.ApplicableTaxType = applicableTaxType

	return withholdingTaxRate, nil
}

func (s *AppWithholdingTaxRateService) GetWithholdingTaxRateByName(
	ctx context.Context,
	withholdingTaxRateName string,
) (*entities.WithholdingTaxRate, error) {

	withholdingTaxRate, err := s.withholdingTaxRateRepository.FindByName(ctx, withholdingTaxRateName)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find withholdingTaxRate by name=[%v]",
			withholdingTaxRateName,
		)
	}

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, withholdingTaxRate.TaxCategoryID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategory by id=[%v]",
			withholdingTaxRate.TaxCategoryID,
		)
	}

	withholdingTaxRate.TaxCategory = taxCategory

	applicableTaxType, err := s.taxTypeRepository.FindByID(ctx, taxCategory.ApplicableTaxTypeID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxType by id=[%v]",
			taxCategory.ApplicableTaxTypeID,
		)
	}

	taxCategory.ApplicableTaxType = applicableTaxType

	return withholdingTaxRate, nil
}

func (s *AppWithholdingTaxRateService) SaveWithholdingTaxRate(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	withholdingTaxRate *entities.WithholdingTaxRate,
) error {

	err := s.withholdingTaxRateRepository.Save(ctx, withholdingTaxRate)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save withholdingTaxRate by name=[%v]",
			withholdingTaxRate.Name,
		)
	}

	return nil
}

func (s *AppWithholdingTaxRateService) UpdateWithholdingTaxRate(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	withholdingTaxRateID int64,
	form *forms.UpdateWithholdingTaxRateForm,
) (*entities.WithholdingTaxRate, error) {

	withholdingTaxRate, err := s.withholdingTaxRateRepository.FindByID(ctx, withholdingTaxRateID)
	if err != nil {
		return withholdingTaxRate, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update withholdingTaxRate by id=[%v]",
			withholdingTaxRateID,
		)
	}

	if form.Name > "" {
		withholdingTaxRate.Name = form.Name
	}

	err = s.withholdingTaxRateRepository.Save(ctx, withholdingTaxRate)
	if err != nil {
		return withholdingTaxRate, err
	}

	return withholdingTaxRate, nil
}
