package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"errors"

	txns_db "compliance-and-risk-management-backend/app/database"

	"gopkg.in/guregu/null.v3"
)

type UserIncomeService interface {
	CreateUserIncome(ctx context.Context, operations txns_db.TransactionsSQLOperations, userIncome *forms.CreateUserIncomeForm) (*entities.UserIncome, error)
	DeleteUserIncome(ctx context.Context, userIncomeID int64) (*entities.UserIncome, error)
	DownloadUserIncomes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.UserIncomeList, error)
	FilterUserIncomes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.UserIncomeList, error)
	FindUserIncomeByID(ctx context.Context, userIncomeID int64) (*entities.UserIncome, error)
	UpdateUserIncome(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateUserIncomeForm) (*entities.UserIncome, error)
}

type AppUserIncomeService struct {
	taxCategoryRepository repos.TaxCategoryRepository
	userIncomeRepository  repos.UserIncomeRepository
}

func NewUserIncomeService(
	taxCategoryRepository repos.TaxCategoryRepository,
	userIncomeRepo repos.UserIncomeRepository,
) UserIncomeService {
	return &AppUserIncomeService{
		taxCategoryRepository: taxCategoryRepository,
		userIncomeRepository:  userIncomeRepo,
	}
}

func (s *AppUserIncomeService) CreateUserIncome(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateUserIncomeForm,
) (*entities.UserIncome, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	var startDate, endDate null.Time

	if form.StartDate != "" {
		startDateTime, err := utils.ConvertStringDateToTime(form.StartDate)
		if err != nil {
			return nil, apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to convert start date=[%v] to time",
				form.StartDate,
			)
		}

		startDate = null.TimeFrom(startDateTime)
	}

	if form.EndDate != "" {
		endDateTime, err := utils.ConvertStringDateToTime(form.EndDate)
		if err != nil {
			return nil, apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to convert end date=[%v] to time",
				form.EndDate,
			)
		}

		endDate = null.TimeFrom(endDateTime)
	}

	userIncome := &entities.UserIncome{
		Amount:               form.Amount,
		Currency:             "KES",
		Description:          form.Description,
		TaxCategoryID:        form.TaxCategoryID,
		MerchantName:         form.MerchantName,
		MerchantPIN:          form.MerchantPIN,
		PaymentFrequency:     entities.PaymentFrequency(form.PaymentFrequency),
		StartDate:            startDate,
		EndDate:              endDate,
		UserID:               tokenInfo.UserID,
		WithholdingTaxRate:   form.WithholdingTaxRate,
		WithholdingTaxAmount: form.WithholdingTaxAmount,
	}

	err := s.userIncomeRepository.Save(ctx, userIncome)
	if err != nil {
		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create userIncome with description=[%v]",
			form.Description,
		)
	}

	return userIncome, nil
}

func (s *AppUserIncomeService) DeleteUserIncome(
	ctx context.Context,
	userIncomeID int64,
) (*entities.UserIncome, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userIncome, err := s.userIncomeRepository.FindByUserAndID(ctx, tokenInfo.UserID, userIncomeID)
	if err != nil {
		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete userIncome by id=[%v]",
			userIncomeID,
		)
	}

	err = s.userIncomeRepository.Delete(ctx, userIncome)
	if err != nil {
		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete userIncome by id=[%v]",
			userIncomeID,
		)
	}

	return userIncome, nil
}

func (s *AppUserIncomeService) DownloadUserIncomes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.UserIncomeList, error) {

	userIncomeList := &entities.UserIncomeList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	utils.Log.Infof("filtering user expenses for user=[%v]", tokenInfo.UserID)
	filter.UserID = tokenInfo.UserID

	count, err := s.userIncomeRepository.CountUserIncomes(ctx, filter)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count userIncomes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userIncomes, err := s.userIncomeRepository.FilterUserIncomes(ctx, operations, filter)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter userIncomes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userIncomesMap := make(map[int64]*entities.UserIncome)
	taxCategoryIds := []int64{}
	for _, userIncome := range userIncomes {
		taxCategoryIds = append(taxCategoryIds, userIncome.TaxCategoryID)
		userIncomesMap[userIncome.ID] = userIncome
	}

	taxCategorys, err := s.taxCategoryRepository.FindByIDs(ctx, operations, taxCategoryIds)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategorys by ids=[%v]",
			taxCategoryIds,
		)
	}

	taxCategoryMap := make(map[int64]*entities.TaxCategory)
	for _, taxCategory := range taxCategorys {
		taxCategoryMap[taxCategory.ID] = taxCategory
	}

	for _, userIncome := range userIncomes {
		if taxCategory, ok := taxCategoryMap[userIncome.TaxCategoryID]; ok {
			userIncome.TaxCategory = taxCategory
		}
	}

	userIncomeList.UserIncomes = userIncomes

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	userIncomeList.Pagination = pagination

	// utils.CreateAndAppendUserIncomesDataToExcelFile(filePath, userIncomes)

	return userIncomeList, nil
}

func (s *AppUserIncomeService) FilterUserIncomes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.UserIncomeList, error) {

	userIncomeList := &entities.UserIncomeList{}

	pagination := &entities.Pagination{
		Count: 0,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	filter.UserID = tokenInfo.UserID

	count, err := s.userIncomeRepository.CountUserIncomes(ctx, filter)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count userIncomes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	if count == 0 {
		utils.Log.Infof("no user expenses found")
		pagination.Count = count
		userIncomeList.Pagination = pagination
		return userIncomeList, nil
	}

	userIncomes, err := s.userIncomeRepository.FilterUserIncomes(ctx, operations, filter)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter userIncomes for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userIncomesMap := make(map[int64]*entities.UserIncome)
	taxCategoryIds := []int64{}
	for _, userIncome := range userIncomes {
		taxCategoryIds = append(taxCategoryIds, userIncome.TaxCategoryID)
		userIncomesMap[userIncome.ID] = userIncome
	}

	taxCategorys, err := s.taxCategoryRepository.FindByIDs(ctx, operations, taxCategoryIds)
	if err != nil {
		return userIncomeList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategorys by ids=[%v]",
			taxCategoryIds,
		)
	}

	taxCategoryMap := make(map[int64]*entities.TaxCategory)
	for _, taxCategory := range taxCategorys {
		taxCategoryMap[taxCategory.ID] = taxCategory
	}

	for _, userIncome := range userIncomes {
		if taxCategory, ok := taxCategoryMap[userIncome.TaxCategoryID]; ok {
			userIncome.TaxCategory = taxCategory
		}
	}

	userIncomeList.UserIncomes = userIncomes

	pagination.Count = count
	userIncomeList.Pagination = pagination

	return userIncomeList, nil
}

func (s *AppUserIncomeService) FindUserIncomeByID(
	ctx context.Context,
	userIncomeID int64,
) (*entities.UserIncome, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userIncome, err := s.userIncomeRepository.FindByUserAndID(ctx, tokenInfo.UserID, userIncomeID)
	if err != nil {
		if utils.IsErrNoRows(err) {
			utils.Log.Infof("userIncome not found by id=[%v]", userIncomeID)

			return userIncome, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("userIncome not found"),
				utils.ErrorCodeResourceNotFound,
				"userIncome not found",
				"userIncome=[%v] not found",
				userIncomeID,
			)
		}

		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find userIncome by id=[%v]",
			userIncomeID,
		)
	}

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, userIncome.TaxCategoryID)
	if err != nil {
		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategory by id=[%v]",
			userIncome.TaxCategoryID,
		)
	}

	userIncome.TaxCategory = taxCategory

	return userIncome, nil
}

func (s *AppUserIncomeService) UpdateUserIncome(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userIncomeID int64,
	form *forms.UpdateUserIncomeForm,
) (*entities.UserIncome, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userIncome, err := s.userIncomeRepository.FindByUserAndID(ctx, tokenInfo.UserID, userIncomeID)
	if err != nil {
		return userIncome, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update userIncome by id=[%v]",
			userIncomeID,
		)
	}

	if form.Amount > 0 {
		userIncome.Amount = form.Amount
	}

	if form.Currency != "" {
		userIncome.Currency = form.Currency
	}

	if form.Description > "" {
		userIncome.Description = form.Description
	}

	if form.TaxCategoryID > 0 {
		userIncome.TaxCategoryID = form.TaxCategoryID
	}

	if form.MerchantName > "" {
		userIncome.MerchantName = form.MerchantName
	}

	if form.MerchantPIN > "" {
		userIncome.MerchantPIN = form.MerchantPIN
	}

	if form.WithholdingTaxRate > 0 {
		userIncome.WithholdingTaxRate = form.WithholdingTaxRate
	}

	if form.WithholdingTaxAmount > 0 {
		userIncome.WithholdingTaxAmount = form.WithholdingTaxAmount
	}

	var startDate, endDate null.Time

	if form.StartDate != "" {
		startDateTime, err := utils.ConvertStringDateToTime(form.StartDate)
		if err != nil {
			return nil, apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to convert start date=[%v] to time",
				form.StartDate,
			)
		}

		startDate = null.TimeFrom(startDateTime)
	}

	if form.EndDate != "" {
		endDateTime, err := utils.ConvertStringDateToTime(form.EndDate)
		if err != nil {
			return nil, apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to convert end date=[%v] to time",
				form.EndDate,
			)
		}

		endDate = null.TimeFrom(endDateTime)
	}

	if startDate.Valid {
		userIncome.StartDate = startDate
	}

	if endDate.Valid {
		userIncome.EndDate = endDate
	}

	err = s.userIncomeRepository.Save(ctx, userIncome)
	if err != nil {
		return userIncome, err
	}

	return userIncome, nil
}
