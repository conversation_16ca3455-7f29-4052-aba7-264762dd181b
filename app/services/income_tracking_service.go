package services

import (
	"context"
	"errors"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/repos"
	"time"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type IncomeTrackingService interface {
	CreateIncomeTracking(ctx context.Context, operations txns_db.TransactionsSQLOperations, incomeTracking *forms.CreateIncomeTrackingForm) (*entities.IncomeTracking, error)
	DownloadIncomeTrackings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string, storeId int64) (*entities.IncomeTrackingList, error)
	FilterIncomeTrackings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.IncomeTrackingList, error)
	FindIncomeTrackingByID(ctx context.Context, incomeTrackingID int64) (*entities.IncomeTracking, error)
	GetIncomeTrackingByDate(ctx context.Context, trackingDate time.Time) (*entities.IncomeTracking, error)
	UpdateIncomeTracking(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateIncomeTrackingForm) (*entities.IncomeTracking, error)
}

type AppIncomeTrackingService struct {
	incomeTrackingRepository repos.IncomeTrackingRepository   
}

func NewIncomeTrackingService(
	incomeTrackingRepo repos.IncomeTrackingRepository,  
) IncomeTrackingService {
	return &AppIncomeTrackingService{
		incomeTrackingRepository: incomeTrackingRepo,  
	}
}

func (s *AppIncomeTrackingService) CreateIncomeTracking(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateIncomeTrackingForm,
) (*entities.IncomeTracking, error) {

	incomeTracking := &entities.IncomeTracking{
		Balance:      form.Balance,
		TrackingDate: form.TrackingDate.Time,
	}

	err := s.incomeTrackingRepository.Save(ctx, operations, incomeTracking)
	if err != nil {
		return incomeTracking, err
	}

	return incomeTracking, nil
}

func (s *AppIncomeTrackingService) DownloadIncomeTrackings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
	storeId int64,
) (*entities.IncomeTrackingList, error) {

	incomeTrackingList := &entities.IncomeTrackingList{}

	count, err := s.incomeTrackingRepository.CountIncomeTrackings(ctx, filter)
	if err != nil {
		return incomeTrackingList, err
	}

	incomeTrackings, err := s.incomeTrackingRepository.FilterIncomeTrackings(ctx, operations, filter)
	if err != nil {
		return incomeTrackingList, err
	}

	incomeTrackingList.IncomeTrackings = incomeTrackings

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	incomeTrackingList.Pagination = pagination

	// utils.CreateAndAppendIncomeTrackingsDataToExcelFile(filePath, incomeTrackings)

	return incomeTrackingList, nil
}

func (s *AppIncomeTrackingService) FilterIncomeTrackings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.IncomeTrackingList, error) {

	incomeTrackingList := &entities.IncomeTrackingList{}

	count, err := s.incomeTrackingRepository.CountIncomeTrackings(ctx, filter)
	if err != nil {
		logger.Errorf("failed to retrieve incomeTrackings count, err=[%v]\n", err)
		return incomeTrackingList, err
	}

	incomeTrackings, err := s.incomeTrackingRepository.FilterIncomeTrackings(ctx, operations, filter)
	if err != nil {
		logger.Errorf("failed to filter incomeTrackings, err=[%v]\n", err)
		return incomeTrackingList, err
	}

	incomeTrackingList.IncomeTrackings = incomeTrackings

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	incomeTrackingList.Pagination = pagination

	return incomeTrackingList, nil
}

func (s *AppIncomeTrackingService) FindIncomeTrackingByID(
	ctx context.Context,
	incomeTrackingID int64,
) (*entities.IncomeTracking, error) {

	incomeTracking, err := s.incomeTrackingRepository.FindByID(ctx, incomeTrackingID)
	if err != nil {
		return incomeTracking, err
	}

	return incomeTracking, nil
}

func (s *AppIncomeTrackingService) GetIncomeTrackingByDate(
	ctx context.Context,
	trackingDate time.Time,
) (*entities.IncomeTracking, error) {

	incomeTracking, err := s.incomeTrackingRepository.FindByDate(ctx, trackingDate)
	if err != nil {
		return incomeTracking, err
	}

	return incomeTracking, nil
}

func (s *AppIncomeTrackingService) UpdateIncomeTracking(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	incomeTrackingID int64,
	form *forms.UpdateIncomeTrackingForm,
) (*entities.IncomeTracking, error) {

	incomeTracking, err := s.incomeTrackingRepository.FindByID(ctx, incomeTrackingID)
	if err != nil {
		return incomeTracking, errors.New("unable to find incomeTracking")
	}

	if form.Balance > 0 {
		incomeTracking.Balance = form.Balance
	}

	if form.TrackingDate != nil {
		incomeTracking.TrackingDate = form.TrackingDate.Time
	}

	err = s.incomeTrackingRepository.Save(ctx, operations, incomeTracking)
	if err != nil {
		return incomeTracking, err
	}

	return incomeTracking, nil
}
