package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type ExpenseTypeService interface {
	CreateExpenseType(ctx context.Context, operations txns_db.TransactionsSQLOperations, expense *forms.CreateExpenseTypeForm) (*entities.ExpenseType, error)
	DeleteExpenseType(ctx context.Context, expenseID int64) (*entities.ExpenseType, error)
	DownloadExpenseTypes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.ExpenseTypeList, error)
	FilterExpenseTypes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.ExpenseTypeList, error)
	FindExpenseTypeByID(ctx context.Context, expenseID int64) (*entities.ExpenseType, error)
	GetExpenseTypeByName(ctx context.Context, expenseNumber string) (*entities.ExpenseType, error)
	SaveExpenseType(ctx context.Context, operations txns_db.TransactionsSQLOperations, expense *entities.ExpenseType) error
	UpdateExpenseType(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateExpenseTypeForm) (*entities.ExpenseType, error)
}

type AppExpenseTypeService struct {
	expenseRepository repos.ExpenseTypeRepository
}

func NewExpenseTypeService(
	expenseRepo repos.ExpenseTypeRepository,
) ExpenseTypeService {
	return &AppExpenseTypeService{
		expenseRepository: expenseRepo,
	}
}

func (s *AppExpenseTypeService) CreateExpenseType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateExpenseTypeForm,
) (*entities.ExpenseType, error) {

	expense := &entities.ExpenseType{}

	expense.Name = form.Name
	expense.Description = form.Description

	err := s.expenseRepository.Save(ctx, expense)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create expense with name=[%v]",
			form.Name,
		)
	}

	return expense, nil
}

func (s *AppExpenseTypeService) DeleteExpenseType(
	ctx context.Context,
	expenseID int64,
) (*entities.ExpenseType, error) {

	expense, err := s.expenseRepository.FindByID(ctx, expenseID)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete expense by id=[%v]",
			expenseID,
		)
	}

	err = s.expenseRepository.Delete(ctx, expense)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete expense by id=[%v]",
			expenseID,
		)
	}

	return expense, nil
}

func (s *AppExpenseTypeService) DownloadExpenseTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.ExpenseTypeList, error) {

	expenseList := &entities.ExpenseTypeList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.expenseRepository.CountExpenseTypes(ctx, filter)
	if err != nil {
		return expenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count expenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	expenses, err := s.expenseRepository.FilterExpenseTypes(ctx, operations, filter)
	if err != nil {
		return expenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter expenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	expenseList.ExpenseTypes = expenses

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	expenseList.Pagination = pagination

	// utils.CreateAndAppendExpenseTypesDataToExcelFile(filePath, expenses)

	return expenseList, nil
}

func (s *AppExpenseTypeService) FilterExpenseTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ExpenseTypeList, error) {

	expenseList := &entities.ExpenseTypeList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.expenseRepository.CountExpenseTypes(ctx, filter)
	if err != nil {
		return expenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count expenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	expenses, err := s.expenseRepository.FilterExpenseTypes(ctx, operations, filter)
	if err != nil {
		return expenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter expenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	expenseList.ExpenseTypes = expenses

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	expenseList.Pagination = pagination

	return expenseList, nil
}

func (s *AppExpenseTypeService) FindExpenseTypeByID(
	ctx context.Context,
	expenseID int64,
) (*entities.ExpenseType, error) {

	expense, err := s.expenseRepository.FindByID(ctx, expenseID)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find expense by id=[%v]",
			expenseID,
		)
	}

	return expense, nil
}

func (s *AppExpenseTypeService) GetExpenseTypeByName(
	ctx context.Context,
	expenseName string,
) (*entities.ExpenseType, error) {

	expense, err := s.expenseRepository.FindByName(ctx, expenseName)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find expense by name=[%v]",
			expenseName,
		)
	}

	return expense, nil
}

func (s *AppExpenseTypeService) SaveExpenseType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	expense *entities.ExpenseType,
) error {

	err := s.expenseRepository.Save(ctx, expense)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save expense by name=[%v]",
			expense.Name,
		)
	}

	return nil
}

func (s *AppExpenseTypeService) UpdateExpenseType(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	expenseID int64,
	form *forms.UpdateExpenseTypeForm,
) (*entities.ExpenseType, error) {

	expense, err := s.expenseRepository.FindByID(ctx, expenseID)
	if err != nil {
		return expense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update expense by id=[%v]",
			expenseID,
		)
	}

	if form.Name > "" {
		expense.Name = form.Name
	}

	if form.Description > "" {
		expense.Description = form.Description
	}

	err = s.expenseRepository.Save(ctx, expense)
	if err != nil {
		return expense, err
	}

	return expense, nil
}
