package services

import (
	"context"
	"errors"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type ReceivingService interface {
	CreateReceiving(ctx context.Context, operations txns_db.TransactionsSQLOperations, item *forms.CreateReceivingForm) (*entities.Receiving, error)
	DownloadReceivings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.ReceivingsList, error)
	FilterReceivings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.ReceivingsList, error)
	FindReceivingByID(ctx context.Context, itemID int64) (*entities.Receiving, error)
	UpdateReceiving(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateReceivingForm) (*entities.Receiving, error)
}

type AppReceivingService struct {
	itemQuantityRepository repos.ItemQuantityRepository
	itemRepository         repos.ItemRepository
	organizationRepository repos.OrganizationRepository
	receivingRepository    repos.ReceivingRepository
	userRepository         repos.UserRepository
}

func NewReceivingService(
	itemQuantityRepository repos.ItemQuantityRepository,
	itemRepository repos.ItemRepository,
	organizationRepository repos.OrganizationRepository,
	receivingRepository repos.ReceivingRepository,
	userRepository repos.UserRepository,
) ReceivingService {
	return &AppReceivingService{
		itemQuantityRepository: itemQuantityRepository,
		itemRepository:         itemRepository,
		organizationRepository: organizationRepository,
		receivingRepository:    receivingRepository,
		userRepository:         userRepository,
	}
}

func (s *AppReceivingService) CreateReceiving(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateReceivingForm,
) (*entities.Receiving, error) {

	receiving := &entities.Receiving{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Receiving{}, err
	}

	organization, err := s.organizationRepository.FindByID(ctx, user.OrganizationID)
	if err != nil {
		return receiving, errors.New("organization by id not found")
	}

	if form.OrganizationID != organization.ID {
		return receiving, errors.New("cannot create receiving for different organization")
	}

	item, err := s.itemRepository.FindByID(ctx, form.ItemID, form.ItemID)
	if err != nil {
		return receiving, errors.New("item with id not found")
	}

	receiving.ItemID = item.ID
	receiving.Quantity = form.Quantity
	receiving.CostPrice = form.CostPrice
	receiving.Total = form.Total
	receiving.ReceivedAt = form.ReceivedAt
	receiving.SupplierID = form.SupplierID
	receiving.Description = form.Description
	receiving.CreatedBy = user.ID
	receiving.OrganizationID = organization.ID

	err = s.receivingRepository.Save(ctx, receiving)
	if err != nil {
		return receiving, err
	}

	return receiving, nil
}

func (s *AppReceivingService) DownloadReceivings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.ReceivingsList, error) {

	itemList := &entities.ReceivingsList{}

	count := s.receivingRepository.CountReceivings(ctx, filter)

	items, err := s.receivingRepository.FilterReceivings(ctx, operations, filter)
	if err != nil {
		return itemList, err
	}

	itemList.Receivings = items

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	itemList.Pagination = pagination

	// TODO: Implement
	// utils.CreateAndAppendReceivingsDataToExcelFile(filePath, items)

	return itemList, nil
}

func (s *AppReceivingService) FilterReceivings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ReceivingsList, error) {

	receivingsList := &entities.ReceivingsList{}

	count := s.receivingRepository.CountReceivings(ctx, filter)

	receivings, err := s.receivingRepository.FilterReceivings(ctx, operations, filter)
	if err != nil && !utils.IsErrNoRows(err) {
		return receivingsList, err
	}

	receivingsList.Receivings = receivings

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	receivingsList.Pagination = pagination

	return receivingsList, nil
}

func (s *AppReceivingService) FindReceivingByID(
	ctx context.Context,
	receivingID int64,
) (*entities.Receiving, error) {

	receiving, err := s.receivingRepository.FindByID(ctx, receivingID)
	if err != nil {
		return receiving, err
	}

	return receiving, nil
}

func (s *AppReceivingService) UpdateReceiving(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	receivingID int64,
	form *forms.UpdateReceivingForm,
) (*entities.Receiving, error) {

	receiving, err := s.receivingRepository.FindByID(ctx, receivingID)
	if err != nil {
		return receiving, errors.New("unable to find receiving")
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Receiving{}, err
	}

	if receiving.OrganizationID != user.OrganizationID {
		return &entities.Receiving{}, errors.New("user does not have permission to update organization receiving")
	}

	receiving.Quantity = form.Quantity
	receiving.Description = form.Description
	receiving.ReceivedAt = form.ReceivedAt
	receiving.CreatedBy = user.ID
	receiving.SupplierID = form.SupplierID

	if form.CostPrice > 0 {
		receiving.CostPrice = form.CostPrice
	}

	if form.Total > 0 {
		receiving.Total = form.Total
	}

	err = s.receivingRepository.Save(ctx, receiving)
	if err != nil {
		return receiving, err
	}

	return receiving, nil
}
