package services

import (
	"context"

	txns_db "compliance-and-risk-management-backend/app/database"
	coreEntities "compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/utils"
)

type (
	PaymentTransactionsService interface {
		RegisterClientMPesaCallbackURL(ctx context.Context, form *forms.RegisterClientMPesaURLsForm) error
		RegisterMPesaCallbackURL(ctx context.Context, form *forms.RegisterMPesaURLsForm) error
		RequestMPesaPaybillBalance(ctx context.Context, txnsDB txns_db.TransactionsDB) (*coreEntities.MPesaAccountBalanceResponse, error)
	}

	AppPaymentTransactionsService struct {
		mpesa providers.MPesa
	}
)

func NewPaymentTransactionsService(
	mpesa providers.MPesa,
) PaymentTransactionsService {
	return &AppPaymentTransactionsService{
		mpesa: mpesa,
	}
}

func (s *AppPaymentTransactionsService) RegisterClientMPesaCallbackURL(
	ctx context.Context,
	form *forms.RegisterClientMPesaURLsForm,
) error {

	apiBaseURL := "https://optimusapi.strutstechnology.com/api"
	mpesaAPIURL := "https://api.safaricom.co.ke"

	mpesa := providers.NewMPesaWithCrerdentials(
		apiBaseURL,
		mpesaAPIURL,
		form.ConsumerKey,
		form.ConsumerSecret,
		"",
		form.ShortCode,
	)

	registerUrlForm := &forms.RegisterURLForm{
		ConfirmationURL: form.ConfirmationURL,
		ValidationURL:   form.ValidationURL,
		ResponseType:    "",
		ShortCode:       form.ShortCode,
	}

	err := mpesa.RegisterURL(registerUrlForm)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to register URL on M-Pesa, err=[%v]",
			err,
		)
	}

	return nil
}

func (s *AppPaymentTransactionsService) RegisterMPesaCallbackURL(
	ctx context.Context,
	form *forms.RegisterMPesaURLsForm,
) error {

	registerUrlForm := &forms.RegisterURLForm{
		ConfirmationURL: form.ConfirmationURL,
		ValidationURL:   form.ValidationURL,
	}

	err := s.mpesa.RegisterURL(registerUrlForm)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to register URL on M-Pesa, err=[%v]",
			err,
		)
	}

	return nil
}

func (s *AppPaymentTransactionsService) RequestMPesaPaybillBalance(
	ctx context.Context,
	txnsDB txns_db.TransactionsDB,
) (*coreEntities.MPesaAccountBalanceResponse, error) {

	res, err := s.mpesa.AccountBalance()
	if err != nil {
		return res, utils.NewError(
			err,
			"Failed to send Safaricom paybill bal request, response=[%v]",
			res,
		)
	}

	return res, nil
}
