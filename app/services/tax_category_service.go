package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type TaxCategoryService interface {
	CreateTaxCategory(ctx context.Context, operations txns_db.TransactionsSQLOperations, taxCategory *forms.CreateTaxCategoryForm) (*entities.TaxCategory, error)
	DeleteTaxCategory(ctx context.Context, taxCategoryID int64) (*entities.TaxCategory, error)
	DownloadTaxCategories(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.TaxCategoryList, error)
	FilterTaxCategories(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.TaxCategoryList, error)
	FindTaxCategoryByID(ctx context.Context, taxCategoryID int64) (*entities.TaxCategory, error)
	GetTaxCategoryByName(ctx context.Context, taxCategoryNumber string) (*entities.TaxCategory, error)
	SaveTaxCategory(ctx context.Context, operations txns_db.TransactionsSQLOperations, taxCategory *entities.TaxCategory) error
	UpdateTaxCategory(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateTaxCategoryForm) (*entities.TaxCategory, error)
}

type AppTaxCategoryService struct {
	taxCategoryRepository repos.TaxCategoryRepository
	taxTypeRepository     repos.TaxTypeRepository
	userRepository        repos.UserRepository
}

func NewTaxCategoryService(
	taxCategoryRepo repos.TaxCategoryRepository,
	taxTypeRepository repos.TaxTypeRepository,
	userRepository repos.UserRepository,
) TaxCategoryService {
	return &AppTaxCategoryService{
		taxCategoryRepository: taxCategoryRepo,
		taxTypeRepository:     taxTypeRepository,
		userRepository:        userRepository,
	}
}

func (s *AppTaxCategoryService) CreateTaxCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateTaxCategoryForm,
) (*entities.TaxCategory, error) {

	taxCategory := &entities.TaxCategory{}

	taxCategory.IncomeSource = form.IncomeSource

	err := s.taxCategoryRepository.Save(ctx, taxCategory)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create taxCategory with name=[%v]",
			form.IncomeSource,
		)
	}

	return taxCategory, nil
}

func (s *AppTaxCategoryService) DeleteTaxCategory(
	ctx context.Context,
	taxCategoryID int64,
) (*entities.TaxCategory, error) {

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, taxCategoryID)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete taxCategory by id=[%v]",
			taxCategoryID,
		)
	}

	err = s.taxCategoryRepository.Delete(ctx, taxCategory)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete taxCategory by id=[%v]",
			taxCategoryID,
		)
	}

	return taxCategory, nil
}

func (s *AppTaxCategoryService) DownloadTaxCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.TaxCategoryList, error) {

	taxCategoryList := &entities.TaxCategoryList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.taxCategoryRepository.CountTaxCategories(ctx, filter)
	if err != nil {
		return taxCategoryList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count taxCategorys for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxCategories, err := s.taxCategoryRepository.FilterTaxCategories(ctx, operations, filter)
	if err != nil {
		return taxCategoryList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter taxCategories for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxCategoryList.TaxCategories = taxCategories

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	taxCategoryList.Pagination = pagination

	// utils.CreateAndAppendTaxCategorysDataToExcelFile(filePath, taxCategorys)

	return taxCategoryList, nil
}

func (s *AppTaxCategoryService) FilterTaxCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.TaxCategoryList, error) {

	taxCategoryList := &entities.TaxCategoryList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.taxCategoryRepository.CountTaxCategories(ctx, filter)
	if err != nil {
		return taxCategoryList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count taxCategories for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxCategories, err := s.taxCategoryRepository.FilterTaxCategories(ctx, operations, filter)
	if err != nil {
		return taxCategoryList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter taxCategories for user=[%v]",
			tokenInfo.UserID,
		)
	}

	taxCategoryList.TaxCategories = taxCategories

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	taxCategoryList.Pagination = pagination

	return taxCategoryList, nil
}

func (s *AppTaxCategoryService) FindTaxCategoryByID(
	ctx context.Context,
	taxCategoryID int64,
) (*entities.TaxCategory, error) {

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, taxCategoryID)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategory by id=[%v]",
			taxCategoryID,
		)
	}

	taxType, err := s.taxTypeRepository.FindByID(ctx, taxCategory.ApplicableTaxTypeID)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxType by id=[%v]",
			taxCategory.ApplicableTaxTypeID,
		)
	}

	taxCategory.ApplicableTaxType = taxType

	return taxCategory, nil
}

func (s *AppTaxCategoryService) GetTaxCategoryByName(
	ctx context.Context,
	taxCategoryName string,
) (*entities.TaxCategory, error) {

	taxCategory, err := s.taxCategoryRepository.FindByName(ctx, taxCategoryName)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find taxCategory by name=[%v]",
			taxCategoryName,
		)
	}

	return taxCategory, nil
}

func (s *AppTaxCategoryService) SaveTaxCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	taxCategory *entities.TaxCategory,
) error {

	err := s.taxCategoryRepository.Save(ctx, taxCategory)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save taxCategory by name=[%v]",
			taxCategory.IncomeSource,
		)
	}

	return nil
}

func (s *AppTaxCategoryService) UpdateTaxCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	taxCategoryID int64,
	form *forms.UpdateTaxCategoryForm,
) (*entities.TaxCategory, error) {

	taxCategory, err := s.taxCategoryRepository.FindByID(ctx, taxCategoryID)
	if err != nil {
		return taxCategory, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update taxCategory by id=[%v]",
			taxCategoryID,
		)
	}

	if form.IncomeSource > "" {
		taxCategory.IncomeSource = form.IncomeSource
	}

	if form.Description > "" {
		taxCategory.Description = form.Description
	}

	err = s.taxCategoryRepository.Save(ctx, taxCategory)
	if err != nil {
		return taxCategory, err
	}

	return taxCategory, nil
}
