package services

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"errors"
	"strings"
	"time"
)

type (
	SessionService interface {
		FilterUserSessions(ctx context.Context, operations database.TransactionsSQLOperations, userID int64, filter *entities.PaginationFilter) (*entities.SessionList, error)
		FullSession(ctx context.Context, operations database.TransactionsSQLOperations, sessionID int64) (*entities.FullSession, error)
		GetSession(ctx context.Context, operations database.TransactionsSQLOperations, sessionID int64) (*entities.Session, error)
		Login(ctx context.Context, operations database.TransactionsSQLOperations, form *forms.UserLoginForm) (*entities.User, *entities.Session, error)
		Logout(ctx context.Context, operations database.TransactionsSQLOperations, sessionID int64) error
		SaveSession(ctx context.Context, operations database.TransactionsSQLOperations, session *entities.Session) error
	}

	AppSessionService struct {
		applicationRepository     repos.ApplicationRepository
		phoneNumberRepository     repos.PhoneNumberRepository
		sessionRepository         repos.SessionRepository
		userApplicationRepository repos.UserApplicationRepository
		userRepository            repos.UserRepository
	}
)

func NewSessionService(
	applicationRepository repos.ApplicationRepository,
	phoneNumberRepository repos.PhoneNumberRepository,
	sessionRepository repos.SessionRepository,
	userApplicationRepository repos.UserApplicationRepository,
	userRepository repos.UserRepository,
) *AppSessionService {

	return &AppSessionService{
		applicationRepository:     applicationRepository,
		phoneNumberRepository:     phoneNumberRepository,
		sessionRepository:         sessionRepository,
		userApplicationRepository: userApplicationRepository,
		userRepository:            userRepository,
	}
}

func (s *AppSessionService) FilterUserSessions(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) (*entities.SessionList, error) {

	sessionList := &entities.SessionList{}

	if filter.Page < 1 {
		filter.Page = 1
	}

	if filter.Per < 0 || filter.Per > 100 {
		filter.Per = 20
	}

	sessions, err := s.sessionRepository.FilterSessions(ctx, operations, filter)
	if err != nil {
		return sessionList, err
	}

	count, err := s.sessionRepository.CountSessions(ctx, operations, filter)
	if err != nil {
		return sessionList, err
	}

	sessionList.Sessions = sessions
	sessionList.Pagination = entities.NewPagination(count, filter.Page, filter.Per)

	return sessionList, nil
}

func (s *AppSessionService) FullSession(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	sessionID int64,
) (*entities.FullSession, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	session, err := s.sessionRepository.FindFullSession(ctx, operations, tokenInfo.ApplicationType, sessionID)
	if err != nil {
		return session, err
	}

	return session, nil
}

func (s *AppSessionService) GetSession(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	sessionID int64,
) (*entities.Session, error) {

	session, err := s.sessionRepository.FindByID(ctx, operations, sessionID)
	if err != nil {
		return session, err
	}

	return session, nil
}

func (s *AppSessionService) Login(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	form *forms.UserLoginForm,
) (*entities.User, *entities.Session, error) {

	email := strings.TrimSpace(form.Email)
	password := strings.TrimSpace(form.Password)
	if password == "" {
		return nil, nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid password provided"),
			utils.ErrorCodeInvalidForm,
			"Invalid password provided",
			"Invalid password=[%v] provided",
			password,
		)
	}

	session := &entities.Session{}
	var user *entities.User
	var err error

	user, err = s.userRepository.FindByEmail(ctx, operations, email)
	if err != nil {
		return user, session, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("user not found"),
			utils.ErrorCodeResourceNotFound,
			"User with email not found",
			"User with email=[%v] not found",
			email,
		)
	}

	if email == "" {
		return nil, nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid email provided"),
			utils.ErrorCodeInvalidForm,
			"Invalid email provided",
			"Invalid email=[%v] provided",
			email,
		)
	}

	if utils.IsErrNoRows(err) {
		return user, session, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("invalid email"),
			utils.ErrorCodeInvalidCredentials,
			"User details were not found",
			"Failed to search for user with email=[%v]",
			email,
		)
	}

	err = utils.ComparePassword(user.Password, password)
	if err != nil {
		return user, session, utils.NewErrorWithCode(
			errors.New("invalid password"),
			utils.ErrorCodeInvalidCredentials,
			"Failed to validate password for user with email=[%v]",
			email,
		)
	}

	applicationType := ctxhelper.ApplicationType(ctx)

	applicationUser, err := s.applicationRepository.FindByApplicationTypeAndUser(
		ctx,
		operations,
		applicationType,
		user.ID,
	)
	if err != nil && !utils.IsErrNoRows(err) {
		return user, session, utils.NewErrorWithCode(
			errors.New("invalid password"),
			utils.ErrorCodeInvalidCredentials,
			"Failed to validate password for user with email=[%v]",
			email,
		)
	} else if utils.IsErrNoRows(err) && applicationType != entities.ApplicationTypeAdmin {
		application, err := s.applicationRepository.FindByApplicationType(ctx, operations, applicationType)
		if err != nil {
			return user, session, utils.NewErrorWithCode(
				errors.New("invalid password"),
				utils.ErrorCodeInvalidCredentials,
				"Failed to validate password for user with email=[%v]",
				email,
			)
		}

		userApplication := &entities.UserApplication{
			ApplicationID: application.ID,
			UserID:        user.ID,
		}

		err = s.userApplicationRepository.Save(ctx, operations, userApplication)
		if err != nil {
			return user, session, utils.NewErrorWithCode(
				errors.New("invalid password"),
				utils.ErrorCodeInvalidCredentials,
				"Failed to validate password for user with email=[%v]",
				email,
			)
		}

		applicationUser, err = s.applicationRepository.FindByApplicationTypeAndUser(ctx, operations, applicationType, user.ID)
		if err != nil {
			return user, session, utils.NewErrorWithCode(
				errors.New("invalid password"),
				utils.ErrorCodeInvalidCredentials,
				"Failed to validate password for user with email=[%v]",
				email,
			)
		}
	}

	// Set user phone number
	if user.PhoneNumberID != nil {
		phoneNumber, err := s.phoneNumberRepository.FindByID(ctx, operations, *user.PhoneNumberID)
		if err != nil {
			utils.Log.Infof("unable to retrieve user phoneNumber, err=[%v]", err.Error())
		}

		user.PhoneNumber = phoneNumber
	}

	if !user.Status.IsActive() {
		return user, session, utils.NewErrorWithCode(
			errors.New("status not active"),
			utils.ErrorCodeInvalidUserStatus,
			"Failed to login user with id=[%v]",
			user.ID,
		)
	}

	session = &entities.Session{
		ApplicationID:   applicationUser.ID,
		ApplicationType: ctxhelper.ApplicationType(ctx),
		IPAddress:       ctxhelper.IPAddress(ctx),
		LastRefreshedAt: time.Now(),
		UserAgent:       ctxhelper.UserAgent(ctx),
		UserID:          user.ID,
	}

	return user, session, s.SaveSession(ctx, operations, session)
}

func (s *AppSessionService) Logout(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	sessionID int64,
) error {

	// tokenInfo := ctxhelper.TokenInfo(ctx)

	session, err := s.sessionRepository.FindByID(ctx, operations, sessionID)
	if err != nil {
		return utils.NewErrorWithCode(
			errors.New("invalid session"),
			utils.ErrorCodeInvalidCredentials,
			"Failed to find session with id=[%v]",
			sessionID,
		)
	}

	// if utils.Int64Value(session.UserID) != tokenInfo.UserID {
	// 	return utils.NewErrorWithCode(
	// 		errors.New("unauthorized"),
	// 		utils.ErrorCodeUnauthorized,
	// 		"Cannot logout session=[%v] by user=[%v]",
	// 		sessionID,
	// 		tokenInfo.UserID,
	// 	)
	// }

	// if session.DeactivatedAt != nil {
	// 	return nil
	// }

	// session.DeactivatedAt = utils.Time(time.Now())

	err = s.sessionRepository.Save(ctx, operations, session)
	if err != nil {
		return utils.NewErrorWithCode(
			errors.New("invalid session"),
			utils.ErrorCodeInvalidCredentials,
			"Failed to save session with id=[%v]",
			sessionID,
		)
	}

	// if tokenInfo.SessionID != session.ID && session.ClientIdentifier == entities.WebClientIdentifier {
	// 	sessionIDs := []int64{session.ID}
	// 	s.webSocketConnectionService.SendSuccessfulLogoutMessage(ctx, sessionIDs)
	// }

	return nil
}

func (s *AppSessionService) SaveSession(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	session *entities.Session,
) error {

	// Deactivate any existing sessions for application type, android and web on android login
	// clientIdentifiers := []entities.ClientIdentifier{"android", "web"}
	// err := s.sessionRepository.DeactivateSessionsForUserAndApplicationType(ctx, operations, utils.Int64Value(session.UserID), session.ApplicationType, clientIdentifiers)
	// if err != nil {
	// 	return apperr.Wrap(
	// 		err,
	// 	).AddLogMessagef(
	// 		"Failed to deactivate existing user application sessions for user=[%v] and application=[%v]",
	// 		session.UserID,
	// 		session.ApplicationID,
	// 	)
	// }

	// Create new session for user android application
	err := s.sessionRepository.Save(ctx, operations, session)
	if err != nil {
		return utils.NewErrorWithCode(
			errors.New("invalid session"),
			utils.ErrorCodeInvalidCredentials,
			"Failed to find session with id=[%v]",
			session.ID,
		)
	}

	return nil
}
