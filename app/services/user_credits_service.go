package services

import (
	"context"
	"errors"
	"fmt"
	"os"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
)

type (
	UserCreditService interface {
		TopupUserCredits(context.Context, txns_db.TransactionsSQLOperations, string, *forms.UserCreditTopupForm) (*entities.UserCreditTopUpAPIResponse, error)
	}

	AppUserCreditService struct {
		userCreditsRepository repos.UserCreditRepository
		userRepository        repos.UserRepository
	}
)

func NewUserCreditService(
	userCreditsRepository repos.UserCreditRepository,
	userRepository repos.UserRepository,
) UserCreditService {
	return &AppUserCreditService{
		userCreditsRepository: userCreditsRepository,
		userRepository:        userRepository,
	}
}

func (s *AppUserCreditService) TopupUserCredits(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	b2bAPIKey string,
	form *forms.UserCreditTopupForm,
) (*entities.UserCreditTopUpAPIResponse, error) {

	// Validate API Key
	systemB2BAPIKey := os.Getenv("B2B_API_KEY")
	if b2bAPIKey != systemB2BAPIKey {
		return nil, errors.New("invalid b2b api key")
	}

	// Get user by account number
	user, err := s.userRepository.FindByAccountNumber(ctx, operations, form.AccountNumber)
	if err != nil {
		errMessage := fmt.Sprintf("user not found by account number=[%v]", form.AccountNumber)
		return nil, errors.New(errMessage)
	}

	// Get user credits
	userCredits, err := s.userCreditsRepository.FindByUserID(ctx, user.ID)
	if err != nil {
		errMessage := fmt.Sprintf("user credits not found by user id=[%v]", user.ID)
		return nil, errors.New(errMessage)
	}

	// Topup account with the respective amount and discount
	newBalance := userCredits.CreditBalance + form.Amount
	userCredits.CreditBalance = newBalance
	err = s.userCreditsRepository.Save(ctx, operations, userCredits)
	if err != nil {
		errMessage := fmt.Sprintf("failed to update user credit balance=[%v]", newBalance)
		return nil, errors.New(errMessage)
	}

	response := &entities.UserCreditTopUpAPIResponse{
		AccountNumber: form.AccountNumber,
		Amount:        form.Amount,
		CreditBalance: newBalance,
		PhoneNumber:   form.PhoneNumber,
	}

	// TODO: Notify User via SMS and Email of successful topup
	// TODO: Save transaction

	utils.Log.Infof("user credit topup response=[%v]", response)

	return response, nil
}
