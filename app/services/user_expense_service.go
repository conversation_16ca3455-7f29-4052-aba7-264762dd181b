package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"errors"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type UserExpenseService interface {
	CreateUserExpense(ctx context.Context, operations txns_db.TransactionsSQLOperations, userExpense *forms.CreateUserExpenseForm) (*entities.UserExpense, error)
	DeleteUserExpense(ctx context.Context, userExpenseID int64) (*entities.UserExpense, error)
	DownloadUserExpenses(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.UserExpenseList, error)
	FilterUserExpenses(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.UserExpenseList, error)
	FindUserExpenseByID(ctx context.Context, userExpenseID int64) (*entities.UserExpense, error)
	GetUserExpenseByName(ctx context.Context, userExpenseNumber string) (*entities.UserExpense, error)
	SaveUserExpense(ctx context.Context, operations txns_db.TransactionsSQLOperations, userExpense *entities.UserExpense) error
	UpdateUserExpense(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateUserExpenseForm) (*entities.UserExpense, error)
}

type AppUserExpenseService struct {
	expenseTypeRepository repos.ExpenseTypeRepository
	userExpenseRepository repos.UserExpenseRepository
}

func NewUserExpenseService(
	expenseTypeRepository repos.ExpenseTypeRepository,
	userExpenseRepo repos.UserExpenseRepository,
) UserExpenseService {
	return &AppUserExpenseService{
		expenseTypeRepository: expenseTypeRepository,
		userExpenseRepository: userExpenseRepo,
	}
}

func (s *AppUserExpenseService) CreateUserExpense(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateUserExpenseForm,
) (*entities.UserExpense, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userExpense := &entities.UserExpense{
		Amount:        form.Amount,
		Currency:      "KES",
		Description:   form.Description,
		ExpenseTypeID: form.ExpenseTypeID,
		MerchantName:  form.MerchantName,
		MerchantPIN:   form.MerchantPIN,
		UserID:        tokenInfo.UserID,
	}

	err := s.userExpenseRepository.Save(ctx, userExpense)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create userExpense with description=[%v]",
			form.Description,
		)
	}

	return userExpense, nil
}

func (s *AppUserExpenseService) DeleteUserExpense(
	ctx context.Context,
	userExpenseID int64,
) (*entities.UserExpense, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userExpense, err := s.userExpenseRepository.FindByUserAndID(ctx, tokenInfo.UserID, userExpenseID)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete userExpense by id=[%v]",
			userExpenseID,
		)
	}

	err = s.userExpenseRepository.Delete(ctx, userExpense)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to delete userExpense by id=[%v]",
			userExpenseID,
		)
	}

	return userExpense, nil
}

func (s *AppUserExpenseService) DownloadUserExpenses(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.UserExpenseList, error) {

	userExpenseList := &entities.UserExpenseList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	utils.Log.Infof("filtering user expenses for user=[%v]", tokenInfo.UserID)
	filter.UserID = tokenInfo.UserID

	count, err := s.userExpenseRepository.CountUserExpenses(ctx, filter)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count userExpenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userExpenses, err := s.userExpenseRepository.FilterUserExpenses(ctx, operations, filter)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter userExpenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userExpensesMap := make(map[int64]*entities.UserExpense)
	expenseTypeIds := []int64{}
	for _, userExpense := range userExpenses {
		expenseTypeIds = append(expenseTypeIds, userExpense.ExpenseTypeID)
		userExpensesMap[userExpense.ID] = userExpense
	}

	expenseTypes, err := s.expenseTypeRepository.FindByIDs(ctx, operations, expenseTypeIds)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find expenseTypes by ids=[%v]",
			expenseTypeIds,
		)
	}

	expenseTypeMap := make(map[int64]*entities.ExpenseType)
	for _, expenseType := range expenseTypes {
		expenseTypeMap[expenseType.ID] = expenseType
	}

	for _, userExpense := range userExpenses {
		if expenseType, ok := expenseTypeMap[userExpense.ExpenseTypeID]; ok {
			userExpense.ExpenseType = expenseType
		}
	}

	userExpenseList.UserExpenses = userExpenses

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	userExpenseList.Pagination = pagination

	// utils.CreateAndAppendUserExpensesDataToExcelFile(filePath, userExpenses)

	return userExpenseList, nil
}

func (s *AppUserExpenseService) FilterUserExpenses(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.UserExpenseList, error) {

	userExpenseList := &entities.UserExpenseList{}

	pagination := &entities.Pagination{
		Count: 0,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	filter.UserID = tokenInfo.UserID

	count, err := s.userExpenseRepository.CountUserExpenses(ctx, filter)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to count userExpenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	if count == 0 {
		utils.Log.Infof("no user expenses found")
		pagination.Count = count
		userExpenseList.Pagination = pagination
		return userExpenseList, nil
	}

	userExpenses, err := s.userExpenseRepository.FilterUserExpenses(ctx, operations, filter)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to filter userExpenses for user=[%v]",
			tokenInfo.UserID,
		)
	}

	userExpensesMap := make(map[int64]*entities.UserExpense)
	expenseTypeIds := []int64{}
	for _, userExpense := range userExpenses {
		expenseTypeIds = append(expenseTypeIds, userExpense.ExpenseTypeID)
		userExpensesMap[userExpense.ID] = userExpense
	}

	expenseTypes, err := s.expenseTypeRepository.FindByIDs(ctx, operations, expenseTypeIds)
	if err != nil {
		return userExpenseList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find expenseTypes by ids=[%v]",
			expenseTypeIds,
		)
	}

	expenseTypeMap := make(map[int64]*entities.ExpenseType)
	for _, expenseType := range expenseTypes {
		expenseTypeMap[expenseType.ID] = expenseType
	}

	for _, userExpense := range userExpenses {
		if expenseType, ok := expenseTypeMap[userExpense.ExpenseTypeID]; ok {
			userExpense.ExpenseType = expenseType
		}
	}

	userExpenseList.UserExpenses = userExpenses

	pagination.Count = count
	userExpenseList.Pagination = pagination

	return userExpenseList, nil
}

func (s *AppUserExpenseService) FindUserExpenseByID(
	ctx context.Context,
	userExpenseID int64,
) (*entities.UserExpense, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userExpense, err := s.userExpenseRepository.FindByUserAndID(ctx, tokenInfo.UserID, userExpenseID)
	if err != nil {
		if utils.IsErrNoRows(err) {
			utils.Log.Infof("userExpense not found by id=[%v]", userExpenseID)

			return userExpense, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("userExpense not found"),
				utils.ErrorCodeResourceNotFound,
				"userExpense not found",
				"userExpense=[%v] not found",
				userExpenseID,
			)
		}

		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find userExpense by id=[%v]",
			userExpenseID,
		)
	}

	expenseType, err := s.expenseTypeRepository.FindByID(ctx, userExpense.ExpenseTypeID)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find expenseType by id=[%v]",
			userExpense.ExpenseTypeID,
		)
	}

	userExpense.ExpenseType = expenseType

	return userExpense, nil
}

func (s *AppUserExpenseService) GetUserExpenseByName(
	ctx context.Context,
	userExpenseName string,
) (*entities.UserExpense, error) {

	userExpense, err := s.userExpenseRepository.FindByName(ctx, userExpenseName)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find userExpense by description=[%v]",
			userExpenseName,
		)
	}

	return userExpense, nil
}

func (s *AppUserExpenseService) SaveUserExpense(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userExpense *entities.UserExpense,
) error {

	err := s.userExpenseRepository.Save(ctx, userExpense)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save userExpense by name=[%v]",
			userExpense.Description,
		)
	}

	return nil
}

func (s *AppUserExpenseService) UpdateUserExpense(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userExpenseID int64,
	form *forms.UpdateUserExpenseForm,
) (*entities.UserExpense, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userExpense, err := s.userExpenseRepository.FindByUserAndID(ctx, tokenInfo.UserID, userExpenseID)
	if err != nil {
		return userExpense, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to update userExpense by id=[%v]",
			userExpenseID,
		)
	}

	if form.Amount > 0 {
		userExpense.Amount = form.Amount
	}

	if form.Currency != "" {
		userExpense.Currency = form.Currency
	}

	if form.Description > "" {
		userExpense.Description = form.Description
	}

	if form.ExpenseTypeID > 0 {
		userExpense.ExpenseTypeID = form.ExpenseTypeID
	}

	if form.MerchantName > "" {
		userExpense.MerchantName = form.MerchantName
	}

	if form.MerchantPIN > "" {
		userExpense.MerchantPIN = form.MerchantPIN
	}

	err = s.userExpenseRepository.Save(ctx, userExpense)
	if err != nil {
		return userExpense, err
	}

	return userExpense, nil
}
