package services

import (
	"context"
	"fmt"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
)

type (
	PaymentService interface {
		DecodePhoneNumberHash(ctx context.Context, transactionsDB database.TransactionsDB, hashString string) (*entities.MPesaPhoneNumberHashDecoderResponse, *entities.PhoneNumber, error)
		InitiateMPesaSTKPush(ctx context.Context, transactionsDB database.TransactionsDB, form *forms.MPesaSTKPushRequestForm) error
		ProcessMPesaDeposit(ctx context.Context, transactionsDB database.TransactionsDB, form *forms.MPesaDepositForm) error
	}

	AppPaymentService struct {
		mpesa                       providers.MPesa
		mpesaDepositRepository      repos.MPesaDepositRepository
		mpesaPhoneNumberHashDecoder providers.MPesaPhoneNumberHashDecoder
		organizationRepository      repos.OrganizationRepository
		phoneNumberRepository       repos.PhoneNumberRepository
		smtpSender                  providers.SmtpSender
	}
)

func NewPaymentService(
	mpesa providers.MPesa,
	mpesaDepositRepository repos.MPesaDepositRepository,
	mpesaPhoneNumberHashDecoder providers.MPesaPhoneNumberHashDecoder,
	organizationRepository repos.OrganizationRepository,
	phoneNumberRepository repos.PhoneNumberRepository,
	smtpSender providers.SmtpSender,
) PaymentService {
	return &AppPaymentService{
		mpesa:                       mpesa,
		mpesaDepositRepository:      mpesaDepositRepository,
		mpesaPhoneNumberHashDecoder: mpesaPhoneNumberHashDecoder,
		organizationRepository:      organizationRepository,
		phoneNumberRepository:       phoneNumberRepository,
		smtpSender:                  smtpSender,
	}
}

func (s *AppPaymentService) DecodePhoneNumberHash(
	ctx context.Context,
	transactionsDB database.TransactionsDB,
	hashString string,
) (*entities.MPesaPhoneNumberHashDecoderResponse, *entities.PhoneNumber, error) {

	decodedResponse := &entities.MPesaPhoneNumberHashDecoderResponse{
		Key:   hashString,
		Value: "",
	}

	var err error

	// Check if phone number already decoded
	phoneNumber, err := s.phoneNumberRepository.FindByMpesaHash(ctx, transactionsDB, hashString)
	if err != nil && !utils.IsErrNoRows(err) {
		return decodedResponse, &entities.PhoneNumber{}, utils.NewError(
			err,
			"unable to decode phone number hash, err=[%v]",
			err,
		)
	}

	if !phoneNumber.IsNew() {
		decodedResponse.Value = phoneNumber.String()
		return decodedResponse, phoneNumber, nil
	}

	// If not decoded, fetch decoded number
	decodedResponse, err = s.mpesaPhoneNumberHashDecoder.DecodePhoneNumber(hashString)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"unable to decode phone number hash, err=[%v]",
			err,
		)
	}

	if decodedResponse == nil {
		return &entities.MPesaPhoneNumberHashDecoderResponse{
			Key:   hashString,
			Value: "",
		}, phoneNumber, nil
	}

	// Check if phone number exists in db
	searchPhoneNumber, err := utils.ParsePhoneNumber(decodedResponse.Value)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"unable to parse phoneNumber=[%v], err=[%v]",
			decodedResponse.Value,
			err,
		)
	}

	utils.Log.Infof("decodedResponse.Value=[%v], searchPhoneNumber=[%v]", decodedResponse.Value, searchPhoneNumber.String())

	foundPhoneNumber, err := s.phoneNumberRepository.SearchByPhoneNumber(ctx, transactionsDB, searchPhoneNumber)
	if err != nil && !utils.IsErrNoRows(err) {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"unable to search decoded phone number in db, err=[%v]",
			err,
		)
	}

	if !foundPhoneNumber.IsNew() {
		phoneNumber = foundPhoneNumber
	} else {
		phoneNumber = &searchPhoneNumber
	}

	// Save decoded number
	phoneNumber.MpesaHash = &decodedResponse.Key
	err = s.phoneNumberRepository.Save(ctx, transactionsDB, phoneNumber)
	if err != nil {
		return decodedResponse, phoneNumber, utils.NewError(
			err,
			"unable to save phone number hash, err=[%v]",
			err,
		)
	}

	return decodedResponse, phoneNumber, nil
}

func (s *AppPaymentService) InitiateMPesaSTKPush(
	ctx context.Context,
	transactionsDB database.TransactionsDB,
	form *forms.MPesaSTKPushRequestForm,
) error {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	utils.Log.Infof("initiating stk push for userID=[%v], phone=[%v], amt=[%v]", tokenInfo.UserID, form.PhoneNumber, form.Amount)

	resp, err := s.mpesa.InitiateSTKPushRequest(form)
	if err != nil {
		return utils.NewError(
			err,
			"failed to initiate stk push, err=[%v]",
			err,
		)
	}

	utils.Log.Infof("stk push response=[%+v]", resp)

	return nil
}

func (s *AppPaymentService) ProcessMPesaDeposit(
	ctx context.Context,
	transactionsDB database.TransactionsDB,
	form *forms.MPesaDepositForm,
) error {

	utils.Log.Infof("processing mpesa topup transID=[%v]", form.TransID)

	phoneNumberDecodeResponse, phoneNumber, err := s.DecodePhoneNumberHash(ctx, transactionsDB, form.MSISDN)
	if err != nil {
		utils.Log.Infof("unable to decode phone number hash, err=[%v]", err.Error())
	}

	phoneNumberValue := phoneNumberDecodeResponse.Value

	floatAmt := utils.ConvertStringToFloat64(form.TransAmount)
	formattedAmount := utils.RenderFloat("#,###.##", floatAmt)

	utils.Log.Infof("sending payment topup email, phoneNumber=[%v], amount=[%v]...", phoneNumberValue, formattedAmount)

	subject := "M-Pesa Deposit"
	to := "<EMAIL>"

	depositorName := fmt.Sprintf("%v %v %v", form.FirstName, form.MiddleName, form.LastName)

	items := map[string]string{
		"TransactionAmount": formattedAmount,
		"TransactionID":     form.TransID,
		"Account":           form.BillRefNumber,
		"Name":              depositorName,
		"PhoneNumber":       phoneNumberValue,
	}

	// Send Email to admin with payment details.
	go s.smtpSender.SendEmailWithTemplate("optimus", "templates/mpesa-topup.html", to, subject, items, nil)

	defaultOrganization, err := s.organizationRepository.GetDefaultOrganization(ctx, transactionsDB)
	if err != nil {
		utils.Log.Infof("unable to find default organization, err=[%v]", err.Error())
	}

	// Save to db and route accordingly depending on the payment account.
	mpesaDeposit := &entities.MPesaDeposit{
		BillRefNumber:     form.BillRefNumber,
		BusinessShortCode: form.BusinessShortCode,
		FirstName:         form.FirstName,
		InvoiceNumber:     form.InvoiceNumber,
		LastName:          form.LastName,
		MiddleName:        form.MiddleName,
		MSISDN:            form.MSISDN,
		OrgAccountBalance: form.OrgAccountBalance,
		OrganizationID:    defaultOrganization.ID,
		PhoneNumberID:     phoneNumber.ID,
		ThirdPartyTransID: form.ThirdPartyTransID,
		TransactionAmount: form.TransAmount,
		TransactionID:     form.TransID,
		TransactionTime:   form.TransTime,
		TransactionType:   form.TransactionType,
	}

	err = s.mpesaDepositRepository.Save(ctx, mpesaDeposit)
	if err != nil {
		utils.Log.Infof("failed to save mpesa deposit, err=[%v]", err.Error())
	}

	return nil
}
