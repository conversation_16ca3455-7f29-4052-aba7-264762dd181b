package mock

import (
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/providers"

	coreentities "compliance-and-risk-management-backend/app/entities"
	coreforms "compliance-and-risk-management-backend/app/forms"
)

type (
	MockOpenAI struct {
	}
)

func NewOpenAI() providers.OpenAI {
	return &MockOpenAI{}
}

func (s *MockOpenAI) AddMessageToThread(threadID string, thread *coreentities.OpenAIThread) (*coreentities.OpenAIChatResponse, error) {
	logger.Infof("adding message to thread=[%v]", threadID)
	return &coreentities.OpenAIChatResponse{}, nil
}

func (s *MockOpenAI) CheackAIThreadRunStatus(threadID string, runID string) (*coreentities.OpenAIThreadRunResponse, error) {
	return &coreentities.OpenAIThreadRunResponse{}, nil
}

func (s *MockOpenAI) CreateAIAssistant(form *coreforms.CreateOpenAIAssistantForm) (*coreentities.OpenAIAssistant, error) {
	logger.Infof("creating AI assistant=[%v]", form.Name)
	return &coreentities.OpenAIAssistant{}, nil
}

func (s *MockOpenAI) CreateAIThread(form *coreforms.CreateOpenAIThreadMessageForm) (*coreentities.OpenAIThread, error) {
	logger.Infof("creating AI thread")
	return &coreentities.OpenAIThread{}, nil
}

func (s *MockOpenAI) CreateAIThreadMessage(threadID string, form *coreforms.CreateOpenAIMessageForm) (*coreentities.OpenAIThreadMessage, error) {
	logger.Infof("creating AI thread message")
	return &coreentities.OpenAIThreadMessage{}, nil
}

func (s *MockOpenAI) CreateAIThreadRun(threadID string, assistantID string) (*coreentities.OpenAIThreadRunResponse, error) {
	logger.Infof("creating AI thread run")
	return &coreentities.OpenAIThreadRunResponse{}, nil
}

func (s *MockOpenAI) FilterThreadMessages(threadID string, filter *coreentities.PaginationFilter) (*coreentities.OpenAIThreadMessages, error) {
	logger.Infof("filter messages for thread=[%v] with filter=[%+v]", threadID, filter)
	return &coreentities.OpenAIThreadMessages{}, nil
}

func (s *MockOpenAI) ListAIAssistants() (*coreentities.OpenAIAssistantList, error) {
	return &coreentities.OpenAIAssistantList{}, nil
}

func (s *MockOpenAI) RetrieveAIAssistant(assistantID string) (*coreentities.OpenAIAssistant, error) {
	logger.Infof("retrieving AI assistant by ID=[%v]", assistantID)
	return &coreentities.OpenAIAssistant{}, nil
}

func (s *MockOpenAI) RetrieveAIThread(threadID string) (*coreentities.OpenAIThread, error) {
	logger.Infof("retrieving AI thread by ID=[%v]", threadID)
	return &coreentities.OpenAIThread{}, nil
}

func (s *MockOpenAI) RetrieveAIThreadRun(threadID, runID string) (*coreentities.OpenAIThreadRunResponse, error) {
	logger.Infof("retrieving AI thread run by threadID=[%v] and runID=[%v]", threadID, runID)
	return &coreentities.OpenAIThreadRunResponse{}, nil
}

func (s *MockOpenAI) SendRequest(request string) (*coreentities.OpenAIChatResponse, error) {
	logger.Infof("sending ai chat request=[%v]", request)
	return &coreentities.OpenAIChatResponse{}, nil
}

func (s *MockOpenAI) UpdateAIAssistant(aiAssistantID string, form *coreforms.UpdateOpenAIAssistantForm) (*coreentities.OpenAIAssistant, error) {
	logger.Infof("updating AI assistant")
	return &coreentities.OpenAIAssistant{}, nil
}
