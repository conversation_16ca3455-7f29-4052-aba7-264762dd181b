package mock

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/providers"
)

type (
	MockMPesaPhoneNumberHashDecoder struct {
	}
)

func NewMPesaPhoneNumberHashDecoder() providers.MPesaPhoneNumberHashDecoder {
	return &MockMPesaPhoneNumberHashDecoder{}
}

func (s *MockMPesaPhoneNumberHashDecoder) DecodePhoneNumber(phoneNumberHash string) (*entities.MPesaPhoneNumberHashDecoderResponse, error) {
	logger.Infof("decoding phone number hash=[%v], category=[%v]", phoneNumberHash)
	return &entities.MPesaPhoneNumberHashDecoderResponse{Key: phoneNumberHash, Value: "254725068386"}, nil
}
