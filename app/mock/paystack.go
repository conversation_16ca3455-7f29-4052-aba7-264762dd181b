package mock

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/providers"
)

type (
	MockPaystack struct {
	}
)

func NewPaystack() providers.Paystack {
	return &MockPaystack{}
}

func (s *MockPaystack) CreateCustomer(*entities.PaystackCustomer) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) CreatePlan(*entities.PaystackPlan) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) CreateSubscription(plan *entities.PaystackSubscription) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) FetchPlanByCode(string) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) FetchPlanByID(int64) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) InitiateTransaction(transaction *entities.PaystackTransaction) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) ListPlans() (*entities.PaystackPlansResponse, error) {
	return &entities.PaystackPlansResponse{}, nil
}

func (s *MockPaystack) ListSubscriptions() (*entities.PaystackSubscriptionList, error) {
	return &entities.PaystackSubscriptionList{}, nil
}

func (s *MockPaystack) UpdatePlan(string, *entities.PaystackPlan) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}

func (s *MockPaystack) VerifyPayment(referenceNumber string) (*entities.PaystackResponse, error) {
	return &entities.PaystackResponse{}, nil
}
