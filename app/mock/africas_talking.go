package mock

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/providers"
)

type (
	MockAfricasTalking struct {
	}
)

func NewAfricasTalking() providers.AfricasTalking {
	return &MockAfricasTalking{}
}

func (s *MockAfricasTalking) SendSms(phoneNumber entities.PhoneNumber, message string, category entities.MessageCategory) error {
	logger.Infof("Send message=[%v] to phone number=[%v], category=[%v]", message, phoneNumber, category)
	return nil
}
