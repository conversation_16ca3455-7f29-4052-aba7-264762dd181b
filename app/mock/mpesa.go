package mock

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/providers"
)

type (
	MockMPesa struct {
	}
)

func NewMPesa() providers.MPesa {
	return &MockMPesa{}
}

func (s *MockMPesa) AccountBalance() (*entities.MPesaAccountBalanceResponse, error) {
	return &entities.MPesaAccountBalanceResponse{}, nil
}

func (s *MockMPesa) AuthenticateMPesa() (*entities.MPesaAuthentication, error) {
	return &entities.MPesaAuthentication{}, nil
}

func (s *MockMPesa) InitiateSTKPushRequest(form *forms.MPesaSTKPushRequestForm) (*entities.MPesaSTKPushResponse, error) {
	return &entities.MPesaSTKPushResponse{}, nil
}

func (s *MockMPesa) TransactionStatusQuery(trxID string) (*entities.MPesaTransactionStatusResponse, error) {
	return &entities.MPesaTransactionStatusResponse{}, nil
}

func (s *MockMPesa) RegisterURL(form *forms.RegisterURLForm) error {
	return nil
}

func (s *MockMPesa) Reversal(mpesaTrxID string, amount float64) (*entities.MPesaReversalResponse, error) {
	return &entities.MPesaReversalResponse{}, nil
}
