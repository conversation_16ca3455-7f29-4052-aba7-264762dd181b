package forms

type (
	UserRegistrationForm struct {
		Email       string          `binding:"required" json:"email"`
		FirstName   string          `binding:"required" json:"first_name"`
		LastName    string          `json:"last_name"`
		PhoneNumber PhoneNumberForm `json:"phone_number"`
		Password    string          `binding:"required" json:"password"`
		NationalID  string          `json:"national_id"`
	}

	UserUpdateForm struct {
		FirstName   string          `json:"first_name"`
		LastName    string          `json:"last_name"`
		PhoneNumber PhoneNumberForm `json:"phone_number"`
	}
)
