package forms

type (
	MPesaSTKPushForm struct {
		BusinessShortCode string
		Password          string
		Timestamp         string
		TransactionType   string
		Amount            float64
		PartyA            string // user phone number
		PartyB            string // org paybill
		PhoneNumber       string
		CallBackURL       string
		AccountReference  string
		TransactionDesc   string
	}

	MPesaSTKPushRequestForm struct {
		Amount                 float64 `binding:"required" json:"amount"`
		PhoneNumber            string  `binding:"required" json:"phone_number"`
		AccountReference       string  `binding:"required" json:"account_reference"`
		TransactionDescription string  `binding:"required" json:"transaction_description"`
	}
)
