package forms

type (
	MPesaReversalForm struct {
		Amount                 float64
		CommandID              string
		Initiator              string
		RecieverIdentifierType string
		ReceiverParty          string
		Remarks                string
		QueueTimeOutURL        string
		ResultURL              string
		SecurityCredential     string
		TransactionID          string
	}

	MPesaReversalRequestForm struct {
		Amount                 float64 `binding:"required" json:"amount"`
		PhoneNumber            string  `binding:"required" json:"phone_number"`
		CallBackURL            string  `binding:"required" json:"call_back_url"`
		AccountReference       string  `binding:"required" json:"account_reference"`
		TransactionDescription string  `binding:"required" json:"transaction_description"`
	}
)
