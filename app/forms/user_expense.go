package forms

type (
	CreateUserExpenseForm struct {
		Amount        float64 `json:"amount"  validate:"required"`
		Currency      string  `json:"currency"`
		Description   string  `json:"description"`
		ExpenseTypeID int64   `json:"expense_type_id" validate:"required"`
		MerchantName  string  `json:"merchant_name"  validate:"required"`
		MerchantPIN   string  `json:"merchant_pin"`
		UserID        int64   `json:"user_id"`
	}

	UpdateUserExpenseForm struct {
		Amount        float64 `json:"amount"`
		Currency      string  `json:"currency"`
		Description   string  `json:"description"`
		ExpenseTypeID int64   `json:"expense_type_id"`
		MerchantName  string  `json:"merchant_name"`
		MerchantPIN   string  `json:"merchant_pin"`
	}
)
