package forms

type (
	CreateEtimsItemForm struct {
		Tin         string  `json:"tin"`         // PIN Number
		BhfID       string  `json:"bhfId"`       // Branch Number
		ItemCd      string  `json:"itemCd"`      // Item Code
		ItemClsCd   string  `json:"itemClsCd"`   // Item Classification Code
		ItemTyCd    string  `json:"itemTyCd"`    // Item Type Code
		ItemNm      string  `json:"itemNm"`      // Item Name
		ItemStdNm   string  `json:"itemStdNm"`   // Item Standard Name
		OrgnNatCd   string  `json:"orgnNatCd"`   // Origin Place Code (Nation)
		PkgUnitCd   string  `json:"pkgUnitCd"`   // Packaging Unit Code
		QtyUnitCd   string  `json:"qtyUnitCd"`   // Quantity Unit Code
		TaxTyCd     string  `json:"taxTyCd"`     // Tax Type code
		BtchNo      int     `json:"btchNo"`      // Batch Number
		Bcd         string  `json:"bcd"`         // Barcode
		DftPrc      float64 `json:"dftPrc"`      // Default Unit Price
		GrpPrcL1    float64 `json:"grpPrcL1"`    // Group1 Unit Price
		GrpPrcL2    float64 `json:"grpPrcL2"`    // Group2 Unit Price
		GrpPrcL3    float64 `json:"grpPrcL3"`    // Group3 Unit Price
		GrpPrcL4    float64 `json:"grpPrcL4"`    // Group4 Unit Price
		GrpPrcL5    float64 `json:"grpPrcL5"`    // Group5 Unit Price
		AddInfo     string  `json:"addInfo"`     // Addional Information
		SftyQty     float64 `json:"sftyQty"`     // Safety Quantity
		IsrcAplcbYn string  `json:"isrcAplcbYn"` // Insurance Applicable (Y/N)
		UseYn       string  `json:"useYn"`       // Yes / No
		RegrNm      string  `json:"regrNm"`      // Registrant Name
		RegrID      string  `json:"regrId"`      // Registrant ID
		ModrNm      string  `json:"modrNm"`      // Modifier Name
		ModrID      string  `json:"modrId"`      // Modifier ID
	}

	EtimsReceiptForm struct {
		CurRcptNo    int64  `json:"curRcptNo"`    // Current Receipt Number
		CustTin      string `json:"custTin"`      // Customer PIN
		CustMblNo    string `json:"custMblNo"`    // Customer Mobile Number
		RptNo        int64  `json:"rptNo"`        // Report Number
		TrdeNm       string `json:"trdeNm"`       // Trade Name
		Adrs         string `json:"adrs"`         // Address
		RcptPbctDt   string `json:"rcptPbctDt"`   // Receipt publication date
		TopMsg       string `json:"topMsg"`       // Top Message
		BtmMsg       string `json:"btmMsg"`       // Bottom Message
		PrchrAcptcYn string `json:"prchrAcptcYn"` // Whether buyers receive item or not
		TotRcptNo    int64  `json:"totRcptNo"`    // Total receipt number
	}

	EtimsItemForm struct {
		Bcd       string  `json:"bcd"`       // Barcode
		DcAmt     float64 `json:"dcAmt"`     // Discount Amount
		DcRt      float64 `json:"dcRt"`      // Discount Rate
		IsrcAmt   string  `json:"isrcAmt"`   // Insurance Amount
		IsrccCd   string  `json:"isrccCd"`   // Insurance Company Code
		ItemCd    string  `json:"itemCd"`    // Item Code
		ItemClsCd string  `json:"itemClsCd"` // Item Classification Code
		IsrccNm   string  `json:"isrccNm"`   // Insurance Company Name
		IsrcRt    string  `json:"isrcRt"`    // Insurance Rate
		ItemNm    string  `json:"itemNm"`    // Item Name
		ItemSeq   int     `json:"itemSeq"`   // Item Sequence Number
		PkgUnitCd string  `json:"pkgUnitCd"` // Packaging Unit Code
		Pkg       float64 `json:"pkg"`       // Package
		Prc       float64 `json:"prc"`       // Unit Price
		QtyUnitCd string  `json:"qtyUnitCd"` // Quantity Unit Code
		Qty       float64 `json:"qty"`       // Quantity
		SplyAmt   float64 `json:"splyAmt"`   // Supply Amount
		TaxAmt    float64 `json:"taxAmt"`    // Tax Amount
		TaxblAmt  float64 `json:"taxblAmt"`  // Taxable Amount
		TaxTyCd   string  `json:"taxTyCd"`   // Taxation Type Code
		TotAmt    float64 `json:"totAmt"`    // Total Amount
	}

	CreateEtimsSalesForm struct {
		Tin          string            `json:"tin"`          // Owner PIN
		BhfID        string            `json:"bhfId"`        // Branch ID
		TrdInvcNo    int64             `json:"trdInvcNo"`    // Trader InvoiceNumber
		InvcNo       int64             `json:"invcNo"`       // Invoice Number
		OrgInvcNo    int64             `json:"orgInvcNo"`    // Original InvoiceNumber
		CustTin      string            `json:"custTin"`      // Customer PIN
		CustNm       string            `json:"custNm"`       // Customer Name
		SalesTyCd    string            `json:"salesTyCd"`    // Sales Type Code
		RcptTyCd     string            `json:"rcptTyCd"`     // Receipt Type Code
		PmtTyCd      string            `json:"pmtTyCd"`      // Payment TypeCode
		SalesSttsCd  string            `json:"salesSttsCd"`  // Invoice Status Code
		CfmDt        string            `json:"cfmDt"`        // Validated Date
		SalesDt      string            `json:"salesDt"`      // Sale Date
		StockRlsDt   string            `json:"stockRlsDt"`   // Stock Released Date
		CnclReqDt    string            `json:"cnclReqDt"`    // Cancel Reqeuested Date
		CnclDt       string            `json:"cnclDt"`       // Canceled Date
		RfdDt        string            `json:"rfdDt"`        // Credit Note Date
		RfdRsnCd     string            `json:"rfdRsnCd"`     // Credit Note Reason Code
		TotItemCnt   int               `json:"totItemCnt"`   // Total Item Count
		TaxblAmtA    float64           `json:"taxblAmtA"`    // Taxable Amount A
		TaxblAmtB    float64           `json:"taxblAmtB"`    // Taxable Amount B
		TaxblAmtC    float64           `json:"taxblAmtC"`    // Taxable Amount C
		TaxblAmtD    float64           `json:"taxblAmtD"`    // Taxable Amount D
		TaxblAmtE    float64           `json:"taxblAmtE"`    // Taxable Amount E
		TaxRtA       float64           `json:"taxRtA"`       // Tax Rate A
		TaxRtB       float64           `json:"taxRtB"`       // Tax Rate B
		TaxRtC       float64           `json:"taxRtC"`       // Tax Rate C
		TaxRtD       float64           `json:"taxRtD"`       // Tax Rate D
		TaxRtE       float64           `json:"taxRtE"`       // Tax Rate E
		TaxAmtA      float64           `json:"taxAmtA"`      // Tax Amt A
		TaxAmtB      float64           `json:"taxAmtB"`      // Tax Amt B
		TaxAmtC      float64           `json:"taxAmtC"`      // Tax Amt C
		TaxAmtD      float64           `json:"taxAmtD"`      // Tax Amt D
		TaxAmtE      float64           `json:"taxAmtE"`      // Tax Amt E
		TotTaxblAmt  float64           `json:"totTaxblAmt"`  // Total Taxable Amount
		TotTaxAmt    float64           `json:"totTaxAmt"`    // Total Tax Amount
		TotAmt       float64           `json:"totAmt"`       // Total Amount
		TrdeNm       string            `json:"trdeNm"`       // Trade Name
		PrchrAcptcYn string            `json:"prchrAcptcYn"` // Purchase Accept Y/N
		Remark       string            `json:"remark"`       // Remark
		RegrID       string            `json:"regrId"`       // Registrant ID
		RegrNm       string            `json:"regrNm"`       // Registrant Name
		ModrID       string            `json:"modrId"`       // Modifier ID
		ModrNm       string            `json:"modrNm"`       // Modifier Name
		Receipt      *EtimsReceiptForm `json:"receipt"`
		ItemList     []*EtimsItemForm  `json:"itemList"`
	}

	CreateEtimsStockItemsForm struct {
		Tin         string                 `json:"tin"`
		BhfID       string                 `json:"bhfId"`
		SarNo       int                    `json:"sarNo"`
		OrgSarNo    int                    `json:"orgSarNo"`
		RegTyCd     string                 `json:"regTyCd"`
		CustTin     string                 `json:"custTin"`
		CustNm      string                 `json:"custNm"`
		CustBhfID   string                 `json:"custBhfId"`
		SarTyCd     string                 `json:"sarTyCd"`
		OcrnDt      string                 `json:"ocrnDt"`
		TotItemCnt  int                    `json:"totItemCnt"`
		TotTaxblAmt float64                `json:"totTaxblAmt"`
		TotTaxAmt   float64                `json:"totTaxAmt"`
		TotAmt      float64                `json:"totAmt"`
		Remark      string                 `json:"remark"`
		RegrID      string                 `json:"regrId"`
		RegrNm      string                 `json:"regrNm"`
		ModrNm      string                 `json:"modrNm"`
		ModrID      string                 `json:"modrId"`
		ItemList    []CreateEtimsStockItem `json:"itemList"`
	}

	CreateEtimsStockItem struct {
		ItemSeq    int     `json:"itemSeq"`
		ItemCd     string  `json:"itemCd"`
		ItemClsCd  string  `json:"itemClsCd"`
		ItemNm     string  `json:"itemNm"`
		Bcd        string  `json:"bcd"`
		PkgUnitCd  string  `json:"pkgUnitCd"`
		Pkg        float64 `json:"pkg"`
		QtyUnitCd  string  `json:"qtyUnitCd"`
		Qty        float64 `json:"qty"`
		ItemExprDt string  `json:"itemExprDt"`
		Prc        float64 `json:"prc"`
		SplyAmt    float64 `json:"splyAmt"`
		TotDcAmt   float64 `json:"totDcAmt"`
		TaxblAmt   float64 `json:"taxblAmt"`
		TaxTyCd    string  `json:"taxTyCd"`
		TaxAmt     float64 `json:"taxAmt"`
		TotAmt     float64 `json:"totAmt"`
	}

	CreateSelectInfoForm struct {
		Tin      string `json:"tin"`
		BhfId    string `json:"bhfId"`
		DvcSrlNo string `json:"dvcSrlNo"`
	}

	CreateTinBranchCustomerTINForm struct {
		Tin      string `json:"tin"`
		BhfId    string `json:"bhfId"`
		CustmTin string `json:"custmTin"`
	}

	CreateTinBranchLastRequstForm struct {
		Tin       string `json:"tin"`
		BhfId     string `json:"bhfId"`
		LastReqDt string `json:"lastReqDt"`
	}

	EchoTestForm struct {
		Tin     string `json:"tin"`
		BhfId   string `json:"bhfId"`
		TestStr string `json:"testStr"`
	}

	NoticeRequestForm struct {
		Tin       string `json:"tin"`
		BhfId     string `json:"bhfId"`
		LastReqDt string `json:"lastReqDt"`
	}
)
