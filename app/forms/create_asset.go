package forms

type CreateAssetForm struct {
	Async       bool    `json:"async"`
	ContentMD5  string  `binding:"required" json:"content_md5"`
	ContentSize int     `binding:"required" json:"content_size"`
	ContentType string  `binding:"required" json:"content_type"`
	Description string  `json:"description"`
	Name        string  `binding:"max=255" json:"name"`
	Data        *string `json:"data"`
	Duration    *int    `json:"duration"`
	ImageLength *int    `json:"image_length"`
	ImageWidth  *int    `json:"image_width"`
	IsPrivate   *bool   `json:"is_private"`
}
