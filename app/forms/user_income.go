package forms

type (
	CreateUserIncomeForm struct {
		TaxCategoryID        int64   `json:"tax_category_id"`
		UserID               int64   `json:"user_id"`
		MerchantName         string  `json:"merchant_name"`
		MerchantPIN          string  `json:"merchant_pin"`
		Amount               float64 `json:"amount"`
		Currency             string  `json:"currency"`
		Description          string  `json:"description"`
		PaymentFrequency     string  `json:"payment_frequency"`
		StartDate            string  `json:"start_date"`
		EndDate              string  `json:"end_date"`
		WithholdingTaxRate   float64 `json:"withholding_tax_rate"`
		WithholdingTaxAmount float64 `json:"withholding_tax_amount"`
	}

	UpdateUserIncomeForm struct {
		TaxCategoryID        int64   `json:"tax_category_id"`
		UserID               int64   `json:"user_id"`
		MerchantName         string  `json:"merchant_name"`
		MerchantPIN          string  `json:"merchant_pin"`
		Amount               float64 `json:"amount"`
		Currency             string  `json:"currency"`
		Description          string  `json:"description"`
		PaymentFrequency     string  `json:"payment_frequency"`
		StartDate            string  `json:"start_date"`
		EndDate              string  `json:"end_date"`
		WithholdingTaxRate   float64 `json:"withholding_tax_rate"`
		WithholdingTaxAmount float64 `json:"withholding_tax_amount"`
	}
)
