package forms

type (
	CreateItemForm struct {
		AvatarURL   string  `json:"avatar_url"`
		CategoryID  int64   `json:"category_id"`
		CostPrice   float64 `json:"cost_price"`
		Description string  `json:"description"`
		ETIMSCode   string  `json:"etims_code"`
		Name        string  `binding:"required" json:"name"`
		Quantity    float64 `binding:"required" json:"quantity"`
		RetailPrice float64 `json:"retail_price"`
		StoreId     int64   `binding:"required" json:"store_id"`
		TaxPercent  float64 `json:"tax_percent"`
		TaxTypeCode string  `json:"tax_type_code"` // Tax Type code - A, B, C, D, E
	}

	UpdateItemForm struct {
		AvatarURL   string  `json:"avatar_url"`
		CategoryID  int64   `json:"category_id"`
		CostPrice   float64 `json:"cost_price"`
		Description string  `json:"description"`
		ETIMSCode   string  `json:"etims_code"`
		Name        string  `binding:"required" json:"name"`
		RetailPrice float64 `json:"retail_price"`
		TaxPercent  float64 `json:"tax_percent"`
	}
)
