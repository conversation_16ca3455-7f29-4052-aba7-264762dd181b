package forms

type (
	CreateWithholdingTaxRateForm struct {
		Name               string  `binding:"required" json:"name"`
		Description        string  `json:"description"`
		TaxCategoryID      int64   `json:"tax_category_id"`
		WithholdingTaxRate float64 `json:"withholding_tax_rate"`
	}

	UpdateWithholdingTaxRateForm struct {
		Name               string  `json:"name"`
		Description        string  `json:"description"`
		TaxCategoryID      int64   `json:"tax_category_id"`
		WithholdingTaxRate float64 `json:"withholding_tax_rate"`
	}
)