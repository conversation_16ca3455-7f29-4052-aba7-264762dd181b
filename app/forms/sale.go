package forms

type (
	CreateSaleForm struct {
		AmountTendered float64               `json:"amount_tendered"`
		Change         float64               `json:"change"`
		CreatedBy      int64                 `json:"created_by"`
		CurrencyID     int64                 `json:"currency_id"`
		CustomerID     *int64                `json:"customer_id"`
		OrganizationID int64                 `json:"organization_id"`
		PaymentMethod  string                `json:"payment_method"`
		SaleItems      []*CreateSaleItemForm `json:"sale_items"`
		Status         string                `json:"status"`
		StoreID        int64                 `json:"store_id"`
		Subtotal       int64                 `json:"subtotal"`
		Total          float64               `json:"total"`
		Vat            float64               `json:"vat,omitempty"`
	}

	UpdateSaleForm struct {
		AmountTendered     int64  `json:"amount_tendered"`
		Change             int64  `json:"change"`
		CurrencyID         int64  `json:"currency_id"`
		CustomerID         *int64 `json:"customer_id"`
		PaymentMethod      string `json:"payment_method"`
		Scale              int64  `json:"scale"`
		Status             string `json:"status"`
		StoreID            int64  `json:"store_id"`
		Subtotal           int64  `json:"subtotal"`
		Total              int64  `json:"total"`
		TotalAmountDisplay string `json:"total_amount_display"`
		Vat                int64  `json:"vat,omitempty"`
	}
)
