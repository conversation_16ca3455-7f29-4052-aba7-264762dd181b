package forms

type (
	CreditNoteForm struct {
		CreditNoteNumber string      `binding:"required" json:"credit_note_number"`
		CustomerName     string      `binding:"required" json:"customer_name"`
		CustomerTIN      string      `binding:"required" json:"customer_tin"`
		CustomerVRN      string      `json:"customer_vrn"`
		DC               int         `json:"dc"`
		FileName         string      `json:"file_name"`
		GC               int         `json:"gc"`
		GrandTotal       float64     `binding:"required" json:"grand_total"`
		GrossAmount      float64     `binding:"required" json:"gross_amount"`
		InvoiceNumber    string      `json:"invoice_number"`
		Items            []*ItemForm `json:"items"`
		OriginalFileName string      `json:"original_file_name"`
		ReceiptCode      string      `json:"receipt_code"`
		Rctvnum          string      `json:"rctvnum"`
		Signature        string      `json:"signature"`
		Vat              float64     `binding:"required" json:"vat"`
		VerificationURL  string      `json:"verification_url"`
	}
)

// # Tax Codes  Description
// 1 - A       18% Tax
// 2 - B-10    Special Rate B
// 3 - C-0     Zero Rated
// 4 - D-SR    Special Rate D-SR
// 5 - E-EX    Exempt E-Ex
