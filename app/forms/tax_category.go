package forms

type (
	CreateTaxCategoryForm struct {
		ApplicableTaxTypeID int64   `json:"applicable_tax_type_id"`
		Description         string  `json:"description"`
		DueDate             string  `json:"due_date"`
		HasWithholdingTax   bool    `json:"has_withholding_tax"`
		IncomeSource        string  `json:"income_source"`
		TaxRate             float64 `json:"tax_rate"`
	}

	UpdateTaxCategoryForm struct {
		ApplicableTaxTypeID int64   `json:"applicable_tax_type_id"`
		Description         string  `json:"description"`
		DueDate             string  `json:"due_date"`
		HasWithholdingTax   bool    `json:"has_withholding_tax"`
		IncomeSource        string  `json:"income_source"`
		TaxRate             float64 `json:"tax_rate"`
	}
)
