package database

import (
	"compliance-and-risk-management-backend/app/utils"
	"database/sql"
	"errors"
	"log"
	"os"
	"path/filepath"
	"runtime"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"
)

type (
	Storage interface {
		RunMigrations(connectionString string) error
	}

	AppStorage struct {
		db *sql.DB
	}
)

func NewStorage(db *sql.DB) Storage {
	return &AppStorage{
		db: db,
	}
}

func (s *AppStorage) RunMigrations(connectionString string) error {
	if connectionString == "" {
		return errors.New("repository: the connString was empty")
	}

	utils.Log.Infof("running db migrations...")

	basePath, err := os.Getwd()
	if err != nil {
		log.Println(err)
	}

	if runtime.GOOS == "windows" {
		basePath = basePath[3:]
	}

	migrationsPath := filepath.Join(basePath, "/app/database/migrations/")
	migrationsPathURL := "file:///" + filepath.ToSlash(migrationsPath)

	m, err := migrate.New(migrationsPathURL, connectionString)
	if err != nil {
		utils.Log.Infof("error running migrations, err=[%v]", err)
		return err
	}

	err = m.Up()
	noChangeError := errors.New("no change")
	if err != nil && err.Error() != noChangeError.Error() {
		utils.Log.Infof("error running db migrations, err=[%v]", err)
		return err
	}

	utils.Log.Infof("Done.")

	return nil
}
