package database

import (
	"context"
	"database/sql"
)

type TestTransactionsDB struct {
	*sql.Tx
	valid bool
}

func (db *TestTransactionsDB) Begin() (*sql.Tx, error) {
	return db.Tx, nil
}

func (db *TestTransactionsDB) Close() error {
	return nil
}

func (db *TestTransactionsDB) InTransaction(ctx context.Context, operations func(context.Context, TransactionsSQLOperations) error) error {
	return operations(ctx, db)
}

func (db *TestTransactionsDB) Ping() error {
	return nil
}

func (db *TestTransactionsDB) ValidForTransactions() bool {
	return true
}

func (db *TestTransactionsDB) Valid() bool {
	return db.valid
}

func NewTestTransactionsDB(tx *sql.Tx) *TestTransactionsDB {
	return &TestTransactionsDB{
		Tx:    tx,
		valid: tx != nil,
	}
}
