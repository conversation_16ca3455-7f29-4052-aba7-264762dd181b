-- +goose Up
BEGIN;

-- Create table with all columns
CREATE TABLE IF NOT EXISTS withholding_tax_rates (
    id                       BIGSERIAL                   PRIMARY KEY, 
    tax_category_id          BIGINT                      NOT NULL REFERENCES tax_categories (id),
    name                     VARCHAR(250)                NOT NULL,
    description              TEXT,
    withholding_tax_rate     NUMERIC(15,2)               NOT NULL,            
    created_by               BIGINT                      NOT NULL REFERENCES users (id),
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

-- Declare variables in a more controlled way
DO $$
DECLARE
    pos_user_id BIGINT;
    tax_cat_id BIGINT;
BEGIN
    -- Get user ID once
    SELECT id INTO pos_user_id FROM users WHERE email = '<EMAIL>';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User <NAME_EMAIL> not found';
    END IF;
    
    RAISE NOTICE 'Using user ID: %', pos_user_id;
    
    -- Insert rates in a single transaction
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Contractual Services',
        'Withholding tax rate for contractual services',
        3.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Consultancy / Professional Services';
    
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Brokerage Services',
        'Withholding tax rate for brokerage services',
        5.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Consultancy / Professional Services';
    
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Property Management Services',
        'Withholding tax rate for property management services',
        7.5, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Consultancy / Professional Services';
    
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Agency Services',
        'Withholding tax rate for agency services',
        10.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Consultancy / Professional Services';

    -- Digital content monetization
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Digital Content Monetization',
        'Withholding tax rate for digital content monetization',
        5.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Digital Content Monetization';

    -- Royalties
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Royalties',
        'Withholding tax rate for royalties',
        5.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Royalties';

    -- Betting, Lottery, Gaming Winnings
    INSERT INTO withholding_tax_rates 
        (tax_category_id, name, description, withholding_tax_rate, created_by)
    SELECT 
        id, 
        'Betting, Lottery, Gaming Winnings',
        'Withholding tax rate for betting, lottery, and gaming winnings',
        20.0, 
        pos_user_id
    FROM tax_categories 
    WHERE income_source = 'Betting, Lottery, Gaming Winnings';
    
    RAISE NOTICE 'Successfully inserted all withholding tax rates';
END $$;

COMMIT;
