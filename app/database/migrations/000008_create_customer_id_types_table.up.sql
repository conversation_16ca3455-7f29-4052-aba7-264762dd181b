CREATE TABLE IF NOT EXISTS customer_id_types (
    id                  BIGSERIAL                   PRIMARY KEY,    
    customer_id_type    VARCHAR(200)                NOT NULL,
    description         VARCHAR(300)                NOT NULL,
    is_default          BOOLEAN                     NOT NULL DEFAULT FALSE, 
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL
);

INSERT INTO customer_id_types(customer_id_type, description) VALUES 
    ('tin', 'TIN'), 
    ('nida', 'NIDA'),  
    ('passport', 'Passport'),
    ('driving_license', 'Driving License'),
    ('voters_id', 'Voters ID'), 
    ('none_of_the_above', 'None Of The Above'); 
