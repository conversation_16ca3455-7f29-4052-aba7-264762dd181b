CREATE TABLE IF NOT EXISTS stores (
    id                BIGSERIAL                   PRIMARY KEY,    
    name              <PERSON><PERSON><PERSON><PERSON>(250)                NOT NULL,
    location          TEXT,
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),  
    phone_number      VA<PERSON>HAR(250),  
    email             VARCHAR(250),  
    vat               VARCHAR(250),  
    pin               VARCHAR(250),  
    website           TEXT,     
    deleted_at        TIMESTAMP,
    avatar_url        TEXT,
    user_id           BIGINT                      NOT NULL REFERENCES users (id),  
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS stores_name_user_id_uidx ON stores(name, user_id);
