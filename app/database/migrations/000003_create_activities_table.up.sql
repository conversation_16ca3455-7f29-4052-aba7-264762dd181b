CREATE TABLE IF NOT EXISTS activities (
    id                  BIGSERIAL                   PRIMARY KEY,    
    user_id             BIGINT                      NOT NULL REFERENCES users(id), 
    activity_type       VARCHAR(150)                NOT NULL,
    description         VARCHAR(255)                NOT NULL,
    source_ip           VARCHAR(50)                 NOT NULL, 
    is_synced           BOOLEAN                     NOT NULL DEFAULT FALSE, 
    resource_id         BIGINT                      NOT NULL,  
    created_at          TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);
