CREATE TABLE IF NOT EXISTS invoice_items (
    id                  BIGSERIAL                   PRIMARY KEY, 
    amt_usd             VARCHAR(255),
    name                VARCHAR(255)                NOT NULL,
    invoice_id          BIGINT                      NOT NULL REFERENCES invoices(id),         
    is_taxable          BOOLEAN,
    quantity            NUMERIC(15,2),
    rate                VARCHAR(255), 
    serial_number       VA<PERSON>HA<PERSON>(255), 
    total               NUMERIC(15,2),
    unit_measure        VARCHAR(255),  
    vat                 NUMERIC(15,2), 
    created_at          TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at          TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS invoice_items_invoice_id_name_uidx ON invoice_items(invoice_id, name);
