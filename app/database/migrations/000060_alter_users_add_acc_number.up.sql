-- Step 1: Add the new column
ALTER TABLE users ADD COLUMN account_number VARCHAR(50);


-- Step 2: Update existing records with generated account numbers
WITH numbered_users AS (
    SELECT id, ROW_NUMBER() OVER (ORDER BY id) AS rn
    FROM users
)
UPDATE users
SET account_number = 'HS01' || LPAD(numbered_users.rn::TEXT, 3, '0')
FROM numbered_users
WHERE users.id = numbered_users.id;


-- Step 3: Set the column to NOT NULL
ALTER TABLE users ALTER COLUMN account_number SET NOT NULL;

