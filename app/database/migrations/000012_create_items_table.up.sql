CREATE TABLE IF NOT EXISTS items (
    id                BIGSERIAL                   PRIMARY KEY,    
    name              <PERSON><PERSON><PERSON><PERSON>(250)                NOT NULL,
    description       TEXT,
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),  
    cost_price        NUMERIC(15,2)               NOT NULL,  
    retail_price      NUMERIC(15,2)               NOT NULL,  
    category_id       BIGINT                      NOT NULL REFERENCES categories (id),  
    tax_percent       NUMERIC(15,2)               NOT NULL DEFAULT 0.00,  
    deleted_at        TIMESTAMP,
    avatar_url        TEXT,
    created_by        BIGINT                      NOT NULL REFERENCES users (id),  
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS items_name_organization_id_uidx ON items(name, organization_id);
