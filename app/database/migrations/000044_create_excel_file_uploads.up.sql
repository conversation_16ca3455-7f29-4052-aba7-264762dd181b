CREATE TYPE FILE_PROCESSING_STATUS AS ENUM ('pending', 'processed', 'failed', 'processing');

CREATE TABLE IF NOT EXISTS excel_file_uploads (
    id                    BIGSERIAL                   PRIMARY KEY,    
    uuid                  VARCHAR(250)                NOT NULL, 
    organization_id       BIGINT                      NOT NULL REFERENCES organizations (id),   
    file_name             VARCHAR(250)                NOT NULL, 
    created_by            BIGINT                      NOT NULL REFERENCES users (id),  
    status                FILE_PROCESSING_STATUS      NOT NULL DEFAULT 'pending',
    store_id              BIGINT                      NOT NULL REFERENCES stores (id),  
    records_count         BIGINT                      NOT NULL DEFAULT 0,  
    deleted_at            TIMESTAMP,
    created_at            TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at            TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS excel_file_uploads_uuid_uidx ON excel_file_uploads(uuid);
