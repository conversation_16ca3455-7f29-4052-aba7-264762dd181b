CREATE TYPE PAYMENT_FREQUENCY AS ENUM ('monthly', 'yearly');

CREATE TABLE IF NOT EXISTS user_incomes (
    id                       BIGSERIAL                   PRIMARY KEY, 
    tax_category_id          BIGINT                      NOT NULL REFERENCES tax_categories(id),         
    user_id                  BIGINT                      NOT NULL REFERENCES users(id),
    merchant_name            VARCHAR(255)                NOT NULL DEFAULT '',
    merchant_pin             VARCHAR(50)                 NOT NULL DEFAULT '',
    amount                   NUMERIC(10,2)               NOT NULL,
    currency                 VARCHAR(10)                 NOT NULL DEFAULT 'KES',
    description              TEXT                        NOT NULL DEFAULT '',
    payment_frequency        PAYMENT_FREQUENCY           NOT NULL DEFAULT 'monthly',
    start_date               DATE,
    end_date                 DATE,
    withholding_tax_rate     NUMERIC(5,2)                NOT NULL DEFAULT 0,
    withholding_tax_amount   NUMERIC(10,2)               NOT NULL DEFAULT 0,
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS user_incomes_merchant_name_idx ON user_incomes(merchant_name); 
CREATE INDEX IF NOT EXISTS user_incomes_merchant_pin_idx ON user_incomes(merchant_pin); 
CREATE INDEX IF NOT EXISTS user_incomes_user_id_idx ON user_incomes(user_id); 
