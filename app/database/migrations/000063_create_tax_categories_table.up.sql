CREATE TABLE IF NOT EXISTS tax_categories (
    id                       BIGSERIAL                   PRIMARY KEY, 
    income_source            VARCHAR(250)                NOT NULL,         
    applicable_tax_type_id   BIGINT                      NOT NULL REFERENCES tax_types (id),
    description              TEXT,
    tax_rate                 NUMERIC(15,2),
    has_withholding_tax      BOOLEAN                     NOT NULL,
    due_date                 DATE,    
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS tax_categories_income_source_uidx ON tax_categories(income_source); 


-- Insert current tax categories with respective tax type ids
-- Employment Income – applies to PAYE
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Employment Income', 
    id, 
    'Tax on employment income such as salaries and wages',
    30.00,
    false,
    NULL
FROM tax_types 
WHERE name = 'Pay As You Earn (PAYE)';

-- Dividends – applies to WHT
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Dividends', 
    id, 
    'Tax withheld on dividend payments. 5% for residents; 20% for non-residents',
    5.00,
    true, 
    NULL
FROM tax_types 
WHERE name = 'Withholding Tax (WHT)';

-- Business Turnover – applies to TOT
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Business Income (Sole Proprietorship)', 
    id, 
    'Turnover tax for micro and small businesses',
    1.00,
    false,
    NULL
FROM tax_types 
WHERE name = 'Turnover Tax (TOT)';


-- Rental Income (Residential)
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Rental Income (Residential)', 
    id, 
    'Tax on residential rental income. 7.5% on gross rental income for annual income up to KES 15 million',
    7.50,
    false,
    NULL
FROM tax_types 
WHERE name = 'Residential Rental Income Tax';


-- Rental Income (Commercial)
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Rental Income (Commercial)', 
    id, 
    'Progressive rates: - First KES 288,000: 10% - Next KES 100,000: 25% - Next KES 5,612,000: 30% - Next KES 3,600,000: 32.5% - Above KES 9,600,000: 35% Personal relief: KES 28,800/year',
    10.0,
    false,
    NULL
FROM tax_types 
WHERE name = 'Income Tax';

-- Capital Gain (e.g, property sales)
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Capital Gains', 
    id, 
    'Capital gains tax on property sale. 15% on net gain',
    15.00,
    false,
    NULL
FROM tax_types 
WHERE name = 'Capital Gains Tax (CGT)';

-- General Business Income – applies to Income Tax
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'General Business Income', 
    id, 
    'Standard income tax on business profits',
    30.00,
    false,
    NULL
FROM tax_types 
WHERE name = 'Income Tax';

-- Consultancy / Professional Services
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Consultancy / Professional Services', 
    id, 
    'Consultancy / Professional Services. 5% for residents; 20% for non-residents',
    5.00,
    true, 
    NULL
FROM tax_types 
WHERE name = 'Withholding Tax (WHT)';

-- Digital Content Monetization – applies to TOT
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Digital Content Monetization', 
    id, 
    'Progressive rates: - First KES 288,000: 10% - Next KES 100,000: 25% - Next KES 5,612,000: 30% - Next KES 3,600,000: 32.5% - Above KES 9,600,000: 35% Personal relief: KES 28,800/year',
    10.00,
    true, 
    NULL
FROM tax_types 
WHERE name = 'Turnover Tax (TOT)';

-- Royalties
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Royalties', 
    id, 
    'Royalties. 5% for residents; 20% for non-residents',
    5.00,
    true,
    NULL
FROM tax_types 
WHERE name = 'Withholding Tax (WHT)';


-- Farming Income – applies to Income Tax
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Farming Income', 
    id, 
    'Taxed under normal income tax rates; allowable expenses can be deducted.',
    30.00,
    false,
    NULL
FROM tax_types 
WHERE name = 'Income Tax';

-- Betting, Lottery, Gaming Winnings
INSERT INTO tax_categories (income_source, applicable_tax_type_id, description, tax_rate, has_withholding_tax, due_date)
SELECT 
    'Betting, Lottery, Gaming Winnings', 
    id, 
    'Betting, Lottery, Gaming Winnings. 20% on winnings.',
    20.00,
    true, 
    NULL
FROM tax_types 
WHERE name = 'Withholding Tax (WHT)';

