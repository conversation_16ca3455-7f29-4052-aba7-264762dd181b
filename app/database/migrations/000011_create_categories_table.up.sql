CREATE TABLE IF NOT EXISTS categories (
    id                BIGSERIAL                   PRIMARY KEY,    
    name              VA<PERSON>HA<PERSON>(250)                NOT NULL,
    description       TEXT,    
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),    
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS categories_name_organization_id_uidx ON categories(name, organization_id);
