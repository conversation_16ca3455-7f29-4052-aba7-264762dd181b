CREATE TABLE IF NOT EXISTS api_keys (
    id                BIGSERIAL                   PRIMARY KEY,    
    api_key           VARCHAR(250)                NOT NULL,
    description       TEXT,
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),      
    deleted_at        TIMESTAMP,
    last_used_at      TIMESTAMP WITH TIME ZONE,
    user_id           BIGINT                      NOT NULL REFERENCES users (id),  
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS api_keys_api_key_uidx ON api_keys(api_key);
