-- +goose Up
CREATE TYPE APPLICATION_TYPE AS ENUM ('admin', 'employee', 'customer', 'supplier', 'user');

CREATE TABLE IF NOT EXISTS applications (
  id               BIGSERIAL        PRIMARY KEY,
  application_type APPLICATION_TYPE NOT NULL,
  created_at       TIMESTAMPTZ      NOT NULL DEFAULT clock_timestamp(),
  updated_at       TIMESTAMPTZ      NOT NULL DEFAULT clock_timestamp()
);

CREATE UNIQUE INDEX application_type_idx ON applications(application_type);

INSERT INTO applications (application_type) VALUES ('admin'), ('employee'), ('customer'), ('supplier'), ('user');
