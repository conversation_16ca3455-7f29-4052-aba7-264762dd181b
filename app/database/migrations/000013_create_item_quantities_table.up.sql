CREATE TABLE IF NOT EXISTS item_quantities (
    id                BIGSERIAL                   PRIMARY KEY,        
    item_id           BIGINT                      NOT NULL REFERENCES items (id),    
    quantity          NUMERIC(15,2)               NOT NULL,  
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS item_quantities_item_id_uidx ON item_quantities(item_id);
