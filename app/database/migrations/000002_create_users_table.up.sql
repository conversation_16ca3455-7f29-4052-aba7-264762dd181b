CREATE TABLE IF NOT EXISTS users (
    id                  BIGSERIAL                   PRIMARY KEY,    
    first_name          <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,
    last_name           VA<PERSON>HA<PERSON>(150)                NOT NULL,
    email               VARCHAR(255)                NOT NULL,
    password            VARCHAR(255)                NOT NULL, 
    phone_number        VARCHAR(150),
    is_synced           B<PERSON><PERSON>EAN                     NOT NULL DEFAULT FALSE, 
    organization_id     BIGINT                      NOT NULL REFERENCES organizations (id),    
    deleted_at          TIMESTAMP WITH TIME ZONE, 
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL,
    updated_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS users_email_uidx ON users(email);

-- Create a default admin user 
INSERT INTO users (first_name, last_name, email, password, organization_id, account_number) VALUES 
    ('System', 'Admin', '<EMAIL>', 'admin', 1, 'RC01000');

