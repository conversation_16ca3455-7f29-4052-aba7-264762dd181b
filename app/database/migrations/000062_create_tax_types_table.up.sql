CREATE TABLE IF NOT EXISTS tax_types (
    id                       BIGSERIAL                   PRIMARY KEY, 
    name                     VA<PERSON><PERSON><PERSON>(250)                NOT NULL,         
    acronym                  VARCHAR(50)                 NOT NULL,            
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS tax_types_name_uidx ON tax_types(name); 


-- Insert current tax types
INSERT INTO tax_types (name, acronym) VALUES 
    ('Pay As You Earn (PAYE)', 'PAYE'), 
    ('Withholding Tax (WHT)', 'WHT'),  
    ('Turnover Tax (TOT)', 'TOT'),
    ('Residential Rental Income Tax', 'RRIT'),
    ('Capital Gains Tax (CGT)', 'CGT'),
    ('Income Tax', 'INT');

