CREATE TABLE IF NOT EXISTS employees (
    id                BIGSERIAL                   PRIMARY KEY,    
    user_id           BIGINT                      NOT NULL REFERENCES users (id),
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS employees_organization_id_user_id_uidx ON employees(organization_id, user_id);
