CREATE TABLE IF NOT EXISTS customers (
    id                BIGSERIAL                   PRIMARY KEY,    
    first_name        <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,
    last_name         <PERSON><PERSON><PERSON><PERSON>(150),
    email             VARCHAR(250),
    phone_number      VA<PERSON>HAR(150),    
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS customers_email_organization_id_uidx ON customers(email, organization_id);
