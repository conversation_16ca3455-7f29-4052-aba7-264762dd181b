CREATE TABLE IF NOT EXISTS income_trackings (
    id              BIGSERIAL                   PRIMARY KEY,       
    balance         NUMERIC(15,2)               NOT NULL,
    tracking_date   DATE                        NOT NULL,    
    deleted_at      TIMESTAMP,
    created_at      TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at      TIMESTAMP WITH TIME ZONE 
);

CREATE INDEX IF NOT EXISTS income_trackings_tracking_date_idx ON income_trackings(tracking_date);
