CREATE TABLE IF NOT EXISTS mpesa_deposits (
    id                   BIGSERIAL                   PRIMARY KEY,    
    transaction_type     VARCHAR(150)                NOT NULL,
    transaction_id       VARCHAR(150)                NOT NULL,
    organization_id      BIGINT                      NOT NULL REFERENCES organizations (id),      
    transaction_time     VARCHAR(150)                NOT NULL,
    transaction_amount   VARCHAR(150)                NOT NULL,
    business_short_code  VA<PERSON>HAR(50)                 NOT NULL,
    bill_ref_number      VA<PERSON>HAR(50)                 NOT NULL,
    invoice_number       VARCHAR(50)                 NOT NULL,
    org_account_balance  VARCHAR(50)                 NOT NULL,
    third_party_trans_id VARCHAR(50)                 NOT NULL,
    msisdn               VARCHAR(150)                NOT NULL,
    first_name           VA<PERSON>HAR(150)                NOT NULL,
    middle_name          VARCHAR(150)                NOT NULL,
    last_name            VA<PERSON><PERSON><PERSON>(150)                NOT NULL,
    phone_number_id      BIGINT                      REFERENCES phone_numbers (id),  
    created_at           TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at           TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS mpesa_deposits_transaction_id_uidx ON mpesa_deposits(transaction_id);
