CREATE TYPE PROVIDER_TYPE AS ENUM ('airtel', 'safaricom', 'telkom');

CREATE TABLE IF NOT EXISTS phone_number_hashes (
    id            BIGSERIAL                   PRIMARY KEY,    
    hash          VARCHAR(255)                NOT NULL,
    phone_number  VARCHAR(50)                 NOT NULL,    
    provider      PROVIDER_TYPE               NOT NULL,      
    deleted_at    TIMESTAMP,
    created_at    TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at    TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS phone_number_hashes_phone_number_uidx ON phone_number_hashes(phone_number);
