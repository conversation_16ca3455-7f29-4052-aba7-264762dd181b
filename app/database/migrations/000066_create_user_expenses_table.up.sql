CREATE TABLE IF NOT EXISTS user_expenses (
    id                       BIGSERIAL                   PRIMARY KEY, 
    expense_type             BIGINT                      NOT NULL REFERENCES expense_types(id),         
    user_id                  BIGINT                      NOT NULL REFERENCES users(id),
    merchant_name            VARCHAR(255)                NOT NULL DEFAULT '',
    merchant_pin             VARCHAR(50)                 NOT NULL DEFAULT '',
    amount                   NUMERIC(10,2)               NOT NULL,
    currency                 VARCHAR(10)                 NOT NULL DEFAULT 'KES',
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS user_expenses_merchant_name_idx ON user_expenses(merchant_name); 
CREATE INDEX IF NOT EXISTS user_expenses_merchant_pin_idx ON user_expenses(merchant_pin); 
CREATE INDEX IF NOT EXISTS user_expenses_user_id_idx ON user_expenses(user_id); 
