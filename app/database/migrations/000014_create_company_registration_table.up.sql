CREATE TABLE IF NOT EXISTS company_registrations (
    id                BIGSERIAL                   PRIMARY KEY,    
    reg_id            VARCHAR(150)                NOT NULL,
    serial            VARCHAR(150)                NOT NULL,
    uin               VARCHAR(50),
    tin               VARCHAR(50),
    vrn               VARCHAR(50),
    mobile            VARCHAR(50),
    street            VARCHAR(50),
    city              VARCHAR(50),
    country           VARCHAR(50),
    name              VARCHAR(255),
    receipt_code      VARCHAR(50),
    region            VARCHAR(50),
    routing_key       VARCHAR(50),
    gc                VARCHAR(50),
    tax_office        VARCHAR(150),
    username          VARCHAR(150)                NOT NULL,
    password          VARCHAR(255)                NOT NULL,
    token_path        VARCHAR(255)                NOT NULL,
    efdms_signature   TEXT                        NOT NULL,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX company_registrations_tin_uidx ON company_registrations(tin);
