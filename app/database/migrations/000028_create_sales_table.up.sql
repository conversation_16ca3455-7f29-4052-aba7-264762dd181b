CREATE TYPE PAYMENT_METHOD AS ENUM ('card', 'cash', 'mpesa');

CREATE TYPE SALE_STATUS AS ENUM ('pending', 'complete', 'cancelled');


CREATE TABLE IF NOT EXISTS sales (
    id                    BIGSERIAL                   PRIMARY KEY,    
    uuid                  VARCHAR(250)                NOT NULL,
    customer_id           BIGINT                      REFERENCES customers (id),  
    organization_id       BIGINT                      NOT NULL REFERENCES organizations (id),  
    subtotal              BIGINT                      NOT NULL,  
    vat                   BIGINT,  
    total                 BIGINT                      NOT NULL,  
    total_amount_display  VARCHAR(250)                NOT NULL,  
    scale                 BIGINT,  
    currency_id           BIGINT                      NOT NULL REFERENCES currencies (id),      
    created_by            BIGINT                      NOT NULL REFERENCES users (id),  
    status                SALE_STATUS                 NOT NULL DEFAULT 'pending',
    amount_tendered       BIGINT,  
    change                BIGINT,  
    payment_method        PAYMENT_METHOD              NOT NULL DEFAULT 'cash',
    store_id              BIGINT                      NOT NULL REFERENCES stores (id),  
    created_at            TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at            TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS sales_uuid_uidx ON sales(uuid);
