CREATE TYPE TRANSACTION_TYPE AS ENUM ('credit', 'debit');
CREATE TYPE TRANSACTION_STATUS AS ENUM ('pending', 'success', 'failed');

CREATE TABLE IF NOT EXISTS transactions (
    id                BIGSERIAL                   PRIMARY KEY,    
    user_id           BIGINT                      NOT NULL REFERENCES users(id),         
    transaction_type  TRANSACTION_TYPE            NOT NULL,     
    status            TRANSACTION_STATUS          NOT NULL DEFAULT 'pending',     
    amount            NuMERIC(16,2),    
    description       TEXT, 
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE INDEX IF NOT EXISTS transactions_user_id_idx ON transactions(user_id);
