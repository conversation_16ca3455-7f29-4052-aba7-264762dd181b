
-- +goose Up
CREATE TYPE CURRENCY_STATUS AS ENUM ('active', 'inactive');
CREATE TABLE currencies
(
    id         BIGSERIAL PRIMARY KEY,
    name       VARCHAR(255)    NOT NULL,
    code       VARCHAR(100)    NOT NULL,
    status     CURRENCY_STATUS NOT NULL DEFAULT 'inactive',
    scale      INT             NOT NULL,
    created_at TIMESTAMPTZ     NOT NULL DEFAULT clock_timestamp(),
    updated_at TIMESTAMPTZ     NOT NULL DEFAULT clock_timestamp()
);

CREATE UNIQUE INDEX currencies_code_uniq_idx ON currencies (LOWER(code));

INSERT INTO currencies (name, status, code, scale)
VALUES ('Kenyan Shilling', 'active', 'KES', 2),
       ('Uganda Shilling', 'active', 'UGX', 0),
       ('Tanzanian Shilling', 'active', 'TZS', 2),
       ('US Dollar', 'active', 'USD', 2),
       ('Pound Sterling', 'active', 'GBP', 2),
       ('European Euro', 'active', 'EUR', 2),
       ('Indian Rupee', 'active', 'INR', 3),
       ('Israeli New Sheqel', 'active', 'ILS', 2),
       ('Japanese Yen', 'active', 'JPY', 0),
       ('Jordanian Dinar', 'active', 'JOD', 3),
       ('Qatari Riyal', 'active', 'QAR', 2),
       ('Egyptian Pound', 'active', 'EGP', 2),
       ('Nigerian Naira', 'active', 'NGN', 2),
       ('Rwandan Franc', 'active', 'RWF', 0),
       ('South African Rand', 'active', 'ZAR', 2),
       ('Zambian Kwacha', 'active', 'ZMW', 2),
       ('Australian Dollar', 'active', 'AUD', 2);
