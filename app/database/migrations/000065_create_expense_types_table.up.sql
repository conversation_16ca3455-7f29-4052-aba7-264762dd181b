CREATE TABLE IF NOT EXISTS expense_types (
    id                       BIGSERIAL                   PRIMARY KEY, 
    name                     VARCHAR(250)                NOT NULL,         
    description              TEXT,
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS expense_types_name_uidx ON expense_types(name); 


-- Insert current tax types
INSERT INTO expense_types (name, description) VALUES 
    ('Office Rent and Utilities', 'Costs related to renting an office space and paying for utilities such as electricity, water, and security services necessary for business operations.'), 
    ('Business Travel & Accomodation', 'Expenses incurred while traveling for work purposes, including , transport fares, mileage allowances, and accommodation.'),  
    ('Marketing and Promotional', 'Expenditure on flyers, business cards, digital marketing, and brand awareness campaigns.'),
    ('Stationery and Printing', 'Cost of office supplies like pens, paper, notebooks, and printing materials used for business records and communication.'),
    ('Professional Training/Certs', 'Fees paid for training, seminars, workshops, and professional certification courses that enhance business knowledge and skills.'),
    ('Motor Vehicle Expenses', 'Repairs and Servising of motor vehicles used for business purposes.'),
    ('Insurance', 'Motor Vehicle and others except life insurance(Saving).'),
    ('Power and Fuel', 'Power and Fuel'),
    ('Commissions', 'Commission clawback,commision for referal'),
    ('Advertisement', 'Advertisement'),
    ('Bookkeeping and Audit', 'Bookkeeping and Audit'),
    ('Bad Debt W/O', 'Bad Debt W/O'),
    ('Salaries', 'For employees(that means you pay PAYE for them)'),
    ('Computer Expenses', 'Computer Expenses'),
    ('Telephone', 'Telephone'),
    ('Entertainment', 'Entertainment'),
    ('Legal Expenses', 'Legal Expenses'),
    ('Startup cost/Pre operating', 'Startup cost/Pre operating'),
    ('Workshop (Confrence expenses)', 'Workshop (Confrence expenses)'),
    ('Bank Charges', 'Bank Charges');

