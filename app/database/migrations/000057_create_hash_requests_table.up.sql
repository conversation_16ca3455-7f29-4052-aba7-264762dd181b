CREATE TYPE HASH_REQUEST_STATUS AS ENUM ('pending', 'success', 'failed');

CREATE TABLE IF NOT EXISTS hash_requests (
    id              BIGSERIAL                   PRIMARY KEY,    
    user_id         BIGINT                      NOT NULL REFERENCES users(id),         
    phone_number    VA<PERSON>HAR(50),
    hash            VARCHAR(250),
    status          HASH_REQUEST_STATUS         NOT NULL DEFAULT 'pending',      
    description     TEXT, 
    transaction_id  BIGINT                      REFERENCES transactions(id),        
    deleted_at      TIMESTAMP,
    created_at      TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at      TIMESTAMP WITH TIME ZONE 
);

CREATE INDEX IF NOT EXISTS hash_requests_user_id_phone_number_hash_idx ON hash_requests(user_id, phone_number, hash);
