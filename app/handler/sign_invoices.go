package handler

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/invoicetemplates"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
)

// GetAllInvoices - GetAllInvoices
func GetAllInvoices_DEPRICATED(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	invoices := []models.Invoice{}
	db.Order("Id DESC").Find(&invoices)
	respondJSON(w, http.StatusOK, invoices)
}

// GetAllInvoices - GetAllInvoices
func GetAllInvoices(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	apiResponse := models.InvoicesRepsApiReport{}

	vars := mux.Vars(r)
	limit := utils.ConvertStringToInt64(vars["limit"])
	offset := utils.ConvertStringToInt64(vars["offset"])
	search := vars["search"]

	apiResponse.Limit = limit
	apiResponse.Offset = offset
	apiResponse.Search = search

	reportsFilter := models.ReportsFilter{}
	reportsFilter.Query = search
	invoiceCount := CountInvoices(db, reportsFilter)
	apiResponse.InvoicesCount = invoiceCount

	noPagesOne := float64(invoiceCount) / float64(limit)
	noPages := math.Ceil(float64(noPagesOne))
	apiResponse.NumPages = int64(noPages)

	invoices := []models.Invoice{}

	if search == "query" {
		db.Order("Id DESC").Offset(offset).Limit(limit).Find(&invoices)
	} else {
		apiResponse.NumPages = 1
		s := "%" + search + "%"
		db.Order("Id DESC").Where(`invoice_number LIKE ? OR vat LIKE ? OR grand_total LIKE ? OR signature LIKE ?  `, s, s, s, s).Limit(limit).Find(&invoices)
	}

	apiResponse.Invoices = invoices
	respondJSON(w, http.StatusOK, apiResponse)
}

func ProcessSignedInvoices(db *gorm.DB) {
	for {

		utils.CreateDirectories()

		signedInputFolder := os.Getenv("OLD_SIGNED_INPUT_FOLDER")
		signedOutputFolder := os.Getenv("SIGNED_OUTPUT_FOLDER")

		log.Println("[" + signedInputFolder + "] Checking for signed invoices to process...")

		files, err := ioutil.ReadDir(signedInputFolder + "/")
		if err != nil {
			log.Println(err, "unable to read from input folder.")
		}

		for _, f := range files {

			log.Println("Found File  >>> ", f.Name())

			// if invoice does not end with .pdf, delete it
			srcFile := signedInputFolder + "\\" + f.Name()
			backupFile := signedOutputFolder + "\\" + f.Name()
			destFile := f.Name()
			txtFile := f.Name()
			invoice := &entities.Invoice{}

			if !strings.HasSuffix(f.Name(), ".txt") {
				utils.DeleteFile(srcFile)
			} else {

				// Backup the file
				utils.CopyFile(srcFile, backupFile)

				// Remember to Take care of spaces in file name
				// Copy file to local directory for processing
				utils.CopyFile(srcFile, destFile)

				// Determine the invoice type from the 7 templates
				// Route invoice respectively to correct handler
				invoicetemplates.GetInvoiceType(txtFile, invoice)
				invoice.FileName = backupFile
				invoice.OriginalFileName = f.Name()

				// Save to DB
				log.Println("------------------------------------------------------------------------")
				log.Println("invoice.InvoiceNumber   >>> ", invoice.InvoiceNumber)
				log.Println("invoice.DateCreated     >>> ", invoice.DateCreated)
				log.Println("invoice.Vat             >>> ", invoice.Vat)
				log.Println("invoice.GrandTotal      >>> ", invoice.GrandTotal)
				log.Println("invoice.Signature       >>> ", invoice.Signature)
				log.Println("========================================================================")

				// response := saveInvoiceToDB(invoice)

				// Delete input file(s) after processing
				// if response > 0 {
				// 	utils.DeleteFile(srcFile)
				// 	utils.DeleteFile(destFile)
				// 	utils.DeleteFile(txtFile)
				// }
			}
		}

		utils.DelaySeconds(1) // Check every 2 seconds
	}
}

func SignInvoices_ICEA(db *gorm.DB) {
	for {

		// Read input folder
		// Extract data from PDF
		// Sign - by pasting to ESD Input
		// Retrieve signature
		// Append signature to PDF
		// END

		utils.CreateDirectories()

		inputFolder := os.Getenv("INPUT_FOLDER")
		backupFolder := os.Getenv("BACKUP_FOLDER")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		txtFolder := os.Getenv("TXT_FOLDER")
		esdInputFolder := os.Getenv("ESD_INPUT_FOLDER")
		esdOutputFolder := os.Getenv("ESD_OUTPUT_FOLDER")
		errorFolder := os.Getenv("ERROR_FOLDER")

		log.Println("[" + inputFolder + "] Checking for invoices to sign...")

		files, err := ioutil.ReadDir(inputFolder + "/")
		if err != nil {
			log.Println("Error processing inputFolder contents >>> ", err)
		}

		invoice := &entities.Invoice{}

		for _, f := range files {

			log.Println("Found File  >>> ", f.Name())
			// Delay for specified config seconds to allow for files copying
			delaySeconds := 1
			log.Println("Delaying for [", delaySeconds, "] before processing...")
			utils.DelaySeconds(1)

			srcFile := inputFolder + "/" + f.Name()
			backupFile := backupFolder + "/" + f.Name()
			destFile := f.Name()
			txtFile := f.Name() + ".txt"

			// Backup the file
			utils.CopyFile(srcFile, backupFile)

			// Copy file to local directory for processing
			utils.CopyFile(srcFile, destFile)
			utils.ConvertPDFtoTxt(destFile, txtFile)

			// Determine the invoice type - Route invoice respectively to correct handler
			invoicetemplates.GetInvoiceType(txtFile, invoice)
			invoice.FileName = f.Name()
			invoice.OriginalFileName = f.Name()
			invoice.DateCreated = time.Now()

			convertedDate := utils.ConvertFormatTwoStringDateToDate(invoice.InvoiceDate)
			invoice.DateCreated = convertedDate

			// Save to DB
			log.Println("------------------------------------------------------------------------")
			log.Println("invoice.InvoiceNumber   >>> ", invoice.InvoiceNumber)
			log.Println("invoice.InvoiceDate     >>> ", invoice.InvoiceDate)
			log.Println("invoice.DateCreated     >>> ", invoice.DateCreated)
			log.Println("invoice.Vat             >>> ", invoice.Vat)
			log.Println("invoice.GrandTotal      >>> ", invoice.GrandTotal)
			log.Println("========================================================================")

			// response := saveInvoiceToDB(db, invoice)

			// Sign Invoice
			invoiceContent := "INVOICE\n"
			invoiceContent += "Invoice Number : " + invoice.InvoiceNumber + "\n"
			invoiceContent += "Gross Amount       : " + fmt.Sprintf("%v", invoice.GrossAmount) + "\n"
			invoiceContent += "VAT                : " + fmt.Sprintf("%v", invoice.Vat) + "\n"
			invoiceContent += "Grand Total        : " + fmt.Sprintf("%v", invoice.GrandTotal) + "\n"
			esdTxtFile := txtFolder + "\\" + txtFile
			utils.WriteToTxtFile(esdTxtFile, invoiceContent)
			esdInputFile := esdInputFolder + "\\" + txtFile
			utils.CopyFile(esdTxtFile, esdInputFile)

			// Retrieve signature
			signatureFile := esdOutputFolder + "\\" + txtFile + ".s"
			errorFile := errorFolder + "\\" + txtFile

			// Append signature to PDF
			// srcFile, destFile, signature string
			signedOutputFile := outputFolder + "\\" + destFile
			invoice.FileName = signedOutputFile
			signature := getSignature(signatureFile, errorFile)
			invoice.Signature = signature

			// response = saveInvoiceToDB(db, invoice)
			// log.Println("Invoice signature update response >>> ", response)

			appendSignatureToPDF(backupFile, signedOutputFile, signature)

			utils.DelaySeconds(2)

			// Print the documents to primary and secondary printers
			// PrintDocument - documentURL, printerName
			// utils.PrintDocument(signedOutputFile, primaryPrinter)

			// Delete input file(s) after processing
			// if response > 0 {
			// 	utils.DeleteFile(srcFile)
			// 	utils.DeleteFile(destFile)
			// 	utils.DeleteFile(txtFile)
			// }
		}

		utils.DelaySeconds(1)
	}
}

func SignInvoice_ICEA(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	license := os.Getenv("LICENSE")
	isLicensed := ValidateLicense(license)

	response := models.JSONResponse{}

	if isLicensed {
		// Get Posted invoice Details
		invoice := models.InvoiceReport{}
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&invoice); err != nil {
			respondError(w, http.StatusBadRequest, err.Error())
			return
		}
		defer r.Body.Close()

		log.Println("Signing invoice with invoice number >>>> ", invoice.InvoiceNumber, " :",
			invoice.InvoiceDate, " | ", invoice.GrossAmount, " | ", invoice.Vat, " | ", invoice.GrandTotal)

		// Remember to trim the invoice number
		invoiceNumberString := strings.Replace(invoice.InvoiceNumber, " ", "", -1)
		invoiceFileName := "C:\\ESD_DOCUMENTS\\Backup\\" + invoiceNumberString + ".txt"
		// Create text file - with InvoiceNumber as file name
		invoiceFile, err := os.Create(invoiceFileName)
		utils.CheckError(err, "Error creating invoice file name ")

		invoiceDetails := "TAX INVOICE\n"
		invoiceDetails += "Invoice Date : " + invoice.InvoiceDate + "\n"
		invoiceDetails += "Invoice Number : " + invoice.InvoiceNumber + "\n"
		invoiceDetails += "Gross Amount : " + utils.FloatToString(invoice.GrossAmount) + "\n"
		invoiceDetails += "Vat : " + utils.FloatToString(invoice.Vat) + "\n"
		invoiceDetails += "Grand Total : " + utils.FloatToString(invoice.GrandTotal) + "\n"

		saveData, err2 := invoiceFile.WriteString(invoiceDetails)
		utils.CheckError(err2, "Error saving invoice data ")
		invoiceFile.Close()

		log.Println(saveData, " bytes written successfully.")

		// Sign - Copy to ESD Input
		esdInputFileName := "C:\\ESD_DOCUMENTS\\ESD_Input\\" + invoiceNumberString + ".txt"
		utils.CopyFile(invoiceFileName, esdInputFileName)

		// Retrieve Signature - File from ESD Output with the same filename
		signedInvoiceFileName := "C:\\ESD_DOCUMENTS\\ESD_Output\\" + invoiceNumberString + ".txt.s"
		invoiceErrorFile := "C:\\ESD_DOCUMENTS\\Error_Documents\\" + invoiceNumberString + ".txt"

		for !utils.FileExists(signedInvoiceFileName) && !utils.FileExists(invoiceErrorFile) {
			log.Println("Invoice not signed, waiting...")
			utils.DelaySeconds(1) //  Delay for a second to allow for signing
		}

		if utils.FileExists(signedInvoiceFileName) {
			// Retrieve and set signatue
			log.Println("Retrieving invoice signature...")
			b, err := ioutil.ReadFile(signedInvoiceFileName)
			if err != nil {
				log.Print(err)
			}
			// log.Println(b) // print the content as 'bytes'
			invoiceSignature := string(b) // convert content to a 'string'
			invoiceSignature = strings.Replace(invoiceSignature, "\n", "", -1)
			log.Println("invoiceSignature  >>> ", invoiceSignature)
			invoice.Signature = invoiceSignature
			respondJSON(w, http.StatusOK, invoice)

		} else if utils.FileExists(invoiceErrorFile) {
			// Error file exists
			response.Status = "01"
			response.Description = "Could not sign invoice : Error in invoice details."
			respondJSON(w, http.StatusOK, response)
		} else {
			response.Status = "01"
			response.Description = "Could not sign invoice : Check ESD Connection."
			respondJSON(w, http.StatusOK, response)
		}
	} else {
		response.Status = "01"
		response.Description = "Licenese Expired! Please contact your system provider."
		respondJSON(w, http.StatusOK, response)
	}

}

// RetrieveSignature - RetrieveSignature
func RetrieveSignature_ICEA(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Check License validity
	license := os.Getenv("LICENSE")
	// log.Println("license      >>> ", license)
	isLicensed := ValidateLicense(license)
	// log.Println("isLicensed   >>> ", isLicensed)

	response := models.JSONResponse{}

	if isLicensed {
		// Retrieve parsed invoice number
		vars := mux.Vars(r)
		invoiceNumber := vars["invoice_number"]

		log.Println("Checking signature for invoice number >>>> ", invoiceNumber)

		invoice := models.InvoiceSignature{}
		invoice.InvoiceNumber = invoiceNumber

		// Check whether invoice file name is available in file system
		invoiceNumberString := strings.Replace(invoice.InvoiceNumber, " ", "", -1)
		signedInvoiceFileName := "C:\\ESD_DOCUMENTS\\ESD_Output\\" + invoiceNumberString + ".txt.s"

		// If available, return signature
		// If not - return appropriate message
		invoiceErrorFile := "C:\\ESD_DOCUMENTS\\Error_Documents\\" + invoiceNumberString + ".txt"

		if utils.FileExists(signedInvoiceFileName) {
			// Retrieve and set signatue
			log.Println("Retrieving invoice signature...")
			b, err := ioutil.ReadFile(signedInvoiceFileName)
			if err != nil {
				log.Print(err)
			}
			// log.Println(b) // print the content as 'bytes'
			invoiceSignature := string(b) // convert content to a 'string'
			invoiceSignature = strings.Replace(invoiceSignature, "\n", "", -1)
			log.Println("invoiceSignature  >>> ", invoiceSignature)
			invoice.Signature = invoiceSignature
			respondJSON(w, http.StatusOK, invoice)

		} else if utils.FileExists(invoiceErrorFile) {
			// Error file exists
			response := models.JSONResponse{}
			response.Status = "01"
			response.Description = "Could not sign invoice : Error in invoice details."
			respondJSON(w, http.StatusOK, response)
		} else {
			response := models.JSONResponse{}
			response.Status = "01"
			response.Description = "Invoice not found!"
			respondJSON(w, http.StatusOK, response)
		}
	} else {
		response.Status = "01"
		response.Description = "Licenese Expired! Please contact your system provider."
		respondJSON(w, http.StatusOK, response)
	}
}

func ProcessOldSignedInvoices_ICEA(db *gorm.DB) {
	for {

		utils.CreateDirectories()

		signedInputFolder := os.Getenv("OLD_SIGNED_INPUT_FOLDER")
		signedOutputFolder := os.Getenv("SIGNED_OUTPUT_FOLDER")

		log.Println("[" + signedInputFolder + "] Checking for old signed invoices to process...")

		files, err := ioutil.ReadDir(signedInputFolder + "/")
		if err != nil {
			log.Println(err, "unable to read old signed folder.")
		}

		invoice := &entities.Invoice{}

		for _, f := range files {

			log.Println("Found File  >>> ", f.Name())

			// if invoice does not end with .pdf, delete it
			srcFile := signedInputFolder + "\\" + f.Name()
			backupFile := signedOutputFolder + "\\" + f.Name()
			destFile := f.Name()
			txtFile := f.Name() + ".txt"

			// Backup the file
			utils.CopyFile(srcFile, backupFile)

			// Remember to Take care of spaces in file name
			// Copy file to local directory for processing
			utils.CopyFile(srcFile, destFile)
			utils.ConvertPDFtoTxt(destFile, txtFile)

			// Determine the invoice type from the 7 templates
			// Route invoice respectively to correct handler
			invoicetemplates.GetInvoiceType(txtFile, invoice)
			invoice.FileName = backupFile
			invoice.OriginalFileName = f.Name()

			convertedDate := utils.ConvertFormatTwoStringDateToDate(invoice.InvoiceDate)
			invoice.DateCreated = convertedDate

			// Save to DB
			log.Println("------------------------------------------------------------------------")
			log.Println("invoice.InvoiceNumber   >>> ", invoice.InvoiceNumber)
			log.Println("invoice.InvoiceDate     >>> ", invoice.InvoiceDate)
			log.Println("invoice.DateCreated     >>> ", invoice.DateCreated)
			log.Println("invoice.Vat             >>> ", invoice.Vat)
			log.Println("invoice.GrandTotal      >>> ", invoice.GrandTotal)
			log.Println("invoice.Signature       >>> ", invoice.Signature)
			log.Println("========================================================================")

			// response := saveInvoiceToDB(invoice)

			// Delete input file(s) after processing
			// if response > 0 {
			// 	utils.DeleteFile(srcFile)
			// 	utils.DeleteFile(destFile)
			// 	utils.DeleteFile(txtFile)
			// }
		}

		utils.DelaySeconds(1) // Check every 2 seconds
	}
}

// appendSignatureToPDF - appendSignatureToPDF
func appendSignatureToPDF(srcFile, destFile, signature string) {
	// pdf_utils.exe src.pdf dest.pdf signature
	exec.Command("pdf_utils.exe", srcFile, destFile, signature).Run()
}
