package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
)

const (
	countUserExpensesSQL = `SELECT COUNT(*) AS count FROM user_expenses WHERE deleted_at IS NULL AND user_id=$1`

	createUserExpenseSQL = `INSERT INTO user_expenses (user_id, expense_type_id, merchant_name, merchant_pin, 
		amount, currency, description, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`

	deleteUserExpenseSQL = `UPDATE user_expenses SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectUserExpenseByNameSQL = selectUserExpenseSQL + ` AND name=$1`

	selectUserExpenseByIDSQL = selectUserExpenseSQL + ` AND id=$2`

	selectUserExpenseSQL = `SELECT id, user_id, expense_type_id, merchant_name, merchant_pin, amount, 
		currency, description, created_at, updated_at FROM user_expenses WHERE deleted_at IS NULL AND user_id=$1`

	updateUserExpenseSQL = `UPDATE user_expenses SET amount=$1, currency=$2, description=$3, expense_type_id=$4, 
		merchant_name=$5, merchant_pin=$6, updated_at=$7 WHERE id=$8`
)

type (
	UserExpenseRepository interface {
		CountUserExpenses(context.Context, *entities.PaginationFilter) (int, error)
		CreateUserExpense(UserExpense *entities.UserExpense) error
		Delete(context.Context, *entities.UserExpense) error
		FilterUserExpenses(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.UserExpense, error)
		FindByName(context.Context, string) (*entities.UserExpense, error)
		FindByUserAndID(context.Context, int64, int64) (*entities.UserExpense, error)
		Save(context.Context, *entities.UserExpense) error
	}

	AppUserExpenseRepository struct {
		db *sql.DB
	}
)

func NewUserExpenseRepository(db *sql.DB) UserExpenseRepository {
	return &AppUserExpenseRepository{db: db}
}

func (r *AppUserExpenseRepository) CountUserExpenses(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countUserExpensesSQL
	args = append(args, filter.UserID)
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppUserExpenseRepository) CreateUserExpense(
	userExpense *entities.UserExpense,
) error {

	userExpense.Timestamps.Touch()

	err := r.db.QueryRow(
		createUserExpenseSQL,
		userExpense.UserID,
		userExpense.ExpenseTypeID,
		userExpense.MerchantName,
		userExpense.MerchantPIN,
		userExpense.Amount,
		userExpense.Currency,
		userExpense.CreatedAt,
		userExpense.UpdatedAt,
	).Scan(&userExpense.ID)

	if err != nil {
		log.Printf("error saving user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserExpenseRepository) Delete(
	ctx context.Context,
	UserExpense *entities.UserExpense,
) error {

	_, err := r.db.Exec(
		deleteUserExpenseSQL,
		UserExpense.ID,
	)

	if err != nil {
		log.Printf("error deleting user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserExpenseRepository) FilterUserExpenses(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.UserExpense, error) {

	UserExpenses := make([]*entities.UserExpense, 0)

	args := make([]interface{}, 0)
	query := selectUserExpenseSQL
	args = append(args, filter.UserID)
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return UserExpenses, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoUserExpense(rows)
		if err != nil {
			return UserExpenses, err
		}

		UserExpenses = append(UserExpenses, invoice)
	}

	return UserExpenses, rows.Err()
}

func (r *AppUserExpenseRepository) FindByName(
	ctx context.Context,
	name string,
) (*entities.UserExpense, error) {
	row := r.db.QueryRow(selectUserExpenseByNameSQL, name)
	return r.scanRowIntoUserExpense(row)
}

func (r *AppUserExpenseRepository) FindByUserAndID(
	ctx context.Context,
	userID int64,
	userExpenseID int64,
) (*entities.UserExpense, error) {
	row := r.db.QueryRow(selectUserExpenseByIDSQL, userID, userExpenseID)
	return r.scanRowIntoUserExpense(row)
}

func (r *AppUserExpenseRepository) Save(
	ctx context.Context,
	userExpense *entities.UserExpense,
) error {

	userExpense.Timestamps.Touch()
	var err error

	if userExpense.IsNew() {
		err = r.db.QueryRow(
			createUserExpenseSQL,
			userExpense.UserID,
			userExpense.ExpenseTypeID,
			userExpense.MerchantName,
			userExpense.MerchantPIN,
			userExpense.Amount,
			userExpense.Currency,
			userExpense.Description,
			userExpense.CreatedAt,
			userExpense.UpdatedAt,
		).Scan(&userExpense.ID)

	} else {

		_, err = r.db.Exec(
			updateUserExpenseSQL,
			userExpense.Amount,
			userExpense.Currency,
			userExpense.Description,
			userExpense.ExpenseTypeID,
			userExpense.MerchantName,
			userExpense.MerchantPIN,
			userExpense.UpdatedAt,
			userExpense.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserExpenseRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(merchant_name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppUserExpenseRepository) scanRowIntoUserExpense(
	rowScanner txns_db.RowScanner,
) (*entities.UserExpense, error) {

	var userExpense entities.UserExpense

	err := rowScanner.Scan(
		&userExpense.ID,
		&userExpense.UserID,
		&userExpense.ExpenseTypeID,
		&userExpense.MerchantName,
		&userExpense.MerchantPIN,
		&userExpense.Amount,
		&userExpense.Currency,
		&userExpense.Description,
		&userExpense.CreatedAt,
		&userExpense.UpdatedAt,
	)

	if err != nil {
		return &userExpense, err
	}

	return &userExpense, nil
}
