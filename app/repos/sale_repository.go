package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
	"context"
	"database/sql"
	"fmt"
	"log"
)

const (
	countSalesSQL = `SELECT COUNT(*) AS count FROM sales WHERE store_id=$1`

	countSalesForUserSQL = countSalesSQL + ` AND created_by=$1`

	countSalesForStoreSQL = countSalesSQL + ` AND store_id=$1`

	countSalesForStoreAndUserSQL = countSalesSQL + ` AND store_id=$1 AND created_by=$2`

	createSaleSQL = `INSERT INTO sales (amount_tendered, change, created_by, currency_id, customer_id, organization_id, payment_method, scale, status, store_id, 
		subtotal, total, total_amount_display, uuid, vat, created_at, updated_at) VALUES 
		($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17) RETURNING id`

	filterSalesForUserSQL = selectSaleSQL + ` AND user_id=$2`

	selectSaleByIDSQL = selectSaleSQL + ` AND id=$2`

	selectSaleByUUIDSQL = selectSaleSQL + ` AND uuid=$2`

	selectSaleSQL = `SELECT id, amount_tendered, change, created_by, currency_id, customer_id, organization_id, payment_method, scale, status, store_id, 
		subtotal, total, total_amount_display, uuid, vat, created_at, updated_at FROM sales WHERE store_id=$1`

	updateSaleSQL = `UPDATE sales SET amount_tendered=$1, change=$2, currency_id=$3, customer_id=$4, payment_method=$5, scale=$6, status=$7, subtotal=$8, total=$9, 
		total_amount_display=$10, uuid=$11, vat=$12, updated_at=$13 WHERE id=$14`
)

type (
	SaleRepository interface {
		CountSales(context.Context, *entities.PaginationFilter, int64) (int, error)
		CountSalesForStore(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) (int, error)
		CountSalesForStoreAndUser(context.Context, txns_db.TransactionsSQLOperations, int64, int64, *entities.PaginationFilter) (int, error)
		CountSalesForUser(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) (int, error)
		FilterSalesForStore(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) ([]*entities.Sale, error)
		FilterSalesForUser(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) ([]*entities.Sale, error)
		FindByUUID(context.Context, string) (*entities.Sale, error)
		FindByID(context.Context, int64, int64) (*entities.Sale, error)
		GetSaleByID(int64) (*entities.Sale, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.Sale) error
	}

	AppSaleRepository struct {
		db *sql.DB
	}
)

func NewSaleRepository(db *sql.DB) SaleRepository {
	return &AppSaleRepository{db: db}
}

func (r *AppSaleRepository) CountSales(
	ctx context.Context,
	filter *entities.PaginationFilter,
	storeId int64,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countSalesSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}
	return count, nil
}

func (r *AppSaleRepository) CountSalesForUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, userID)
	currentIndex := 1

	query, args, _ := r.buildQueryFromFilter(countSalesForUserSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		utils.Log.Infof("unable to count sales for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppSaleRepository) CountSalesForStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeID)
	currentIndex := 1

	query, args, _ := r.buildQueryFromFilter(countSalesForStoreSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		utils.Log.Infof("unable to count sales for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppSaleRepository) CountSalesForStoreAndUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	userID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeID, userID)
	currentIndex := 2

	query, args, _ := r.buildQueryFromFilter(countSalesForStoreAndUserSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		utils.Log.Infof("unable to count sales for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppSaleRepository) FilterSalesForStore(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeID int64,
	filter *entities.PaginationFilter,
) ([]*entities.Sale, error) {

	categories := make([]*entities.Sale, 0)

	args := make([]interface{}, 0)
	args = append(args, storeID)
	query := selectSaleSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSale(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppSaleRepository) FilterSalesForUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) ([]*entities.Sale, error) {

	categories := make([]*entities.Sale, 0)

	args := make([]interface{}, 0)
	args = append(args, userID)
	query := filterSalesForUserSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSale(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppSaleRepository) FindByUUID(
	ctx context.Context,
	uuid string,
) (*entities.Sale, error) {
	row := r.db.QueryRow(selectSaleByUUIDSQL, uuid)
	return r.scanRowIntoSale(row)
}

func (r *AppSaleRepository) FindByID(
	ctx context.Context,
	saleID int64,
	storeId int64,
) (*entities.Sale, error) {
	row := r.db.QueryRow(selectSaleByIDSQL, storeId, saleID)
	return r.scanRowIntoSale(row)
}

func (r *AppSaleRepository) GetSaleByID(saleID int64) (*entities.Sale, error) {
	row := r.db.QueryRow(selectSaleByIDSQL, saleID)
	return r.scanRowIntoSale(row)
}

func (r *AppSaleRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	sale *entities.Sale,
) error {

	sale.Timestamps.Touch()
	var err error

	if sale.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createSaleSQL,
			sale.AmountTendered,
			sale.Change,
			sale.CreatedBy,
			sale.CurrencyID,
			sale.CustomerID,
			sale.OrganizationID,
			sale.PaymentMethod,
			sale.Scale,
			sale.Status,
			sale.StoreID,
			sale.Subtotal,
			sale.Total,
			sale.TotalAmountDisplay,
			sale.UUID,
			sale.Vat,
			sale.CreatedAt,
			sale.UpdatedAt,
		)

		err = res.Scan(&sale.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateSaleSQL,
			sale.AmountTendered,
			sale.Change,
			sale.CurrencyID,
			sale.CustomerID,
			sale.PaymentMethod,
			sale.Scale,
			sale.Status,
			sale.Subtotal,
			sale.Total,
			sale.TotalAmountDisplay,
			sale.UUID,
			sale.Vat,
			sale.UpdatedAt,
			sale.ID,
		)
	}

	if err != nil {
		log.Printf("error saving sale, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppSaleRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppSaleRepository) scanRowIntoSale(
	rowScanner txns_db.RowScanner,
) (*entities.Sale, error) {

	var sale entities.Sale

	// selectSaleSQL = `SELECT id, amount_tendered, change, created_by, currency_id, customer_id, organization_id, payment_method, scale, status, store_id,
	// subtotal, total, total_amount_display, uuid, vat, created_at, updated_at FROM sales`

	err := rowScanner.Scan(
		&sale.ID,
		&sale.AmountTendered,
		&sale.Change,
		&sale.CreatedBy,
		&sale.CurrencyID,
		&sale.CustomerID,
		&sale.OrganizationID,
		&sale.PaymentMethod,
		&sale.Scale,
		&sale.Status,
		&sale.StoreID,
		&sale.Subtotal,
		&sale.Total,
		&sale.TotalAmountDisplay,
		&sale.UUID,
		&sale.Vat,
		&sale.CreatedAt,
		&sale.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning sale,  err=[%v]\n", err.Error())
		return &sale, err
	}

	return &sale, nil
}
