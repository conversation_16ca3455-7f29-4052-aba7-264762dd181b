package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
)

const (
	countExcelFileUploadsSQL = `SELECT COUNT(*) AS count FROM excel_file_uploads WHER<PERSON> deleted_at IS NULL AND store_id=$1`

	createExcelFileUploadSQL = `INSERT INTO excel_file_uploads (uuid, organization_id, file_name, created_by, status, 
		store_id, records_count, created_at, updated_at) VALUES
		($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`

	deleteExcelFileUploadByIDSQL = `UPDATE excel_file_uploads SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectExcelFileUploadByIDSQL = selectExcelFileUploadSQL + ` AND id=$1`

	selectExcelFileUploadByKeySQL = selectExcelFileUploadSQL + ` AND api_key=$1`

	selectExcelFileUploadSQL = `SELECT id, uuid, organization_id, file_name, created_by, status, 
		store_id, records_count, created_at, updated_at FROM excel_file_uploads WHERE deleted_at IS NULL`

	updateExcelFileUploadSQL = `UPDATE excel_file_uploads SET status=$1, records_count=$2, updated_at=$3 WHERE id=$4`

	updateExcelFileUploadLastUsedAtSQL = `UPDATE excel_file_uploads SET last_used_at=now(), updated_at=now() WHERE id=$1`
)

type (
	ExcelFileUploadRepository interface {
		CountExcelFileUploads(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		Delete(ctx context.Context, excelFileUpload *entities.ExcelFileUpload) error
		FilterExcelFileUploads(ctx context.Context, db txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.ExcelFileUpload, error)
		FindByID(ctx context.Context, excelFileUploadID int64) (*entities.ExcelFileUpload, error)
		FindByKey(ctx context.Context, key string) (*entities.ExcelFileUpload, error)
		Save(ctx context.Context, excelFileUpload *entities.ExcelFileUpload) error
		UpdateLastUsedAt(ctx context.Context, excelFileUpload *entities.ExcelFileUpload) error
	}

	AppExcelFileUploadRepository struct {
		db *sql.DB
	}
)

func NewExcelFileUploadRepository(db *sql.DB) ExcelFileUploadRepository {
	return &AppExcelFileUploadRepository{db: db}
}

func (r *AppExcelFileUploadRepository) CountExcelFileUploads(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, filter.StoreID)
	query := countExcelFileUploadsSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppExcelFileUploadRepository) Delete(
	ctx context.Context,
	excelFileUpload *entities.ExcelFileUpload,
) error {
	_, err := r.db.ExecContext(ctx, deleteExcelFileUploadByIDSQL, excelFileUpload.ID)
	return err
}

func (r *AppExcelFileUploadRepository) FilterExcelFileUploads(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.ExcelFileUpload, error) {

	fileUploads := make([]*entities.ExcelFileUpload, 0)

	args := make([]interface{}, 0)
	query := selectExcelFileUploadSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return fileUploads, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoExcelFileUpload(rows)
		if err != nil {
			return fileUploads, err
		}

		fileUploads = append(fileUploads, invoice)
	}

	return fileUploads, rows.Err()
}

func (r *AppExcelFileUploadRepository) FindByID(
	ctx context.Context,
	excelFileUploadID int64,
) (*entities.ExcelFileUpload, error) {
	row := r.db.QueryRow(selectExcelFileUploadByIDSQL, excelFileUploadID)
	return r.scanRowIntoExcelFileUpload(row)
}

func (r *AppExcelFileUploadRepository) FindByKey(
	ctx context.Context,
	key string,
) (*entities.ExcelFileUpload, error) {
	row := r.db.QueryRow(selectExcelFileUploadByKeySQL, key)
	return r.scanRowIntoExcelFileUpload(row)
}

func (r *AppExcelFileUploadRepository) Save(
	ctx context.Context,
	excelFileUpload *entities.ExcelFileUpload,
) error {

	excelFileUpload.Timestamps.Touch()
	var err error

	if excelFileUpload.IsNew() {

		err = r.db.QueryRow(
			createExcelFileUploadSQL,
			excelFileUpload.UUID,
			excelFileUpload.OrganizationID,
			excelFileUpload.FileName,
			excelFileUpload.CreatedBy,
			excelFileUpload.Status,
			excelFileUpload.StoreID,
			excelFileUpload.RecordsCount,
			excelFileUpload.CreatedAt,
			excelFileUpload.UpdatedAt,
		).Scan(&excelFileUpload.ID)

	} else {

		_, err = r.db.Exec(
			updateExcelFileUploadSQL,
			excelFileUpload.Status,
			excelFileUpload.RecordsCount,
			excelFileUpload.UpdatedAt,
			excelFileUpload.ID,
		)
	}

	if err != nil {
		log.Printf("error saving api_key, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppExcelFileUploadRepository) UpdateLastUsedAt(
	ctx context.Context,
	excelFileUpload *entities.ExcelFileUpload,
) error {
	_, err := r.db.ExecContext(ctx, updateExcelFileUploadLastUsedAtSQL, excelFileUpload.ID)
	return err
}

func (r *AppExcelFileUploadRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.StoreID > 0 {
		query += fmt.Sprintf(" AND store_id = $%d", currentIndex)
		args = append(args, filter.StoreID)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Status)) > 0 {
		query += fmt.Sprintf(" AND status = $%d", currentIndex)
		args = append(args, filter.Status)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(api_key) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppExcelFileUploadRepository) scanRowIntoExcelFileUpload(
	rowScanner txns_db.RowScanner,
) (*entities.ExcelFileUpload, error) {

	var excelFileUpload entities.ExcelFileUpload

	err := rowScanner.Scan(
		&excelFileUpload.ID,
		&excelFileUpload.UUID,
		&excelFileUpload.OrganizationID,
		&excelFileUpload.FileName,
		&excelFileUpload.CreatedBy,
		&excelFileUpload.Status,
		&excelFileUpload.StoreID,
		&excelFileUpload.RecordsCount,
		&excelFileUpload.CreatedAt,
		&excelFileUpload.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning excelFileUpload,  err=[%v]\n", err.Error())
		return &excelFileUpload, err
	}

	return &excelFileUpload, nil
}
