package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"
)

const (
	countphoneNumberHashesSQL = `SELECT COUNT(*) AS count FROM phone_number_hashes WHERE deleted_at IS NULL`

	insertPhoneNumberHashSQL = `INSERT INTO phone_number_hashes (hash, phone_number, provider, created_at, updated_at) VALUES`

	createphoneNumberHashesQL = insertPhoneNumberHashSQL + ` ($1, $2, $3, $4, $5) RETURNING id`

	deletePhoneNumberHashByIDSQL = `UPDATE phone_number_hashes SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectPhoneNumberHashByIDAndStoreIDSQL = selectphoneNumberHashQL + ` AND id=$1`

	selectPhoneNumberHashByHashSQL = selectphoneNumberHashQL + ` AND hash=$1`

	selectPhoneNumberHashByPhoneNumberSQL = selectphoneNumberHashQL + ` AND phone_number=$1`

	filterphoneNumberHashesSQL = selectphoneNumberHashQL + ``

	selectphoneNumberHashQL = `SELECT id, hash, phone_number, provider, deleted_at, created_at, updated_at 
		FROM phone_number_hashes WHERE deleted_at IS NULL`

	updatephoneNumberHashesQL = `UPDATE phone_number_hashes SET hash=$1, updated_at=$2 WHERE id=$3`
)

type (
	PhoneNumberHashRepository interface {
		CountPhoneNumberHashes(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		Delete(ctx context.Context, phoneNumberHash *entities.PhoneNumberHash) error
		FilterPhoneNumberHashes(ctx context.Context, db txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.PhoneNumberHash, error)
		FindByID(ctx context.Context, phoneNumberHashID int64) (*entities.PhoneNumberHash, error)
		FindByHash(ctx context.Context, hash string) (*entities.PhoneNumberHash, error)
		FindByPhoneNumber(ctx context.Context, phoneNumber string) (*entities.PhoneNumberHash, error)
		Save(ctx context.Context, phoneNumberHash *entities.PhoneNumberHash) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, phoneNumberHashes []*entities.PhoneNumberHash) error
	}

	AppPhoneNumberHashRepository struct {
		db *sql.DB
	}
)

func NewPhoneNumberHashRepository(db *sql.DB) PhoneNumberHashRepository {
	return &AppPhoneNumberHashRepository{db: db}
}

func (r *AppPhoneNumberHashRepository) CountPhoneNumberHashes(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countphoneNumberHashesSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppPhoneNumberHashRepository) Delete(
	ctx context.Context,
	phoneNumberHash *entities.PhoneNumberHash,
) error {
	_, err := r.db.ExecContext(ctx, deletePhoneNumberHashByIDSQL, phoneNumberHash.ID)
	return err
}

func (r *AppPhoneNumberHashRepository) FilterPhoneNumberHashes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.PhoneNumberHash, error) {

	categories := make([]*entities.PhoneNumberHash, 0)

	args := make([]interface{}, 0)
	query := filterphoneNumberHashesSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoPhoneNumberHash(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppPhoneNumberHashRepository) FindByID(
	ctx context.Context,
	phoneNumberHashID int64,
) (*entities.PhoneNumberHash, error) {
	row := r.db.QueryRow(selectPhoneNumberHashByIDAndStoreIDSQL, phoneNumberHashID)
	return r.scanRowIntoPhoneNumberHash(row)
}

func (r *AppPhoneNumberHashRepository) FindByHash(
	ctx context.Context,
	hash string,
) (*entities.PhoneNumberHash, error) {
	row := r.db.QueryRow(selectPhoneNumberHashByHashSQL, hash)
	return r.scanRowIntoPhoneNumberHash(row)
}

func (r *AppPhoneNumberHashRepository) FindByPhoneNumber(
	ctx context.Context,
	phoneNumber string,
) (*entities.PhoneNumberHash, error) {
	row := r.db.QueryRow(selectPhoneNumberHashByPhoneNumberSQL, phoneNumber)
	return r.scanRowIntoPhoneNumberHash(row)
}

func (r *AppPhoneNumberHashRepository) Save(
	ctx context.Context,
	phoneNumberHash *entities.PhoneNumberHash,
) error {

	phoneNumberHash.Timestamps.Touch()
	var err error

	if phoneNumberHash.IsNew() {

		err = r.db.QueryRow(
			createphoneNumberHashesQL,
			phoneNumberHash.Hash,
			phoneNumberHash.PhoneNumber,
			phoneNumberHash.Provider,
			phoneNumberHash.CreatedAt,
			phoneNumberHash.UpdatedAt,
		).Scan(&phoneNumberHash.ID)

	} else {

		_, err = r.db.Exec(
			updatephoneNumberHashesQL,
			phoneNumberHash.Hash,
			phoneNumberHash.UpdatedAt,
			phoneNumberHash.ID,
		)
	}

	if err != nil {
		log.Printf("error saving api_key, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppPhoneNumberHashRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	phoneNumberHashes []*entities.PhoneNumberHash,
) error {

	query := insertPhoneNumberHashSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(phoneNumberHashes))

	for index, phoneNumberHashes := range phoneNumberHashes {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4)
		currentIndex += 5
		args = append(
			args,
			phoneNumberHashes.Hash,
			phoneNumberHashes.PhoneNumber,
			phoneNumberHashes.Provider,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(phone_number) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&phoneNumberHashes[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppPhoneNumberHashRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(phone_number) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(hash) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppPhoneNumberHashRepository) scanRowIntoPhoneNumberHash(
	rowScanner txns_db.RowScanner,
) (*entities.PhoneNumberHash, error) {

	var phoneNumberHash entities.PhoneNumberHash

	err := rowScanner.Scan(
		&phoneNumberHash.ID,
		&phoneNumberHash.Hash,
		&phoneNumberHash.PhoneNumber,
		&phoneNumberHash.Provider,
		&phoneNumberHash.DeletedAt,
		&phoneNumberHash.CreatedAt,
		&phoneNumberHash.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning phoneNumberHash,  err=[%v]\n", err.Error())
		return &phoneNumberHash, err
	}

	return &phoneNumberHash, nil
}
