package repos

import (
	"context"
	"fmt"
	"strings"

	db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
)

const (
	countCurrenciesSQL = `SELECT COUNT(*) FROM currencies`

	selectCurrenciesSQL = `SELECT id, name, code, status, scale, created_at, updated_at FROM currencies`

	selectCurrencyByIDSQL = selectCurrenciesSQL + " WHERE id = $1"

	selectCurrenciesByIDsSQL = selectCurrenciesSQL + " WHERE id IN "
)

type (
	CurrencyRepository interface {
		CountCurrencies(context.Context, db.TransactionsSQLOperations, *entities.PaginationFilter) (int, error)
		FilterCurrencies(context.Context, db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.Currency, error)
		FindByID(context.Context, db.TransactionsSQLOperations, int64) (*entities.Currency, error)
		ListByIDs(context.Context, db.TransactionsSQLOperations, []int64) ([]*entities.Currency, error)
	}

	AppCurrencyRepository struct{}
)

func NewCurrencyRepository() CurrencyRepository {
	return &AppCurrencyRepository{}
}

func (r *AppCurrencyRepository) CountCurrencies(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (int, error) {

	var count int
	args := []interface{}{}
	query := countCurrenciesSQL

	err := operations.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return count, utils.NewDatabaseError(
			err,
			"Failed to query currencies count with filter=[%v]",
			filter,
		)
	}

	return count, nil
}

func (r *AppCurrencyRepository) FilterCurrencies(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Currency, error) {

	currencies := make([]*entities.Currency, 0)

	query := selectCurrenciesSQL
	currentIndex := 1
	args := []interface{}{}

	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return currencies, err
	}

	for rows.Next() {
		currency, err := r.scanRowIntoCurrency(rows)
		if err != nil {
			return currencies, err
		}

		currencies = append(currencies, currency)
	}

	return currencies, rows.Err()
}

func (r *AppCurrencyRepository) FindByID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	currencyID int64,
) (*entities.Currency, error) {

	row := operations.QueryRowContext(ctx, selectCurrencyByIDSQL, currencyID)
	return r.scanRowIntoCurrency(row)
}

func (r *AppCurrencyRepository) ListByIDs(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	currencyIDs []int64,
) ([]*entities.Currency, error) {

	currencies := make([]*entities.Currency, 0)

	query := selectCurrenciesByIDsSQL
	args := make([]interface{}, 0)

	values := make([]string, len(currencyIDs))
	for index, currencyID := range currencyIDs {
		values[index] = fmt.Sprintf("$%d", index+1)
		args = append(args, currencyID)
	}

	query += " (" + strings.Join(values, ",") + ")"
	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return currencies, err
	}

	defer rows.Close()

	for rows.Next() {
		currency, err := r.scanRowIntoCurrency(rows)
		if err != nil {
			return currencies, err
		}

		currencies = append(currencies, currency)
	}

	return currencies, rows.Err()
}

func (r *AppCurrencyRepository) scanRowIntoCurrency(
	rowScanner db.RowScanner,
) (*entities.Currency, error) {

	var currency entities.Currency

	err := rowScanner.Scan(
		&currency.ID,
		&currency.Name,
		&currency.Code,
		&currency.Status,
		&currency.Scale,
		&currency.Timestamps.CreatedAt,
		&currency.Timestamps.UpdatedAt,
	)

	if err != nil {
		return &currency, utils.NewDatabaseError(
			err,
			"currency scan error",
		)
	}

	return &currency, nil
}
