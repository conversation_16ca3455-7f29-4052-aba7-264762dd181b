package repos

import (
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strconv"
	"strings"
)

const (
	countExpenseTypesSQL = `SELECT COUNT(*) AS count FROM expense_types WHERE deleted_at IS NULL`

	createExpenseTypeSQL = `INSERT INTO expense_types (name, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4) RETURNING id`

	deleteExpenseTypeSQL = `UPDATE expense_types SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectExpenseTypeByNameSQL = selectExpenseTypeSQL + ` AND name=$1`

	selectExpenseTypeByIDSQL = selectExpenseTypeSQL + ` AND id=$1`

	selectExpenseTypeSQL = `SELECT id, name, description, created_at, updated_at FROM expense_types WHERE deleted_at IS NULL`

	updateExpenseTypeSQL = `UPDATE expense_types SET name=$1, description=$2, updated_at=$3 WHERE id=$4`
)

type (
	ExpenseTypeRepository interface {
		CountExpenseTypes(context.Context, *entities.PaginationFilter) (int, error)
		CreateExpenseType(expenseType *entities.ExpenseType) error
		Delete(context.Context, *entities.ExpenseType) error
		FilterExpenseTypes(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.ExpenseType, error)
		FindByName(context.Context, string) (*entities.ExpenseType, error)
		FindByID(context.Context, int64) (*entities.ExpenseType, error)
		FindByIDs(ctx context.Context, operations txns_db.TransactionsSQLOperations, expenseTypeIDs []int64) ([]*entities.ExpenseType, error)
		Save(context.Context, *entities.ExpenseType) error
	}

	AppExpenseTypeRepository struct {
		db *sql.DB
	}
)

func NewExpenseTypeRepository(db *sql.DB) ExpenseTypeRepository {
	return &AppExpenseTypeRepository{db: db}
}

func (r *AppExpenseTypeRepository) CountExpenseTypes(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countExpenseTypesSQL
	currentIndex := 1

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppExpenseTypeRepository) CreateExpenseType(
	expenseType *entities.ExpenseType,
) error {

	expenseType.Timestamps.Touch()

	err := r.db.QueryRow(
		createExpenseTypeSQL,
		expenseType.Name,
		expenseType.Description,
		expenseType.CreatedAt,
		expenseType.UpdatedAt,
	).Scan(&expenseType.ID)

	if err != nil {
		log.Printf("error saving expenseType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppExpenseTypeRepository) Delete(
	ctx context.Context,
	expenseType *entities.ExpenseType,
) error {

	_, err := r.db.Exec(
		deleteExpenseTypeSQL,
		expenseType.ID,
	)

	if err != nil {
		log.Printf("error deleting expenseType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppExpenseTypeRepository) FilterExpenseTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.ExpenseType, error) {

	expenses := make([]*entities.ExpenseType, 0)

	args := make([]interface{}, 0)
	query := selectExpenseTypeSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return expenses, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoExpenseType(rows)
		if err != nil {
			return expenses, err
		}

		expenses = append(expenses, invoice)
	}

	return expenses, rows.Err()
}

func (r *AppExpenseTypeRepository) FindByName(
	ctx context.Context,
	name string,
) (*entities.ExpenseType, error) {
	row := r.db.QueryRow(selectExpenseTypeByNameSQL, name)
	return r.scanRowIntoExpenseType(row)
}

func (r *AppExpenseTypeRepository) FindByID(
	ctx context.Context,
	expenseTypeID int64,
) (*entities.ExpenseType, error) {
	row := r.db.QueryRow(selectExpenseTypeByIDSQL, expenseTypeID)
	return r.scanRowIntoExpenseType(row)
}

func (r *AppExpenseTypeRepository) FindByIDs(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	expenseTypeIDs []int64,
) ([]*entities.ExpenseType, error) {

	expenseTypes := make([]*entities.ExpenseType, 0)
	args := make([]interface{}, 0)
	currentIndex := 1

	if len(expenseTypeIDs) == 0 {
		return expenseTypes, nil
	}

	expenseTypeIDsQuery := ""
	for _, assetID := range expenseTypeIDs {
		expenseTypeIDsQuery += "$" + strconv.Itoa(currentIndex) + ","
		args = append(args, assetID)
		currentIndex++
	}
	expenseTypeIDsQuery = strings.TrimRight(expenseTypeIDsQuery, ",")
	query := selectExpenseTypeSQL + " AND id IN (" + expenseTypeIDsQuery + ")"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return expenseTypes, apperr.NewDatabaseError(err)
	}

	defer rows.Close()

	for rows.Next() {
		asset, err := r.scanRowIntoExpenseType(rows)
		if err != nil {
			return expenseTypes, apperr.NewDatabaseError(err)
		}

		expenseTypes = append(expenseTypes, asset)
	}

	return expenseTypes, apperr.WrapNilableDatabaseError(rows.Err())
}

func (r *AppExpenseTypeRepository) Save(
	ctx context.Context,
	expenseType *entities.ExpenseType,
) error {

	expenseType.Timestamps.Touch()
	var err error

	if expenseType.IsNew() {
		err = r.db.QueryRow(
			createExpenseTypeSQL,
			expenseType.Name,
			expenseType.Description,
			expenseType.CreatedAt,
			expenseType.UpdatedAt,
		).Scan(&expenseType.ID)

	} else {

		_, err = r.db.Exec(
			updateExpenseTypeSQL,
			expenseType.Name,
			expenseType.Description,
			expenseType.UpdatedAt,
			expenseType.ID,
		)
	}

	if err != nil {
		log.Printf("error saving expenseType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppExpenseTypeRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppExpenseTypeRepository) scanRowIntoExpenseType(
	rowScanner txns_db.RowScanner,
) (*entities.ExpenseType, error) {

	var expenseType entities.ExpenseType

	err := rowScanner.Scan(
		&expenseType.ID,
		&expenseType.Name,
		&expenseType.Description,
		&expenseType.CreatedAt,
		&expenseType.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning expenseType,  err=[%v]\n", err.Error())
		return &expenseType, err
	}

	return &expenseType, nil
}
