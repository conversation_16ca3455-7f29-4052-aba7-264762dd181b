package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
)

const (
	countTaxTypesSQL = `SELECT COUNT(*) AS count FROM tax_types WHERE deleted_at IS NULL`

	createTaxTypeSQL = `INSERT INTO tax_types (name, acronym, created_at, updated_at)
		VALUES ($1, $2, $3, $4) RETURNING id`

	deleteTaxTypeSQL = `UPDATE tax_types SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectTaxTypeByNameSQL = selectTaxTypeSQL + ` AND name=$1`

	selectTaxTypeByIDSQL = selectTaxTypeSQL + ` AND id=$1`

	selectTaxTypeSQL = `SELECT id, name, acronym, created_at, updated_at FROM tax_types WHERE deleted_at IS NULL`

	updateTaxTypeSQL = `UPDATE tax_types SET name=$1, acronym=$2, updated_at=$3 WHERE id=$4`
)

type (
	TaxTypeRepository interface {
		CountTaxTypes(context.Context, *entities.PaginationFilter) (int, error)
		CreateTaxType(TaxType *entities.TaxType) error
		Delete(context.Context, *entities.TaxType) error
		FilterTaxTypes(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.TaxType, error)
		FindByName(context.Context, string) (*entities.TaxType, error)
		FindByID(context.Context, int64) (*entities.TaxType, error)
		Save(context.Context, *entities.TaxType) error
	}

	AppTaxTypeRepository struct {
		db *sql.DB
	}
)

func NewTaxTypeRepository(db *sql.DB) TaxTypeRepository {
	return &AppTaxTypeRepository{db: db}
}

func (r *AppTaxTypeRepository) CountTaxTypes(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countTaxTypesSQL
	currentIndex := 1

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppTaxTypeRepository) CreateTaxType(
	taxType *entities.TaxType,
) error {

	taxType.Timestamps.Touch()

	err := r.db.QueryRow(
		createTaxTypeSQL,
		taxType.Name,
		taxType.Acronym,
		taxType.CreatedAt,
		taxType.UpdatedAt,
	).Scan(&taxType.ID)

	if err != nil {
		log.Printf("error saving taxType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxTypeRepository) Delete(
	ctx context.Context,
	taxType *entities.TaxType,
) error {

	_, err := r.db.Exec(
		deleteTaxTypeSQL,
		taxType.ID,
	)

	if err != nil {
		log.Printf("error deleting taxType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxTypeRepository) FilterTaxTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.TaxType, error) {

	taxTypes := make([]*entities.TaxType, 0)

	args := make([]interface{}, 0)
	query := selectTaxTypeSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return taxTypes, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoTaxType(rows)
		if err != nil {
			return taxTypes, err
		}

		taxTypes = append(taxTypes, invoice)
	}

	return taxTypes, rows.Err()
}

func (r *AppTaxTypeRepository) FindByName(
	ctx context.Context,
	name string,
) (*entities.TaxType, error) {
	row := r.db.QueryRow(selectTaxTypeByNameSQL, name)
	return r.scanRowIntoTaxType(row)
}

func (r *AppTaxTypeRepository) FindByID(
	ctx context.Context,
	taxTypeID int64,
) (*entities.TaxType, error) {
	row := r.db.QueryRow(selectTaxTypeByIDSQL, taxTypeID)
	return r.scanRowIntoTaxType(row)
}

func (r *AppTaxTypeRepository) Save(
	ctx context.Context,
	taxType *entities.TaxType,
) error {

	taxType.Timestamps.Touch()
	var err error

	if taxType.IsNew() {
		err = r.db.QueryRow(
			createTaxTypeSQL,
			taxType.Name,
			taxType.Acronym,
			taxType.CreatedAt,
			taxType.UpdatedAt,
		).Scan(&taxType.ID)

	} else {

		_, err = r.db.Exec(
			updateTaxTypeSQL,
			taxType.Name,
			taxType.Acronym,
			taxType.UpdatedAt,
			taxType.ID,
		)
	}

	if err != nil {
		log.Printf("error saving taxType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxTypeRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(acronym) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppTaxTypeRepository) scanRowIntoTaxType(
	rowScanner txns_db.RowScanner,
) (*entities.TaxType, error) {

	var taxType entities.TaxType

	err := rowScanner.Scan(
		&taxType.ID,
		&taxType.Name,
		&taxType.Acronym,
		&taxType.CreatedAt,
		&taxType.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning taxType,  err=[%v]\n", err.Error())
		return &taxType, err
	}

	return &taxType, nil
}
