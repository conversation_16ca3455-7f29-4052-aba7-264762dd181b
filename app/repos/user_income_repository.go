package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
)

const (
	countUserIncomesSQL = `SELECT COUNT(*) AS count FROM user_incomes WHERE deleted_at IS NULL AND user_id=$1`

	createUserIncomeSQL = `INSERT INTO user_incomes (user_id, tax_category_id, merchant_name, merchant_pin, 
		amount, currency, description, payment_frequency, start_date, end_date, withholding_tax_rate, 
		withholding_tax_amount, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 
		$12, $13, $14) RETURNING id`

	deleteUserIncomeSQL = `UPDATE user_incomes SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectUserIncomeByIDSQL = selectUserIncomeSQL + ` AND id=$2`

	selectUserIncomeSQL = `SELECT id, user_id, tax_category_id, merchant_name, merchant_pin, amount, 
		currency, description, payment_frequency, start_date, end_date, withholding_tax_rate, 
		withholding_tax_amount, created_at, updated_at FROM user_incomes 
		WHERE deleted_at IS NULL AND user_id=$1`

	updateUserIncomeSQL = `UPDATE user_incomes SET amount=$1, currency=$2, description=$3, tax_category_id=$4, 
		merchant_name=$5, merchant_pin=$6, payment_frequency=$7, start_date=$8, end_date=$9, updated_at=$10 WHERE id=$11`
)

type (
	UserIncomeRepository interface {
		CountUserIncomes(context.Context, *entities.PaginationFilter) (int, error)
		CreateUserIncome(userIncome *entities.UserIncome) error
		Delete(context.Context, *entities.UserIncome) error
		FilterUserIncomes(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.UserIncome, error)
		FindByUserAndID(context.Context, int64, int64) (*entities.UserIncome, error)
		Save(context.Context, *entities.UserIncome) error
	}

	AppUserIncomeRepository struct {
		db *sql.DB
	}
)

func NewUserIncomeRepository(db *sql.DB) UserIncomeRepository {
	return &AppUserIncomeRepository{db: db}
}

func (r *AppUserIncomeRepository) CountUserIncomes(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countUserIncomesSQL
	args = append(args, filter.UserID)
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppUserIncomeRepository) CreateUserIncome(
	userIncome *entities.UserIncome,
) error {

	userIncome.Timestamps.Touch()

	err := r.db.QueryRow(
		createUserIncomeSQL,
		userIncome.UserID,
		userIncome.TaxCategoryID,
		userIncome.MerchantName,
		userIncome.MerchantPIN,
		userIncome.Amount,
		userIncome.Currency,
		userIncome.CreatedAt,
		userIncome.UpdatedAt,
	).Scan(&userIncome.ID)

	if err != nil {
		log.Printf("error saving user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserIncomeRepository) Delete(
	ctx context.Context,
	UserIncome *entities.UserIncome,
) error {

	_, err := r.db.Exec(
		deleteUserIncomeSQL,
		UserIncome.ID,
	)

	if err != nil {
		log.Printf("error deleting user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserIncomeRepository) FilterUserIncomes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.UserIncome, error) {

	UserIncomes := make([]*entities.UserIncome, 0)

	args := make([]interface{}, 0)
	query := selectUserIncomeSQL
	args = append(args, filter.UserID)
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return UserIncomes, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoUserIncome(rows)
		if err != nil {
			return UserIncomes, err
		}

		UserIncomes = append(UserIncomes, invoice)
	}

	return UserIncomes, rows.Err()
}

func (r *AppUserIncomeRepository) FindByUserAndID(
	ctx context.Context,
	userID int64,
	userIncomeID int64,
) (*entities.UserIncome, error) {
	row := r.db.QueryRow(selectUserIncomeByIDSQL, userID, userIncomeID)
	return r.scanRowIntoUserIncome(row)
}

func (r *AppUserIncomeRepository) Save(
	ctx context.Context,
	userIncome *entities.UserIncome,
) error {

	userIncome.Timestamps.Touch()
	var err error

	if userIncome.IsNew() {
		err = r.db.QueryRow(
			createUserIncomeSQL,
			userIncome.UserID,
			userIncome.TaxCategoryID,
			userIncome.MerchantName,
			userIncome.MerchantPIN,
			userIncome.Amount,
			userIncome.Currency,
			userIncome.Description,
			userIncome.PaymentFrequency,
			userIncome.StartDate,
			userIncome.EndDate,
			userIncome.WithholdingTaxRate,
			userIncome.WithholdingTaxAmount,
			userIncome.CreatedAt,
			userIncome.UpdatedAt,
		).Scan(&userIncome.ID)

	} else {

		_, err = r.db.Exec(
			updateUserIncomeSQL,
			userIncome.Amount,
			userIncome.Currency,
			userIncome.Description,
			userIncome.TaxCategoryID,
			userIncome.MerchantName,
			userIncome.MerchantPIN,
			userIncome.PaymentFrequency,
			userIncome.StartDate,
			userIncome.EndDate,
			userIncome.UpdatedAt,
			userIncome.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user_expense, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserIncomeRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(merchant_name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppUserIncomeRepository) scanRowIntoUserIncome(
	rowScanner txns_db.RowScanner,
) (*entities.UserIncome, error) {

	var userIncome entities.UserIncome

	err := rowScanner.Scan(
		&userIncome.ID,
		&userIncome.UserID,
		&userIncome.TaxCategoryID,
		&userIncome.MerchantName,
		&userIncome.MerchantPIN,
		&userIncome.Amount,
		&userIncome.Currency,
		&userIncome.Description,
		&userIncome.PaymentFrequency,
		&userIncome.StartDate,
		&userIncome.EndDate,
		&userIncome.WithholdingTaxRate,
		&userIncome.WithholdingTaxAmount,
		&userIncome.CreatedAt,
		&userIncome.UpdatedAt,
	)

	if err != nil {
		return &userIncome, err
	}

	return &userIncome, nil
}
