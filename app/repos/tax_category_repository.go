package repos

import (
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strconv"
	"strings"
)

const (
	countTaxCategoriesSQL = `SELECT COUNT(*) AS count FROM tax_categories WHERE deleted_at IS NULL`

	createTaxCategorySQL = `INSERT INTO tax_categories (applicable_tax_type_id, description, due_date, has_withholding_tax, 
		income_source, tax_rate, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id`

	deleteTaxCategorySQL = `UPDATE tax_categories SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectTaxCategoryByIncomeSourceSQL = selectTaxCategorySQL + ` AND income_source=$1`

	selectTaxCategoryByIDSQL = selectTaxCategorySQL + ` AND id=$1`

	selectTaxCategorySQL = `SELECT id, applicable_tax_type_id, description, due_date, has_withholding_tax, income_source, tax_rate, 
		created_at, updated_at FROM tax_categories WHERE deleted_at IS NULL`

	updateTaxCategorySQL = `UPDATE tax_categories SET applicable_tax_type_id=$1, description=$2, due_date=$3, has_withholding_tax=$4, 
		income_source=$5, tax_rate=$6, updated_at=$7 WHERE id=$8`
)

type (
	TaxCategoryRepository interface {
		CountTaxCategories(context.Context, *entities.PaginationFilter) (int, error)
		CreateTaxCategory(TaxCategory *entities.TaxCategory) error
		Delete(context.Context, *entities.TaxCategory) error
		FilterTaxCategories(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.TaxCategory, error)
		FindByName(context.Context, string) (*entities.TaxCategory, error)
		FindByID(context.Context, int64) (*entities.TaxCategory, error)
		FindByIDs(ctx context.Context, operations txns_db.TransactionsSQLOperations, taxCategoryIDs []int64) ([]*entities.TaxCategory, error)
		Save(context.Context, *entities.TaxCategory) error
	}

	AppTaxCategoryRepository struct {
		db *sql.DB
	}
)

func NewTaxCategoryRepository(db *sql.DB) TaxCategoryRepository {
	return &AppTaxCategoryRepository{db: db}
}

func (r *AppTaxCategoryRepository) CountTaxCategories(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countTaxCategoriesSQL
	currentIndex := 1

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppTaxCategoryRepository) CreateTaxCategory(
	taxCategory *entities.TaxCategory,
) error {

	taxCategory.Timestamps.Touch()

	err := r.db.QueryRow(
		createTaxCategorySQL,
		taxCategory.ApplicableTaxType,
		taxCategory.Description,
		taxCategory.DueDate,
		taxCategory.HasWithholdingTax,
		taxCategory.IncomeSource,
		taxCategory.TaxRate,
		taxCategory.CreatedAt,
		taxCategory.UpdatedAt,
	).Scan(&taxCategory.ID)

	if err != nil {
		log.Printf("error saving taxCategory, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxCategoryRepository) Delete(
	ctx context.Context,
	taxCategory *entities.TaxCategory,
) error {

	_, err := r.db.Exec(
		deleteTaxCategorySQL,
		taxCategory.ID,
	)

	if err != nil {
		log.Printf("error deleting taxCategory, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxCategoryRepository) FilterTaxCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.TaxCategory, error) {

	taxCategories := make([]*entities.TaxCategory, 0)

	args := make([]interface{}, 0)
	query := selectTaxCategorySQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return taxCategories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoTaxCategory(rows)
		if err != nil {
			return taxCategories, err
		}

		taxCategories = append(taxCategories, invoice)
	}

	return taxCategories, rows.Err()
}

func (r *AppTaxCategoryRepository) FindByName(
	ctx context.Context,
	name string,
) (*entities.TaxCategory, error) {
	row := r.db.QueryRow(selectTaxCategoryByIncomeSourceSQL, name)
	return r.scanRowIntoTaxCategory(row)
}

func (r *AppTaxCategoryRepository) FindByID(
	ctx context.Context,
	taxCategoryID int64,
) (*entities.TaxCategory, error) {
	row := r.db.QueryRow(selectTaxCategoryByIDSQL, taxCategoryID)
	return r.scanRowIntoTaxCategory(row)
}

func (r *AppTaxCategoryRepository) FindByIDs(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	taxCategoryIDs []int64,
) ([]*entities.TaxCategory, error) {

	taxCategories := make([]*entities.TaxCategory, 0)
	args := make([]interface{}, 0)
	currentIndex := 1

	if len(taxCategoryIDs) == 0 {
		return taxCategories, nil
	}

	taxCategoryIDsQuery := ""
	for _, assetID := range taxCategoryIDs {
		taxCategoryIDsQuery += "$" + strconv.Itoa(currentIndex) + ","
		args = append(args, assetID)
		currentIndex++
	}
	taxCategoryIDsQuery = strings.TrimRight(taxCategoryIDsQuery, ",")
	query := selectTaxCategorySQL + " AND id IN (" + taxCategoryIDsQuery + ")"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return taxCategories, apperr.NewDatabaseError(err)
	}

	defer rows.Close()

	for rows.Next() {
		asset, err := r.scanRowIntoTaxCategory(rows)
		if err != nil {
			return taxCategories, apperr.NewDatabaseError(err)
		}

		taxCategories = append(taxCategories, asset)
	}

	return taxCategories, apperr.WrapNilableDatabaseError(rows.Err())
}

func (r *AppTaxCategoryRepository) Save(
	ctx context.Context,
	taxCategory *entities.TaxCategory,
) error {

	taxCategory.Timestamps.Touch()
	var err error

	if taxCategory.IsNew() {
		err = r.db.QueryRow(
			createTaxCategorySQL,
			taxCategory.ApplicableTaxTypeID,
			taxCategory.Description,
			taxCategory.DueDate,
			taxCategory.HasWithholdingTax,
			taxCategory.IncomeSource,
			taxCategory.TaxRate,
			taxCategory.CreatedAt,
			taxCategory.UpdatedAt,
		).Scan(&taxCategory.ID)

	} else {

		_, err = r.db.Exec(
			updateTaxCategorySQL,
			taxCategory.ApplicableTaxTypeID,
			taxCategory.Description,
			taxCategory.DueDate,
			taxCategory.HasWithholdingTax,
			taxCategory.IncomeSource,
			taxCategory.TaxRate,
			taxCategory.UpdatedAt,
			taxCategory.ID,
		)
	}

	if err != nil {
		log.Printf("error saving taxCategory, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTaxCategoryRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(income_source) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppTaxCategoryRepository) scanRowIntoTaxCategory(
	rowScanner txns_db.RowScanner,
) (*entities.TaxCategory, error) {

	var taxCategory entities.TaxCategory

	err := rowScanner.Scan(
		&taxCategory.ID,
		&taxCategory.ApplicableTaxTypeID,
		&taxCategory.Description,
		&taxCategory.DueDate,
		&taxCategory.HasWithholdingTax,
		&taxCategory.IncomeSource,
		&taxCategory.TaxRate,
		&taxCategory.CreatedAt,
		&taxCategory.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning taxCategory,  err=[%v]\n", err.Error())
		return &taxCategory, err
	}

	return &taxCategory, nil
}
