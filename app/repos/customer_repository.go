package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
)

const (
	countCustomersSQL = `SELECT COUNT(*) AS count FROM customers WHERE store_id=$1`

	createCustomerSQL = ` INSERT INTO customers (first_name, last_name, email, phone_number, organization_id, store_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id`

	selectCustomerByEmail = selectCustomerSQL + ` AND email=$2`

	selectCustomerByIDSQL = selectCustomerSQL + ` AND id=$2`

	selectCustomerSQL = `SELECT id, first_name, last_name, email, phone_number, organization_id, store_id, created_at, updated_at FROM customers WHERE store_id=$1`

	updateCustomerSQL = `UPDATE customers SET first_name=$1, last_name=$2, email=$3, phone_number=$4, updated_at=$5 WHERE id=$6`
)

type (
	CustomerRepository interface {
		CountCustomers(context.Context, *entities.PaginationFilter, int64) (int, error)
		FilterCustomers(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter, int64) ([]*entities.Customer, error)
		FindByEmailAndStore(context.Context, string, int64) (*entities.Customer, error)
		FindByID(context.Context, int64, int64) (*entities.Customer, error)
		GetCustomerByID(int64) (*entities.Customer, error)
		Save(context.Context, *entities.Customer) error
	}

	AppCustomerRepository struct {
		db *sql.DB
	}
)

func NewCustomerRepository(db *sql.DB) CustomerRepository {
	return &AppCustomerRepository{db: db}
}

func (r *AppCustomerRepository) CountCustomers(
	ctx context.Context,
	filter *entities.PaginationFilter,
	storeId int64,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countCustomersSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppCustomerRepository) FilterCustomers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) ([]*entities.Customer, error) {

	categories := make([]*entities.Customer, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := selectCustomerSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoCustomer(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppCustomerRepository) FindByEmailAndStore(
	ctx context.Context,
	email string,
	storeId int64,
) (*entities.Customer, error) {
	row := r.db.QueryRow(selectCustomerByEmail, storeId, email)
	return r.scanRowIntoCustomer(row)
}

func (r *AppCustomerRepository) FindByID(
	ctx context.Context,
	customerID int64,
	storeId int64,
) (*entities.Customer, error) {
	row := r.db.QueryRow(selectCustomerByIDSQL, storeId, customerID)
	return r.scanRowIntoCustomer(row)
}

func (r *AppCustomerRepository) GetCustomerByID(customerID int64) (*entities.Customer, error) {
	row := r.db.QueryRow(selectCustomerByIDSQL, customerID)
	return r.scanRowIntoCustomer(row)
}

func (r *AppCustomerRepository) Save(
	ctx context.Context,
	customer *entities.Customer,
) error {

	customer.Timestamps.Touch()
	var err error

	if customer.IsNew() {

		err = r.db.QueryRow(
			createCustomerSQL,
			customer.FirstName,
			customer.LastName,
			customer.Email,
			customer.PhoneNumber,
			customer.OrganizationID,
			customer.StoreID,
			customer.CreatedAt,
			customer.UpdatedAt,
		).Scan(&customer.ID)

	} else {

		_, err = r.db.Exec(
			updateCustomerSQL,
			customer.FirstName,
			customer.LastName,
			customer.Email,
			customer.PhoneNumber,
			customer.UpdatedAt,
			customer.ID,
		)
	}

	if err != nil {
		log.Printf("error saving customer, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppCustomerRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(first_name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(last_name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(email) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(phone_number) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppCustomerRepository) scanRowIntoCustomer(
	rowScanner txns_db.RowScanner,
) (*entities.Customer, error) {

	var customer entities.Customer

	err := rowScanner.Scan(
		&customer.ID,
		&customer.FirstName,
		&customer.LastName,
		&customer.Email,
		&customer.PhoneNumber,
		&customer.OrganizationID,
		&customer.StoreID,
		&customer.CreatedAt,
		&customer.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning customer,  err=[%v]\n", err.Error())
		return &customer, err
	}

	return &customer, nil
}
