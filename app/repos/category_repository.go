package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
)

const (
	countCategoriesSQL = `SELECT COUNT(*) AS count FROM categories WHERE store_id=$1`

	createCategorySQL = `INSERT INTO categories (name, description, organization_id, store_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`

	selectCategoryByNameAndOrganizationSQL = selectCategorySQL + ` AND name=$2 AND organization_id=$3`

	selectCategoryByNameAndStoreIDSQL = selectCategorySQL + ` AND name=$2`

	selectCategoryByIDSQL = selectCategorySQL + ` AND id=$2`

	selectCategorySQL = `SELECT id, name, description, organization_id, store_id, created_at, updated_at FROM categories WHERE store_id=$1`

	updateCategorySQL = `UPDATE categories SET name=$1, description=$2, updated_at=$3 WHERE id=$4`
)

type (
	CategoryRepository interface {
		CountCategories(context.Context, *entities.PaginationFilter, int64) int
		CreateCategory(category *entities.Category) error
		FilterCategories(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter, int64) ([]*entities.Category, error)
		FindByNameAndOganization(context.Context, string, int64) (*entities.Category, error)
		FindByNameAndStore(context.Context, string, int64) (*entities.Category, error)
		FindByID(context.Context, int64) (*entities.Category, error)
		GetCategoryByID(int64) (*entities.Category, error)
		Save(context.Context, *entities.Category) error
	}

	AppCategoryRepository struct {
		db *sql.DB
	}
)

func NewCategoryRepository(db *sql.DB) CategoryRepository {
	return &AppCategoryRepository{db: db}
}

func (r *AppCategoryRepository) CountCategories(
	ctx context.Context,
	filter *entities.PaginationFilter,
	storeId int64,
) int {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countCategoriesSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppCategoryRepository) CreateCategory(
	category *entities.Category,
) error {

	category.Timestamps.Touch()

	err := r.db.QueryRow(
		createCategorySQL,
		category.Name,
		category.Description,
		category.OrganizationID,
		category.StoreID,
		category.CreatedAt,
		category.UpdatedAt,
	).Scan(&category.ID)

	if err != nil {
		log.Printf("error saving category, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppCategoryRepository) FilterCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) ([]*entities.Category, error) {

	categories := make([]*entities.Category, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := selectCategorySQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoCategory(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppCategoryRepository) FindByNameAndOganization(
	ctx context.Context,
	name string,
	organizationID int64,
) (*entities.Category, error) {
	row := r.db.QueryRow(selectCategoryByNameAndOrganizationSQL, name, organizationID)
	return r.scanRowIntoCategory(row)
}

func (r *AppCategoryRepository) FindByNameAndStore(
	ctx context.Context,
	name string,
	storeID int64,
) (*entities.Category, error) {
	row := r.db.QueryRow(selectCategoryByNameAndStoreIDSQL, storeID, name)
	return r.scanRowIntoCategory(row)
}

func (r *AppCategoryRepository) FindByID(
	ctx context.Context,
	categoryID int64,
) (*entities.Category, error) {
	row := r.db.QueryRow(selectCategoryByIDSQL, categoryID)
	return r.scanRowIntoCategory(row)
}

func (r *AppCategoryRepository) GetCategoryByID(categoryID int64) (*entities.Category, error) {
	row := r.db.QueryRow(selectCategoryByIDSQL, categoryID)
	return r.scanRowIntoCategory(row)
}

func (r *AppCategoryRepository) Save(
	ctx context.Context,
	category *entities.Category,
) error {

	category.Timestamps.Touch()
	var err error

	if category.IsNew() {
		err = r.db.QueryRow(
			createCategorySQL,
			category.Name,
			category.Description,
			category.OrganizationID,
			category.StoreID,
			category.CreatedAt,
			category.UpdatedAt,
		).Scan(&category.ID)

	} else {

		_, err = r.db.Exec(
			updateCategorySQL,
			category.Name,
			category.Description,
			category.UpdatedAt,
			category.ID,
		)
	}

	if err != nil {
		log.Printf("error saving category, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppCategoryRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppCategoryRepository) scanRowIntoCategory(
	rowScanner txns_db.RowScanner,
) (*entities.Category, error) {

	var category entities.Category

	err := rowScanner.Scan(
		&category.ID,
		&category.Name,
		&category.Description,
		&category.OrganizationID,
		&category.StoreID,
		&category.CreatedAt,
		&category.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning category,  err=[%v]\n", err.Error())
		return &category, err
	}

	return &category, nil
}
