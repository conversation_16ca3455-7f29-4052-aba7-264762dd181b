package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
)

const (
	countWithholdingTaxRatesSQL = `SELECT COUNT(*) AS count FROM withholding_tax_rates WHERE deleted_at IS NULL`

	createWithholdingTaxRateSQL = `INSERT INTO withholding_tax_rates (created_by, description, name, tax_category_id, 
		withholding_tax_rate, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	deleteWithholdingTaxRateSQL = `UPDATE withholding_tax_rates SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectWithholdingTaxRateByNameSQL = selectWithholdingTaxRateSQL + ` AND name=$1`

	selectWithholdingTaxRateByIDSQL = selectWithholdingTaxRateSQL + ` AND id=$1`

	selectWithholdingTaxRateSQL = `SELECT id, created_by, description, name, tax_category_id, withholding_tax_rate, 
		created_at, updated_at FROM withholding_tax_rates WHERE deleted_at IS NULL`

	updateWithholdingTaxRateSQL = `UPDATE withholding_tax_rates SET description=$1, name=$2, tax_category_id=$3, 
		withholding_tax_rate=$4, updated_at=$5 WHERE id=$6`
)

type (
	WithholdingTaxRateRepository interface {
		CountWithholdingTaxRates(context.Context, *entities.PaginationFilter) (int, error)
		CreateWithholdingTaxRate(WithholdingTaxRate *entities.WithholdingTaxRate) error
		Delete(context.Context, *entities.WithholdingTaxRate) error
		FilterWithholdingTaxRates(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.WithholdingTaxRate, error)
		FindByName(context.Context, string) (*entities.WithholdingTaxRate, error)
		FindByID(context.Context, int64) (*entities.WithholdingTaxRate, error)
		Save(context.Context, *entities.WithholdingTaxRate) error
	}

	AppWithholdingTaxRateRepository struct {
		db *sql.DB
	}
)

func NewWithholdingTaxRateRepository(db *sql.DB) WithholdingTaxRateRepository {
	return &AppWithholdingTaxRateRepository{db: db}
}

func (r *AppWithholdingTaxRateRepository) CountWithholdingTaxRates(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	query := countWithholdingTaxRatesSQL
	currentIndex := 1

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppWithholdingTaxRateRepository) CreateWithholdingTaxRate(
	withholdingTaxRate *entities.WithholdingTaxRate,
) error {

	withholdingTaxRate.Timestamps.Touch()

	err := r.db.QueryRow(
		createWithholdingTaxRateSQL,
		withholdingTaxRate.CreatedBy,
		withholdingTaxRate.Description,
		withholdingTaxRate.Name,
		withholdingTaxRate.TaxCategoryID,
		withholdingTaxRate.WithholdingTaxRate,
		withholdingTaxRate.CreatedAt,
		withholdingTaxRate.UpdatedAt,
	).Scan(&withholdingTaxRate.ID)

	if err != nil {
		log.Printf("error saving withholdingTaxRate, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppWithholdingTaxRateRepository) Delete(
	ctx context.Context,
	withholdingTaxRate *entities.WithholdingTaxRate,
) error {

	_, err := r.db.Exec(
		deleteWithholdingTaxRateSQL,
		withholdingTaxRate.ID,
	)

	if err != nil {
		log.Printf("error deleting withholdingTaxRate, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppWithholdingTaxRateRepository) FilterWithholdingTaxRates(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.WithholdingTaxRate, error) {

	withholdingTaxRates := make([]*entities.WithholdingTaxRate, 0)

	args := make([]interface{}, 0)
	query := selectWithholdingTaxRateSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return withholdingTaxRates, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoWithholdingTaxRate(rows)
		if err != nil {
			return withholdingTaxRates, err
		}

		withholdingTaxRates = append(withholdingTaxRates, invoice)
	}

	return withholdingTaxRates, rows.Err()
}

func (r *AppWithholdingTaxRateRepository) FindByName(
	ctx context.Context,
	name string,
) (*entities.WithholdingTaxRate, error) {
	row := r.db.QueryRow(selectWithholdingTaxRateByNameSQL, name)
	return r.scanRowIntoWithholdingTaxRate(row)
}

func (r *AppWithholdingTaxRateRepository) FindByID(
	ctx context.Context,
	withholdingTaxRateID int64,
) (*entities.WithholdingTaxRate, error) {
	row := r.db.QueryRow(selectWithholdingTaxRateByIDSQL, withholdingTaxRateID)
	return r.scanRowIntoWithholdingTaxRate(row)
}

func (r *AppWithholdingTaxRateRepository) Save(
	ctx context.Context,
	withholdingTaxRate *entities.WithholdingTaxRate,
) error {

	withholdingTaxRate.Timestamps.Touch()
	var err error

	if withholdingTaxRate.IsNew() {

		err = r.db.QueryRow(
			createWithholdingTaxRateSQL,
			withholdingTaxRate.CreatedBy,
			withholdingTaxRate.Description,
			withholdingTaxRate.Name,
			withholdingTaxRate.TaxCategory,
			withholdingTaxRate.WithholdingTaxRate,
			withholdingTaxRate.CreatedAt,
			withholdingTaxRate.UpdatedAt,
		).Scan(&withholdingTaxRate.ID)

	} else {

		_, err = r.db.Exec(
			updateWithholdingTaxRateSQL,
			withholdingTaxRate.Description,
			withholdingTaxRate.Name,
			withholdingTaxRate.TaxCategoryID,
			withholdingTaxRate.WithholdingTaxRate,
			withholdingTaxRate.UpdatedAt,
			withholdingTaxRate.ID,
		)
	}

	if err != nil {
		log.Printf("error saving withholdingTaxRate, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppWithholdingTaxRateRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppWithholdingTaxRateRepository) scanRowIntoWithholdingTaxRate(
	rowScanner txns_db.RowScanner,
) (*entities.WithholdingTaxRate, error) {

	var withholdingTaxRate entities.WithholdingTaxRate

	err := rowScanner.Scan(
		&withholdingTaxRate.ID,
		&withholdingTaxRate.CreatedBy,
		&withholdingTaxRate.Description,
		&withholdingTaxRate.Name,
		&withholdingTaxRate.TaxCategoryID,
		&withholdingTaxRate.WithholdingTaxRate,
		&withholdingTaxRate.CreatedAt,
		&withholdingTaxRate.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning withholdingTaxRate,  err=[%v]\n", err.Error())
		return &withholdingTaxRate, err
	}

	return &withholdingTaxRate, nil
}
