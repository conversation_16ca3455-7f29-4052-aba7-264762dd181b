package repos

import (
	db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
	"context"
	"database/sql"
	"fmt"
	"log"
)

const (
	countUsersSQL = `SELECT COUNT(*) AS count FROM users WHERE 1=1`

	createUserSQL = `INSERT INTO users (email, first_name, last_name, password, is_synced, organization_id, 
		account_number, phone_number_id, phone_number_str, created_at, updated_at) VALUES 
		($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING id`

	selectLastUserSQL = selectUserSQL + ` ORDER BY id DESC LIMIT 1`

	selectUserByAccountNumberSQL = selectUserSQL + ` WHERE account_number=$1`

	selectUserByEmailSQL = selectUserSQL + ` WHERE email=$1`

	selectUserByIDSQL = selectUserSQL + ` WHERE id=$1`

	selectUserSQL = `SELECT id, email, first_name, last_name, password, is_synced, organization_id, account_number, phone_number_id, 
		COALESCE(phone_number_str, '') AS phone_number_str, created_at, updated_at FROM users`

	updateUserSQL = `UPDATE users SET first_name=$1, last_name=$2, phone_number_id=$3, phone_number_str=$4, updated_at=$5 WHERE id = $6`
)

type (
	UserRepository interface {
		CountUsers(ctx context.Context, filter *entities.QueryFilter) int
		CreateUser(user *entities.User) error
		FindByEmail(ctx context.Context, operations db.TransactionsSQLOperations, email string) (*entities.User, error)
		FindByAccountNumber(ctx context.Context, operations db.TransactionsSQLOperations, accountNumber string) (*entities.User, error)
		FindByID(ctx context.Context, userID int64) (*entities.User, error)
		GetLastUser(ctx context.Context, operations db.TransactionsSQLOperations) (*entities.User, error)
		GetUserByID(userID int64) (*entities.User, error)
		Save(ctx context.Context, user *entities.User) error
	}

	AppUserRepository struct {
		db *sql.DB
	}
)

func NewUserRepository(db *sql.DB) UserRepository {
	return &AppUserRepository{db: db}
}

func (r *AppUserRepository) CountUsers(
	ctx context.Context,
	filter *entities.QueryFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countUsersSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppUserRepository) CreateUser(
	user *entities.User,
) error {

	user.Timestamps.Touch()

	err := r.db.QueryRow(
		createUserSQL,
		user.Email,
		user.FirstName,
		user.LastName,
		user.Password,
		user.IsSynced,
		user.OrganizationID,
		user.AccountNumber,
		user.CreatedAt,
		user.UpdatedAt,
	).Scan(&user.ID)

	if err != nil {
		log.Printf("error saving user, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserRepository) FindByEmail(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	email string,
) (*entities.User, error) {
	row := operations.QueryRowContext(ctx, selectUserByEmailSQL, email)
	return r.scanRowIntoUser(row)
}

func (r *AppUserRepository) FindByAccountNumber(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	accountNumber string,
) (*entities.User, error) {
	row := operations.QueryRowContext(ctx, selectUserByAccountNumberSQL, accountNumber)
	return r.scanRowIntoUser(row)
}

func (r *AppUserRepository) FindByID(
	ctx context.Context,
	userID int64,
) (*entities.User, error) {
	row := r.db.QueryRow(selectUserByIDSQL, userID)
	return r.scanRowIntoUser(row)
}

func (r *AppUserRepository) GetLastUser(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
) (*entities.User, error) {
	row := operations.QueryRowContext(ctx, selectLastUserSQL)
	return r.scanRowIntoUser(row)
}

func (r *AppUserRepository) GetUserByID(userID int64) (*entities.User, error) {
	row := r.db.QueryRow(selectUserByIDSQL, userID)
	return r.scanRowIntoUser(row)
}

func (r *AppUserRepository) Save(
	ctx context.Context,
	user *entities.User,
) error {

	user.Timestamps.Touch()
	var err error

	if user.IsNew() {
		err = r.db.QueryRow(
			createUserSQL,
			user.Email,
			user.FirstName,
			user.LastName,
			user.Password,
			user.IsSynced,
			user.OrganizationID,
			user.AccountNumber,
			user.PhoneNumberID,
			user.PhoneNumberStr,
			user.CreatedAt,
			user.UpdatedAt,
		).Scan(&user.ID)
	} else {

		_, err = r.db.Exec(
			updateUserSQL,
			user.FirstName,
			user.LastName,
			user.PhoneNumberID,
			user.PhoneNumberStr,
			user.UpdatedAt,
			user.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserRepository) buildQueryFromFilter(
	query string,
	filter *entities.QueryFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppUserRepository) scanRowIntoUser(
	rowScanner db.RowScanner,
) (*entities.User, error) {

	var user entities.User

	err := rowScanner.Scan(
		&user.ID,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.Password,
		&user.IsSynced,
		&user.OrganizationID,
		&user.AccountNumber,
		&user.PhoneNumberID,
		&user.PhoneNumberStr,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		utils.Log.Infof("error scanning user,  err=[%v]", err.Error())
		return &user, err
	}

	return &user, nil
}
