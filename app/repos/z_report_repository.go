package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"

	txns_db "compliance-and-risk-management-backend/app/database"
)

const (
	countZReportsSQL = `SELECT COUNT(*) AS count FROM z_reports WHERE 1=1`

	insertZReportSQL = ` INSERT INTO z_reports (z_report_date, z_report_time, invoice_count, net_amount, vat_amount, pmt_amount, 
		status, total_amount, z_report_payload, is_synced, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) RETURNING id`

	selectZReportSQL = `SELECT id, z_report_date, z_report_time, invoice_count, net_amount, vat_amount, pmt_amount, total_amount, 
		z_report_payload, is_synced, created_at, updated_at FROM z_reports`

	selectLatestZReportSQL = selectZReportSQL + ` ORDER BY id DESC LIMIT 1`

	selectZReportByZReportNumberSQL = selectZReportSQL + ` WHERE zreport_number=$1`

	selectZReportByIDSQL = selectZReportSQL + ` WHERE id=$1`

	updateZReportSQL = `UPDATE z_reports SET file_name=$1, is_synced=$2, updated_at=$3 WHERE id=$4`
)

type (
	ZReportRepository interface {
		CountZReports(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) int
		FilterZReports(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.ZReport, error)
		GetLatestZReport(ctx context.Context) (*entities.ZReport, error)
		GetZReportByZReportNumber(ctx context.Context, email string) (*entities.ZReport, error)
		GetZReportByID(ctx context.Context, zreportID int64) (*entities.ZReport, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, zreport *entities.ZReport) error
	}

	AppZReportRepository struct {
		db *sql.DB
	}
)

func NewZReportRepository(db *sql.DB) ZReportRepository {
	return &AppZReportRepository{db: db}
}

func (r *AppZReportRepository) CountZReports(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countZReportsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppZReportRepository) FilterZReports(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.ZReport, error) {

	zreports := make([]*entities.ZReport, 0)

	args := make([]interface{}, 0)
	query := selectZReportSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d OFFSET $%d  ", currentIndex, currentIndex+1)
	args = append(args, filter.Per, (filter.Page-1)*filter.Per)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return zreports, err
	}

	defer rows.Close()

	for rows.Next() {
		zreport, err := r.scanRowIntoZReport(rows)
		if err != nil {
			return zreports, err
		}

		zreports = append(zreports, zreport)
	}

	return zreports, rows.Err()
}

func (r *AppZReportRepository) GetZReportByID(
	ctx context.Context,
	zreportID int64,
) (*entities.ZReport, error) {
	row := r.db.QueryRow(selectZReportByIDSQL, zreportID)
	return r.scanRowIntoZReport(row)
}

func (r *AppZReportRepository) GetLatestZReport(
	ctx context.Context,
) (*entities.ZReport, error) {
	row := r.db.QueryRow(selectLatestZReportSQL)
	return r.scanRowIntoZReport(row)
}

func (r *AppZReportRepository) GetZReportByZReportNumber(
	ctx context.Context,
	zreportNum string,
) (*entities.ZReport, error) {
	row := r.db.QueryRow(selectZReportByZReportNumberSQL, zreportNum)
	return r.scanRowIntoZReport(row)
}

func (r *AppZReportRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	zreport *entities.ZReport,
) error {

	zreport.Timestamps.Touch()
	var err error

	if zreport.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			insertZReportSQL,
			zreport.ZReportDate,
			zreport.ZReportTime,
			zreport.InvoiceCount,
			zreport.NetAmount,
			zreport.VatAmount,
			zreport.PmtAmount,
			zreport.Status,
			zreport.TotalAmount,
			zreport.ZReportPayload,
			zreport.IsSynced,
			zreport.CreatedAt,
			zreport.UpdatedAt,
		)

		err = res.Scan(&zreport.ID)

	} else {

		_, err = r.db.Exec(
			updateZReportSQL,
			zreport.FileName,
			zreport.IsSynced,
			zreport.UpdatedAt,
			zreport.ID,
		)
	}

	if err != nil {
		log.Printf("error saving zreport, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppZReportRepository) SelectZReportSummary(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ZReportTotals, error) {

	zReportTotals := &entities.ZReportTotals{}

	return zReportTotals, nil
}

func (r *AppZReportRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppZReportRepository) scanRowIntoZReport(
	rowScanner txns_db.RowScanner,
) (*entities.ZReport, error) {

	var zreport entities.ZReport

	err := rowScanner.Scan(
		&zreport.ID,
		&zreport.ZReportDate,
		&zreport.ZReportTime,
		&zreport.InvoiceCount,
		&zreport.NetAmount,
		&zreport.VatAmount,
		&zreport.PmtAmount,
		&zreport.TotalAmount,
		&zreport.ZReportPayload,
		&zreport.IsSynced,
		&zreport.CreatedAt,
		&zreport.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning zreport,  err=[%v]\n", err.Error())
		return &zreport, err
	}

	return &zreport, nil
}
