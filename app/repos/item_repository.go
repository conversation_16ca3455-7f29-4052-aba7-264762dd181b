package repos

import (
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
)

const (
	countItemsSQL = `SELECT COUNT(*) AS count FROM items WHERE store_id=$1`

	createItemSQL = `INSERT INTO items (name, description, organization_id, cost_price, retail_price, category_id, 
		store_id, tax_percent, avatar_url, etims_code, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING id`

	selectItemByIDSQL = selectItemSQL + ` AND id=$2`

	selectItemByNameAndStoreIDSQL = selectItemSQL + ` AND name=$2`

	selectItemsByIDsSQL = selectItemSQL + " AND id IN "

	selectItemSQL = `SELECT id, name, description, organization_id, cost_price, retail_price, category_id, store_id, 
		tax_percent, avatar_url, etims_code, created_by, created_at, updated_at FROM items WHERE store_id=$1`

	updateItemSQL = `UPDATE items SET name=$1, description=$2, cost_price=$3, retail_price=$4, category_id=$5, 
		tax_percent=$6, avatar_url=$7, etims_code=$8, updated_at=$9 WHERE id=$10`
)

type (
	ItemRepository interface {
		CountItems(context.Context, *entities.PaginationFilter, int64) (int, error)
		FilterItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter, int64) ([]*entities.Item, error)
		FindByNameAndStore(context.Context, string, int64) (*entities.Item, error)
		FindByID(ctx context.Context, itemId int64, storeId int64) (*entities.Item, error)
		GetItemByID(int64) (*entities.Item, error)
		GetItemsByIds(context.Context, txns_db.TransactionsSQLOperations, []int64, int64) ([]*entities.Item, error)
		Save(context.Context, *entities.Item) error
	}

	AppItemRepository struct {
		db *sql.DB
	}
)

func NewItemRepository(db *sql.DB) ItemRepository {
	return &AppItemRepository{db: db}
}

func (r *AppItemRepository) CountItems(
	ctx context.Context,
	filter *entities.PaginationFilter,
	storeId int64,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countItemsSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}
	return count, nil
}

func (r *AppItemRepository) FilterItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) ([]*entities.Item, error) {

	items := make([]*entities.Item, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := selectItemSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return items, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoItem(rows)
		if err != nil {
			return items, err
		}

		items = append(items, invoice)
	}

	return items, rows.Err()
}

func (r *AppItemRepository) FindByNameAndStore(
	ctx context.Context,
	name string,
	storeID int64,
) (*entities.Item, error) {
	row := r.db.QueryRow(selectItemByNameAndStoreIDSQL, storeID, name)
	return r.scanRowIntoItem(row)
}

func (r *AppItemRepository) FindByID(
	ctx context.Context,
	itemID int64,
	storeId int64,
) (*entities.Item, error) {
	row := r.db.QueryRow(selectItemByIDSQL, storeId, itemID)
	return r.scanRowIntoItem(row)
}

func (r *AppItemRepository) GetItemByID(itemID int64) (*entities.Item, error) {
	row := r.db.QueryRow(selectItemByIDSQL, itemID)
	return r.scanRowIntoItem(row)
}

func (r *AppItemRepository) GetItemsByIds(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemIDs []int64,
	storeId int64,
) ([]*entities.Item, error) {

	items := make([]*entities.Item, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := selectItemsByIDsSQL
	currentIndex := 2

	values := make([]string, len(itemIDs))
	for index, itemID := range itemIDs {
		values[index] = fmt.Sprintf("$%d", currentIndex)
		args = append(args, itemID)
		currentIndex++
	}

	query += " (" + strings.Join(values, ",") + ")"

	utils.Log.Infof("query=[%v], args=[%+v]\n", query, args)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return items, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoItem(rows)
		if err != nil {
			return items, err
		}

		items = append(items, invoice)
	}

	return items, rows.Err()
}

func (r *AppItemRepository) Save(
	ctx context.Context,
	item *entities.Item,
) error {

	item.Timestamps.Touch()
	var err error

	if item.IsNew() {
		err = r.db.QueryRow(
			createItemSQL,
			item.Name,
			item.Description,
			item.OrganizationID,
			item.CostPrice,
			item.RetailPrice,
			item.CategoryID,
			item.StoreID,
			item.TaxPercent,
			item.AvatarURL,
			item.ETIMSCode,
			item.CreatedBy,
			item.CreatedAt,
			item.UpdatedAt,
		).Scan(&item.ID)

	} else {

		_, err = r.db.Exec(
			updateItemSQL,
			item.Name,
			item.Description,
			item.CostPrice,
			item.RetailPrice,
			item.CategoryID,
			item.TaxPercent,
			item.AvatarURL,
			item.ETIMSCode,
			item.UpdatedAt,
			item.ID,
		)
	}

	if err != nil {
		log.Printf("error saving item, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%')", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppItemRepository) scanRowIntoItem(
	rowScanner txns_db.RowScanner,
) (*entities.Item, error) {

	var item entities.Item

	err := rowScanner.Scan(
		&item.ID,
		&item.Name,
		&item.Description,
		&item.OrganizationID,
		&item.CostPrice,
		&item.RetailPrice,
		&item.CategoryID,
		&item.StoreID,
		&item.TaxPercent,
		&item.AvatarURL,
		&item.ETIMSCode,
		&item.CreatedBy,
		&item.CreatedAt,
		&item.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning item,  err=[%v]\n", err.Error())
		return &item, err
	}

	return &item, nil
}
