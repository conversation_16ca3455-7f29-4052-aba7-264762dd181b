package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countXeroAuthenticationsSQL = `SELECT COUNT(*) AS count FROM xero_authentication WHERE 1=1`

	createXeroAuthenticationSQL = ` INSERT INTO xero_authentication (access_token, expires_in, refresh_token, tenant_id, scope, 
		token_type, organization_id, organization_name, tenant_type, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) ON CONFLICT 
		(organization_id) DO UPDATE SET access_token = EXCLUDED.access_token, refresh_token = EXCLUDED.refresh_token,  
		tenant_type = EXCLUDED.tenant_type, updated_at=Now() RETURNING id`

	deleteXeroAuthenticationByIDSQL = `DELETE FROM xero_authentication WHERE id=$1`

	selectXeroAuthenticationByOrganizationIDSQL = selectXeroAuthenticationSQL + ` WHERE organization_id=$1`

	selectXeroAuthenticationByIDSQL = selectXeroAuthenticationSQL + ` WHERE id=$1`

	selectXeroAuthenticationSQL = `SELECT id, access_token, expires_in, organization_id, organization_name, refresh_token, scope, 
		tenant_id, tenant_type, token_type, created_at, updated_at FROM xero_authentication`

	updateXeroAuthenticationSQL = `UPDATE xero_authentication SET access_token=$1, expires_in=$2, refresh_token=$3, tenant_id=$4, 
		organization_name=$5, tenant_type=$6, updated_at=$7 WHERE id=$8`
)

type (
	XeroAuthenticationRepository interface {
		CountXeroAuthentications(ctx context.Context, filter *entities.QueryFilter) int
		Delete(ctx context.Context, token *entities.XeroRefreshToken) error
		FindByID(ctx context.Context, id int64) (*entities.XeroRefreshToken, error)
		FindByOrganizationID(ctx context.Context, organizationID int64) (*entities.XeroRefreshToken, error)
		GetXeroAuthenticationByID(ctx context.Context, userID int64) (*entities.XeroRefreshToken, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, user *entities.XeroRefreshToken) error
	}

	AppXeroAuthenticationRepository struct {
		db *sql.DB
	}
)

func NewXeroAuthenticationRepository(db *sql.DB) XeroAuthenticationRepository {
	return &AppXeroAuthenticationRepository{db: db}
}

func (r *AppXeroAuthenticationRepository) CountXeroAuthentications(
	ctx context.Context,
	filter *entities.QueryFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countXeroAuthenticationsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppXeroAuthenticationRepository) Delete(
	ctx context.Context,
	token *entities.XeroRefreshToken,
) error {
	_, err := r.db.ExecContext(ctx, deleteXeroAuthenticationByIDSQL, token.ID)
	return err
}

func (r *AppXeroAuthenticationRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.XeroRefreshToken, error) {
	row := r.db.QueryRow(selectXeroAuthenticationByIDSQL, id)
	return r.scanRowIntoXeroAuthentication(row)
}

func (r *AppXeroAuthenticationRepository) FindByOrganizationID(
	ctx context.Context,
	organizationID int64,
) (*entities.XeroRefreshToken, error) {
	row := r.db.QueryRow(selectXeroAuthenticationByOrganizationIDSQL, organizationID)
	return r.scanRowIntoXeroAuthentication(row)
}

func (r *AppXeroAuthenticationRepository) GetXeroAuthenticationByID(
	ctx context.Context,
	authenticationID int64,
) (*entities.XeroRefreshToken, error) {
	row := r.db.QueryRow(selectXeroAuthenticationByIDSQL, authenticationID)
	return r.scanRowIntoXeroAuthentication(row)
}

func (r *AppXeroAuthenticationRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	token *entities.XeroRefreshToken,
) error {

	token.Timestamps.Touch()
	var err error

	if token.IsNew() {
		res := r.db.QueryRowContext(
			ctx,
			createXeroAuthenticationSQL,
			token.AccessToken,
			token.ExpiresIn,
			token.RefreshToken,
			token.TenantID,
			token.Scope,
			token.TokenType,
			token.OrganizationID,
			token.OrganizationName,
			token.TenantType,
			token.CreatedAt,
			token.UpdatedAt,
		)

		err = res.Scan(&token.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateXeroAuthenticationSQL,
			token.AccessToken,
			token.ExpiresIn,
			token.RefreshToken,
			token.TenantID,
			token.OrganizationName,
			token.TenantType,
			token.UpdatedAt,
			token.ID,
		)
	}

	if err != nil {
		log.Printf("error saving token, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppXeroAuthenticationRepository) buildQueryFromFilter(
	query string,
	filter *entities.QueryFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppXeroAuthenticationRepository) scanRowIntoXeroAuthentication(
	row *sql.Row,
) (*entities.XeroRefreshToken, error) {

	var token entities.XeroRefreshToken

	err := row.Scan(
		&token.ID,
		&token.AccessToken,
		&token.ExpiresIn,
		&token.OrganizationID,
		&token.OrganizationName,
		&token.RefreshToken,
		&token.Scope,
		&token.TenantID,
		&token.TenantType,
		&token.TokenType,
		&token.CreatedAt,
		&token.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning token, err=[%v]\n", err.Error())
		return &token, err
	}

	return &token, err
}
