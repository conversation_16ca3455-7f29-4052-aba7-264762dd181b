package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countCompanyRegistrationsSQL = `SELECT COUNT(*) AS count FROM company_registrations WHERE 1=1`

	insertCompanyRegistrationSQL = ` INSERT INTO company_registrations (reg_id, serial, uin, tin, vrn, mobile, street, city,  
		country, name, receipt_code, region, routing_key, gc, tax_office, username, password, token_path, efdms_signature, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21) RETURNING id`

	selectCompanyRegistrationByCompanyTINSQL = selectCompanyRegistrationSQL + ` WHERE tin=$1`

	selectCompanyRegistrationByIDSQL = selectCompanyRegistrationSQL + ` WHERE id=$1`

	selectCompanyRegistrationSQL = `SELECT id, reg_id, serial, uin, tin, vrn, mobile, street, city, country, name, receipt_code, region, 
		routing_key, gc, tax_office, username, password, token_path, efdms_signature, created_at, updated_at FROM company_registrations`

	updateCompanyRegistrationSQL = `UPDATE company_registrations SET reg_id=$1, serial=$2, uin=$3, tin=$4, vrn=$5, mobile=$6, street=$7, city=$8, 
		country=$9, name=$10, receipt_code=$11, region=$12, routing_key=$13, gc=$14, tax_office=$15, username=$16, password=$17, token_path=$18, 
		efdms_signature=$19, updated_at=$20 WHERE id=$21`
)

type (
	CompanyRegistrationRepository interface {
		CountCompanyRegistrations(ctx context.Context, filter *entities.QueryFilter) int
		FilterCompanyRegistrations(ctx context.Context, filter *entities.QueryFilter) (*entities.CompanyRegistrationList, error)
		GetCompanyRegistrationByCompanyTIN(ctx context.Context, email string) (*entities.CompanyRegistration, error)
		GetCompanyRegistrationByID(ctx context.Context, companyRegistrationID int64) (*entities.CompanyRegistration, error)
		Save(ctx context.Context, companyRegistration *entities.CompanyRegistration) error
	}

	AppCompanyRegistrationRepository struct {
		db *sql.DB
	}
)

func NewCompanyRegistrationRepository(db *sql.DB) CompanyRegistrationRepository {
	return &AppCompanyRegistrationRepository{db: db}
}

func (r *AppCompanyRegistrationRepository) CountCompanyRegistrations(
	ctx context.Context,
	filter *entities.QueryFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countCompanyRegistrationsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppCompanyRegistrationRepository) FilterCompanyRegistrations(
	ctx context.Context,
	filter *entities.QueryFilter,
) (*entities.CompanyRegistrationList, error) {
	companyRegistrationList := &entities.CompanyRegistrationList{}
	return companyRegistrationList, nil
}

func (r *AppCompanyRegistrationRepository) GetCompanyRegistrationByID(
	ctx context.Context,
	companyRegistrationID int64,
) (*entities.CompanyRegistration, error) {
	row := r.db.QueryRow(selectCompanyRegistrationByIDSQL, companyRegistrationID)
	return r.scanRowIntoCompanyRegistration(row)
}

func (r *AppCompanyRegistrationRepository) GetCompanyRegistrationByCompanyTIN(
	ctx context.Context,
	email string,
) (*entities.CompanyRegistration, error) {
	row := r.db.QueryRow(selectCompanyRegistrationByCompanyTINSQL, email)
	return r.scanRowIntoCompanyRegistration(row)
}

func (r *AppCompanyRegistrationRepository) Save(
	ctx context.Context,
	companyRegistration *entities.CompanyRegistration,
) error {

	companyRegistration.Timestamps.Touch()
	var err error

	if companyRegistration.IsNew() {
		err = r.db.QueryRow(
			insertCompanyRegistrationSQL,
			companyRegistration.RegID,
			companyRegistration.Serial,
			companyRegistration.UIN,
			companyRegistration.TIN,
			companyRegistration.VRN,
			companyRegistration.Mobile,
			companyRegistration.Street,
			companyRegistration.City,
			companyRegistration.Country,
			companyRegistration.Name,
			companyRegistration.ReceiptCode,
			companyRegistration.Region,
			companyRegistration.RoutingKey,
			companyRegistration.GC,
			companyRegistration.TaxOffice,
			companyRegistration.Username,
			companyRegistration.Password,
			companyRegistration.TokenPath,
			companyRegistration.EFDMSSignature,
			companyRegistration.CreatedAt,
			companyRegistration.UpdatedAt,
		).Scan(&companyRegistration.ID)

	} else {

		_, err = r.db.Exec(
			updateCompanyRegistrationSQL,
			companyRegistration.RegID,
			companyRegistration.Serial,
			companyRegistration.UIN,
			companyRegistration.TIN,
			companyRegistration.VRN,
			companyRegistration.Mobile,
			companyRegistration.Street,
			companyRegistration.City,
			companyRegistration.Country,
			companyRegistration.Name,
			companyRegistration.ReceiptCode,
			companyRegistration.Region,
			companyRegistration.RoutingKey,
			companyRegistration.GC,
			companyRegistration.TaxOffice,
			companyRegistration.Username,
			companyRegistration.Password,
			companyRegistration.TokenPath,
			companyRegistration.EFDMSSignature,
			companyRegistration.UpdatedAt,
			companyRegistration.ID,
		)
	}

	if err != nil {
		log.Printf("error saving companyRegistration, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppCompanyRegistrationRepository) buildQueryFromFilter(
	query string,
	filter *entities.QueryFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppCompanyRegistrationRepository) scanRowIntoCompanyRegistration(
	row *sql.Row,
) (*entities.CompanyRegistration, error) {

	var companyRegistration entities.CompanyRegistration

	err := row.Scan(
		&companyRegistration.ID,
		&companyRegistration.RegID,
		&companyRegistration.Serial,
		&companyRegistration.UIN,
		&companyRegistration.TIN,
		&companyRegistration.VRN,
		&companyRegistration.Mobile,
		&companyRegistration.Street,
		&companyRegistration.City,
		&companyRegistration.Country,
		&companyRegistration.Name,
		&companyRegistration.ReceiptCode,
		&companyRegistration.Region,
		&companyRegistration.RoutingKey,
		&companyRegistration.GC,
		&companyRegistration.TaxOffice,
		&companyRegistration.Username,
		&companyRegistration.Password,
		&companyRegistration.TokenPath,
		&companyRegistration.EFDMSSignature,
		&companyRegistration.CreatedAt,
		&companyRegistration.UpdatedAt,
	)

	if err != nil {
		return &companyRegistration, err
	}

	return &companyRegistration, nil
}
