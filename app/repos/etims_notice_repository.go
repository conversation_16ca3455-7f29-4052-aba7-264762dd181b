package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"
)

const (
	insertEtimsNoticeSQL = `INSERT INTO etims_notices (content, detail_url, notice_number, registration_date_time, registration_name, 
		title, created_at, updated_at) VALUES`

	createEtimsNoticeSQL = insertEtimsNoticeSQL + ` ($1, $2, $3, $4, $5, $6, $7, &8) RETURNING id`

	selectEtimsNoticeByIDSQL = selectEtimsNoticeSQL + ` WHERE id=$1`

	selectEtimsNoticeSQL = `SELECT id, content, detail_url, notice_number, registration_date_time, registration_name, title, 
		created_at, updated_at FROM etims_notices`
)

type (
	EtimsNoticeRepository interface {
		Count(context.Context, *entities.PaginationFilter) (int, error)
		FilterEtimsNotices(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.EtimsNotice, error)
		FindByID(context.Context, int64) (*entities.EtimsNotice, error)
		Save(context.Context, *entities.EtimsNotice) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.EtimsNotice) error
	}

	AppEtimsNoticeRepository struct {
		db *sql.DB
	}
)

func NewEtimsNoticeRepository(db *sql.DB) EtimsNoticeRepository {
	return &AppEtimsNoticeRepository{db: db}
}

func (r *AppEtimsNoticeRepository) Count(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countInvoicesSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppEtimsNoticeRepository) FilterEtimsNotices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.EtimsNotice, error) {

	etimsNotices := make([]*entities.EtimsNotice, 0)

	args := make([]interface{}, 0)
	query := selectEtimsNoticeSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return etimsNotices, err
	}

	defer rows.Close()

	for rows.Next() {
		etimsNotice, err := r.scanRowIntoEtimsNotice(rows)
		if err != nil {
			return etimsNotices, err
		}

		etimsNotices = append(etimsNotices, etimsNotice)
	}

	return etimsNotices, rows.Err()
}

func (r *AppEtimsNoticeRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.EtimsNotice, error) {
	row := r.db.QueryRow(selectEtimsNoticeByIDSQL, id)
	return r.scanRowIntoEtimsNotice(row)
}

func (r *AppEtimsNoticeRepository) Save(
	ctx context.Context,
	etimsNotice *entities.EtimsNotice,
) error {

	etimsNotice.Timestamps.Touch()

	err := r.db.QueryRow(
		createEtimsNoticeSQL,
		etimsNotice.Content,
		etimsNotice.DetailURL,
		etimsNotice.NoticeNumber,
		etimsNotice.RegistrationDateTime,
		etimsNotice.RegistrationName,
		etimsNotice.Title,
		etimsNotice.CreatedAt,
		etimsNotice.UpdatedAt,
		time.Now(),
	).Scan(&etimsNotice.ID)

	if err != nil {
		log.Printf("error saving etimsNotice, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppEtimsNoticeRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	notices []*entities.EtimsNotice,
) error {

	query := insertEtimsNoticeSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(notices))

	for index, invoiceItem := range notices {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5,
			currentIndex+6, currentIndex+7)
		currentIndex += 8
		args = append(
			args,
			invoiceItem.Content,
			invoiceItem.DetailURL,
			invoiceItem.NoticeNumber,
			invoiceItem.RegistrationDateTime,
			invoiceItem.RegistrationName,
			invoiceItem.Title,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(notice_number) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&notices[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppEtimsNoticeRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(content) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(title) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppEtimsNoticeRepository) scanRowIntoEtimsNotice(
	rowScanner txns_db.RowScanner,
) (*entities.EtimsNotice, error) {

	var etimsNotice entities.EtimsNotice

	err := rowScanner.Scan(
		&etimsNotice.ID,
		&etimsNotice.Content,
		&etimsNotice.DetailURL,
		&etimsNotice.NoticeNumber,
		&etimsNotice.RegistrationDateTime,
		&etimsNotice.RegistrationName,
		&etimsNotice.Title,
		&etimsNotice.CreatedAt,
		&etimsNotice.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning etimsNotice,  err=[%v]\n", err.Error())
		return &etimsNotice, err
	}

	return &etimsNotice, nil
}
