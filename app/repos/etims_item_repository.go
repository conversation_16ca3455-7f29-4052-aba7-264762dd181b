package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"
)

const (
	insertEtimsItemSQL = `INSERT INTO etims_items (dcAmt, dcRt, tin, bhfId, itemCd, itemClsCd, itemTyCd, itemNm, itemStdNm, orgnNatCd, 
		pkgUnitCd, qtyUnitCd, taxTyCd, btchNo, bcd, dftPrc, grpPrcL1, grpPrcL2, grpPrcL3, grpPrcL4, grpPrcL5, addInfo, sftyQty, isrcAplcbYn, 
		useYn, regrNm, regrId, modrNm, modrId, uuid, created_at, updated_at) VALUES`

	createEtimsItemSQL = insertEtimsItemSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, 
		$21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32) RETURNING id`

	selectEtimsItemByIDSQL = selectEtimsItemSQL + ` WHERE id=$1`

	selectEtimsItemByItemCodeSQL = selectEtimsItemSQL + ` WHERE itemCd=$1`

	selectEtimsItemSQL = `SELECT id, dcAmt, dcRt, tin, bhfId, itemCd, itemClsCd, itemTyCd, itemNm, itemStdNm, orgnNatCd, pkgUnitCd, qtyUnitCd, 
		taxTyCd, btchNo,bcd, dftPrc, grpPrcL1, grpPrcL2, grpPrcL3, grpPrcL4, grpPrcL5, addInfo, sftyQty, isrcAplcbYn, useYn, regrNm, regrId, 
		modrNm, modrId, uuid, created_at, updated_at FROM etims_items`
)

type (
	EtimsItemRepository interface {
		Count(context.Context, *entities.PaginationFilter) (int, error)
		FilterEtimsItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.EtimsItem, error)
		FindByID(context.Context, int64) (*entities.EtimsItem, error)
		FindByItemCode(context.Context, string) (*entities.EtimsItem, error)
		Save(context.Context, *entities.EtimsItem) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.EtimsItem) error
	}

	AppEtimsItemRepository struct {
		db *sql.DB
	}
)

func NewEtimsItemRepository(db *sql.DB) EtimsItemRepository {
	return &AppEtimsItemRepository{db: db}
}

func (r *AppEtimsItemRepository) Count(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countInvoicesSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppEtimsItemRepository) FilterEtimsItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.EtimsItem, error) {

	etimsItems := make([]*entities.EtimsItem, 0)

	args := make([]interface{}, 0)
	query := selectEtimsItemSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return etimsItems, err
	}

	defer rows.Close()

	for rows.Next() {
		etimsItem, err := r.scanRowIntoEtimsItem(rows)
		if err != nil {
			return etimsItems, err
		}

		etimsItems = append(etimsItems, etimsItem)
	}

	return etimsItems, rows.Err()
}

func (r *AppEtimsItemRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.EtimsItem, error) {
	row := r.db.QueryRow(selectEtimsItemByIDSQL, id)
	return r.scanRowIntoEtimsItem(row)
}

func (r *AppEtimsItemRepository) FindByItemCode(
	ctx context.Context,
	itemCode string,
) (*entities.EtimsItem, error) {
	row := r.db.QueryRow(selectEtimsItemByItemCodeSQL, itemCode)
	return r.scanRowIntoEtimsItem(row)
}

func (r *AppEtimsItemRepository) Save(
	ctx context.Context,
	etimsItem *entities.EtimsItem,
) error {

	etimsItem.Timestamps.Touch()

	err := r.db.QueryRow(
		createEtimsItemSQL,
		etimsItem.DcAmt,
		etimsItem.DcRt,
		etimsItem.Tin,
		etimsItem.BhfID,
		etimsItem.ItemCd,
		etimsItem.ItemClsCd,
		etimsItem.ItemTyCd,
		etimsItem.ItemNm,
		etimsItem.ItemStdNm,
		etimsItem.OrgnNatCd,
		etimsItem.PkgUnitCd,
		etimsItem.QtyUnitCd,
		etimsItem.TaxTyCd,
		etimsItem.BtchNo,
		etimsItem.Bcd,
		etimsItem.DftPrc,
		etimsItem.GrpPrcL1,
		etimsItem.GrpPrcL2,
		etimsItem.GrpPrcL3,
		etimsItem.GrpPrcL4,
		etimsItem.GrpPrcL5,
		etimsItem.AddInfo,
		etimsItem.SftyQty,
		etimsItem.IsrcAplcbYn,
		etimsItem.UseYn,
		etimsItem.RegrNm,
		etimsItem.RegrID,
		etimsItem.ModrNm,
		etimsItem.ModrID,
		etimsItem.UUID,
		etimsItem.CreatedAt,
		etimsItem.UpdatedAt,
	).Scan(&etimsItem.ID)

	if err != nil {
		log.Printf("error saving etimsItem, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppEtimsItemRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	etimsItems []*entities.EtimsItem,
) error {

	query := insertEtimsItemSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(etimsItems))

	for index, etimsItem := range etimsItems {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5, currentIndex+6, currentIndex+7, currentIndex+8, currentIndex+9, currentIndex+10,
			currentIndex+11, currentIndex+12, currentIndex+13, currentIndex+14, currentIndex+15, currentIndex+16, currentIndex+17, currentIndex+18, currentIndex+19, currentIndex+20,
			currentIndex+21, currentIndex+22, currentIndex+23, currentIndex+24, currentIndex+25, currentIndex+26, currentIndex+27, currentIndex+28, currentIndex+29, currentIndex+30, currentIndex+31)
		currentIndex += 32
		args = append(
			args,
			etimsItem.DcAmt,
			etimsItem.DcRt,
			etimsItem.Tin,
			etimsItem.BhfID,
			etimsItem.ItemCd,
			etimsItem.ItemClsCd,
			etimsItem.ItemTyCd,
			etimsItem.ItemNm,
			etimsItem.ItemStdNm,
			etimsItem.OrgnNatCd,
			etimsItem.PkgUnitCd,
			etimsItem.QtyUnitCd,
			etimsItem.TaxTyCd,
			etimsItem.BtchNo,
			etimsItem.Bcd,
			etimsItem.DftPrc,
			etimsItem.GrpPrcL1,
			etimsItem.GrpPrcL2,
			etimsItem.GrpPrcL3,
			etimsItem.GrpPrcL4,
			etimsItem.GrpPrcL5,
			etimsItem.AddInfo,
			etimsItem.SftyQty,
			etimsItem.IsrcAplcbYn,
			etimsItem.UseYn,
			etimsItem.RegrNm,
			etimsItem.RegrID,
			etimsItem.ModrNm,
			etimsItem.ModrID,
			etimsItem.UUID,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(itemCd, uuid) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&etimsItems[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppEtimsItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(itemNm) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(itemCd) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppEtimsItemRepository) scanRowIntoEtimsItem(
	rowScanner txns_db.RowScanner,
) (*entities.EtimsItem, error) {

	var etimsItem entities.EtimsItem

	err := rowScanner.Scan(
		&etimsItem.ID,
		&etimsItem.DcAmt,
		&etimsItem.DcRt,
		&etimsItem.Tin,
		&etimsItem.BhfID,
		&etimsItem.ItemCd,
		&etimsItem.ItemClsCd,
		&etimsItem.ItemTyCd,
		&etimsItem.ItemNm,
		&etimsItem.ItemStdNm,
		&etimsItem.OrgnNatCd,
		&etimsItem.PkgUnitCd,
		&etimsItem.QtyUnitCd,
		&etimsItem.TaxTyCd,
		&etimsItem.BtchNo,
		&etimsItem.Bcd,
		&etimsItem.DftPrc,
		&etimsItem.GrpPrcL1,
		&etimsItem.GrpPrcL2,
		&etimsItem.GrpPrcL3,
		&etimsItem.GrpPrcL4,
		&etimsItem.GrpPrcL5,
		&etimsItem.AddInfo,
		&etimsItem.SftyQty,
		&etimsItem.IsrcAplcbYn,
		&etimsItem.UseYn,
		&etimsItem.RegrNm,
		&etimsItem.RegrID,
		&etimsItem.ModrNm,
		&etimsItem.ModrID,
		&etimsItem.UUID,
		&etimsItem.CreatedAt,
		&etimsItem.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning etimsItem,  err=[%v]\n", err.Error())
		return &etimsItem, err
	}

	return &etimsItem, nil
}
