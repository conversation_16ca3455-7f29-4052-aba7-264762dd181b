package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countReceivingsSQL = `SELECT COUNT(*) AS count FROM receivings WHERE 1=1`

	createReceivingSQL = `INSERT INTO receivings (receiving_id, quantity, cost_price, total, organization_id, received_at, created_by, supplier_id, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING id`

	selectReceivingByIDSQL = selectReceivingSQL + ` WHERE id=$1`

	selectReceivingByNameAndOrganizationSQL = selectReceivingSQL + ` WHERE name=$1 AND organization_id=$2`

	selectReceivingSQL = `SELECT id, receiving_id, quantity, cost_price, total, organization_id, received_at, created_by, supplier_id, description, created_at, updated_at FROM receivings`

	updateReceivingSQL = `UPDATE receivings SET quantity=$1, cost_price=$2, total=$3, received_at=$4, created_by=$5, supplier_id=$6, description=$7, updated_at=$8 WHERE id=$9`
)

type (
	ReceivingRepository interface {
		CountReceivings(context.Context, *entities.PaginationFilter) int
		FilterReceivings(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.Receiving, error)
		FindByID(context.Context, int64) (*entities.Receiving, error)
		Save(context.Context, *entities.Receiving) error
	}

	AppReceivingRepository struct {
		db *sql.DB
	}
)

func NewReceivingRepository(db *sql.DB) ReceivingRepository {
	return &AppReceivingRepository{db: db}
}

func (r *AppReceivingRepository) CountReceivings(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countReceivingsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppReceivingRepository) FilterReceivings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Receiving, error) {

	categories := make([]*entities.Receiving, 0)

	args := make([]interface{}, 0)
	query := selectReceivingSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoReceiving(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppReceivingRepository) FindByID(
	ctx context.Context,
	receivingID int64,
) (*entities.Receiving, error) {
	row := r.db.QueryRow(selectReceivingByIDSQL, receivingID)
	return r.scanRowIntoReceiving(row)
}

func (r *AppReceivingRepository) Save(
	ctx context.Context,
	receiving *entities.Receiving,
) error {

	receiving.Timestamps.Touch()
	var err error

	if receiving.IsNew() {
		err = r.db.QueryRow(
			createReceivingSQL,
			receiving.ItemID,
			receiving.Quantity,
			receiving.CostPrice,
			receiving.Total,
			receiving.OrganizationID,
			receiving.ReceivedAt,
			receiving.CreatedBy,
			receiving.SupplierID,
			receiving.Description,
			receiving.CreatedAt,
			receiving.UpdatedAt,
		).Scan(&receiving.ID)

	} else {

		_, err = r.db.Exec(
			updateReceivingSQL,
			receiving.Quantity,
			receiving.CostPrice,
			receiving.Total,
			receiving.ReceivedAt,
			receiving.CreatedBy,
			receiving.SupplierID,
			receiving.Description,
			receiving.UpdatedAt,
			receiving.ID,
		)
	}

	if err != nil {
		log.Printf("error saving receiving, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppReceivingRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppReceivingRepository) scanRowIntoReceiving(
	rowScanner txns_db.RowScanner,
) (*entities.Receiving, error) {

	var receiving entities.Receiving

	err := rowScanner.Scan(
		&receiving.ID,
		&receiving.ItemID,
		&receiving.Quantity,
		&receiving.CostPrice,
		&receiving.Total,
		&receiving.OrganizationID,
		&receiving.ReceivedAt,
		&receiving.CreatedBy,
		&receiving.SupplierID,
		&receiving.Description,
		&receiving.CreatedAt,
		&receiving.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning receiving,  err=[%v]\n", err.Error())
		return &receiving, err
	}

	return &receiving, nil
}
