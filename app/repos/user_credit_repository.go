package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countUserCreditsSQL = `SELECT COUNT(*) AS count FROM user_credits WHERE 1=1`

	createUserCreditSQL = `INSERT INTO user_credits (user_id, credit_balance, created_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING id`

	selectUserCreditByIDSQL = selectUserCreditSQL + ` WHERE id=$1`

	selectUserCreditByUserIDSQL = selectUserCreditSQL + ` WHERE user_id=$1`

	selectUserCreditSQL = `SELECT id, user_id, credit_balance, created_at, updated_at FROM user_credits`

	updateUserCreditSQL = `UPDATE user_credits SET credit_balance=$1, updated_at=$2 WHERE id=$3`
)

type (
	UserCreditRepository interface {
		CountUserCredits(context.Context, *entities.PaginationFilter) int
		FindByID(context.Context, int64) (*entities.UserCredit, error)
		FindByUserID(context.Context, int64) (*entities.UserCredit, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.UserCredit) error
	}

	AppUserCreditRepository struct {
		db *sql.DB
	}
)

func NewUserCreditRepository(db *sql.DB) UserCreditRepository {
	return &AppUserCreditRepository{db: db}
}

func (r *AppUserCreditRepository) CountUserCredits(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countUserCreditsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppUserCreditRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.UserCredit, error) {
	row := r.db.QueryRow(selectUserCreditByIDSQL, id)
	return r.scanRowIntoUserCredit(row)
}

func (r *AppUserCreditRepository) FindByUserID(
	ctx context.Context,
	id int64,
) (*entities.UserCredit, error) {
	row := r.db.QueryRow(selectUserCreditByUserIDSQL, id)
	return r.scanRowIntoUserCredit(row)
}

func (r *AppUserCreditRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userCredit *entities.UserCredit,
) error {

	userCredit.Timestamps.Touch()
	var err error

	if userCredit.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createUserCreditSQL,
			userCredit.UserID,
			userCredit.CreditBalance,
			userCredit.CreatedAt,
			userCredit.UpdatedAt,
		)

		err = res.Scan(&userCredit.ID)

	} else {

		_, err = r.db.Exec(
			updateUserCreditSQL,
			userCredit.CreditBalance,
			userCredit.UpdatedAt,
			userCredit.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user Credit Balance, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppUserCreditRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	return query, args, currentIndex
}

func (r *AppUserCreditRepository) scanRowIntoUserCredit(
	rowScanner txns_db.RowScanner,
) (*entities.UserCredit, error) {

	var userCredit entities.UserCredit

	err := rowScanner.Scan(
		&userCredit.ID,
		&userCredit.UserID,
		&userCredit.CreditBalance,
		&userCredit.CreatedAt,
		&userCredit.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning userCredit,  err=[%v]\n", err.Error())
		return &userCredit, err
	}

	return &userCredit, nil
}
