package apperr

import "net/http"

type ErrorCode string

func (s ErrorCode) String() string {
	return string(s)
}

const (
	ErrorCodeInvalidArgument  ErrorCode = "invalid_argument"
	ErrorCodeRequestFailed    ErrorCode = "request_failed"
	ErrorCodeResourceNotFound ErrorCode = "resource_not_found"
	ErrorCodeUnauthorized     ErrorCode = "unauthorized"
)

var (
	errorMessagesMap = map[ErrorCode]string{
		ErrorCodeInvalidArgument:  "You have provided an invalid argument.",
		ErrorCodeResourceNotFound: "Resource not found.",
		ErrorCodeUnauthorized:     "You are not authorized to perform request.",
	}

	statusCodesMap = map[ErrorCode]int{
		ErrorCodeResourceNotFound: http.StatusNotFound,
		ErrorCodeUnauthorized:     http.StatusUnauthorized,
	}
)
