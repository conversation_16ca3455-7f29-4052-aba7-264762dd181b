package utils

import (
	"crypto/aes"
	"encoding/base64"
	"fmt"
	// "github.com/pavlo-v-chernykh/keystore-go/v4"
)

func DecryptEtimsData(encStr string) (string, error) {
	if encStr == "" {
		return "", nil
	}

	base64EncodedKey := "CH2ZWtAGYv7kYoFhQ+jK0g=="

	// Decode the base64 encoded key
	key, err := base64.StdEncoding.DecodeString(base64EncodedKey)
	if err != nil {
		return "", fmt.Erro<PERSON>("failed to decode base64 encoded key: %w", err)
	}

	// Decode the base64 encoded encrypted string
	decodedBytes, err := base64.StdEncoding.DecodeString(encStr)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 encoded string: %w", err)
	}

	// Create AES cipher block
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to create AES cipher: %w", err)
	}

	// Check block size
	if len(decodedBytes) < aes.BlockSize {
		return "", fmt.<PERSON><PERSON><PERSON>("ciphertext too short")
	}

	// Decrypt using AES in ECB mode (no IV)
	if len(decodedBytes)%aes.BlockSize != 0 {
		return "", fmt.Errorf("ciphertext is not a multiple of the block size")
	}

	decrypted := make([]byte, len(decodedBytes))
	for i := 0; i < len(decodedBytes); i += aes.BlockSize {
		block.Decrypt(decrypted[i:i+aes.BlockSize], decodedBytes[i:i+aes.BlockSize])
	}

	// Remove padding (PKCS#5 or PKCS#7)
	decrypted = removePadding(decrypted)

	// Convert decrypted bytes to string
	result := string(decrypted)
	// utils.Log.Infof("decoded etims result=[%v]\n", result)
	return result, nil
}

// Function to remove PKCS#5 or PKCS#7 padding
func removePadding(data []byte) []byte {
	paddingLength := int(data[len(data)-1])
	if paddingLength > aes.BlockSize || paddingLength == 0 {
		return data // Padding is invalid, return the original data
	}
	return data[:len(data)-paddingLength]
}
