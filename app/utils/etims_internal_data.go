package utils

import (
	"bytes"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"crypto/aes"
	"encoding/hex"
	"fmt"
	"math"
	"strconv"
	"strings"
)

// Base32 encoding characters
const base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"

var base32Lookup = []int{
	255, 255, 26, 27, 28, 29, 30, 31, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 0, 1, 2,
	3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
	13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
	23, 24, 25, 255, 255, 255, 255, 255, 255, 0,
	1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
	11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
	21, 22, 23, 24, 25, 255, 255, 255, 255, 255,
}

// Function to convert integer to hexadecimal string with fixed length
func intToHex(val int64, length int) string {
	hexString := strconv.FormatInt(val, 16)
	nbChar := len(hexString)

	if nbChar < length*2 {
		hexString = fmt.Sprintf("%0*s", length*2, hexString)
	}

	return hexString
}

func encryptAES_ECB_NoPadding(data, keyHex string) ([]byte, error) {
	// Convert key from hex to bytes
	key, err := HexToByteArray2(keyHex, 32)
	if err != nil {
		return nil, err
	}

	// Create AES cipher block
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Convert data from hex to bytes
	// plaintext := HexToByteArray(data, 16)
	plaintext, err := HexToByteArray2(data, 16)
	if err != nil {
		return nil, err
	}

	// Log.Infof("plaintext=[%v]\n", plaintext)

	// Check plaintext length and pad if necessary
	blockSize := block.BlockSize()
	if len(plaintext)%blockSize != 0 {
		return nil, fmt.Errorf("plaintext length must be a multiple of the block size")
	}

	// Encrypt the plaintext
	// ciphertext := make([]byte, len(plaintext))
	// for bs, be := 0, blockSize; bs < len(plaintext); bs, be = bs+blockSize, be+blockSize {
	// 	block.Encrypt(ciphertext[bs:be], plaintext[bs:be])
	// }

	ciphertext := make([]byte, len(plaintext))
	for i := 0; i < len(plaintext); i += blockSize {
		block.Encrypt(ciphertext[i:i+blockSize], plaintext[i:i+blockSize])
	}

	return ciphertext, nil
}

func EncryptEtimsData(data, key1 string) (string, error) {

	// Log.Infof("raw string=[%v]\n", data)

	ciphertext, err := encryptAES_ECB_NoPadding(data, key1)
	if err != nil {
		Log.Infof("unable to encryptAES_ECB_NoPadding, err=[%v]\n", err)
		return "", err
	}
	// Log.Infof("ciphertext=[%v]\n", ciphertext)
	// signedBytes := ConvertUnsignedToSigned(ciphertext)

	// Log.Infof("Unsigned bytes: %v\n", ciphertext)
	// Log.Infof("Signed bytes: %v\n", signedBytes)

	encryptedValue := EtimsEncode(ciphertext)

	// Log.Infof("shouldEqual=[AVA7XAGAAAATFAZAA2AVHASAAM]\n")
	// Log.Infof("equals=[%v]\n", encryptedValue == "AVA7XAGAAAATFAZAA2AVHASAAM")
	// Log.Infof("encryptedValue=[%v]\n", encryptedValue)

	// decodedEncryptedValue := decode(encryptedValue)
	// Log.Infof("decodedEncryptedValue=[%v]\n", decodedEncryptedValue)

	return encryptedValue, nil
}

// HexToByteArray converts a hex string to a byte array.
func HexToByteArray2(hexStr string, n int) ([]byte, error) {
	hexStr = padHex(hexStr, n*2) // Ensure hex string is padded to correct length
	bytes := make([]byte, n)
	_, err := hex.Decode(bytes, []byte(hexStr))
	if err != nil {
		return nil, err
	}
	return bytes, nil
}

// padHex pads the hex string to the desired length with leading zeros.
func padHex(hexStr string, length int) string {
	for len(hexStr) < length {
		hexStr = "0" + hexStr
	}
	return hexStr
}

// padToBlockSize pads the plaintext to the block size with zeroes.
func padToBlockSize(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	if padding == blockSize {
		return data
	}
	pad := bytes.Repeat([]byte{0}, padding)
	return append(data, pad...)
}

// HexToByteArray converts a hexadecimal string to a byte array.
func HexToByteArray(hexStr string, n int) []byte {
	if len(hexStr)%2 != 0 {
		hexStr = "0" + hexStr
	}
	bytes, _ := hex.DecodeString(hexStr)
	if len(bytes) > n {
		bytes = bytes[:n]
	}
	return bytes
}

// Simulates the behavior of the Java getInternalData function
func GenerateInternalData(
	invoice *entities.Invoice,
	form *forms.CreateEtimsSalesForm,
	tinBhfPath string,
) (string, error) {

	rstData := ""
	intNSTaxAmtB := 0
	intNRTaxAmtB := 0

	// salesTyCd, rcptTyCd string, taxTyBTaxAmt float64, rptNo, totInvcNoCnt int64, tinBhfPath string
	salesTyCd := "N"
	rcptTyCd := "S"
	taxTyBTaxAmt := invoice.VatB

	// Log.Infof("invoice.VatB=[%v]\n", invoice.VatB)
	// Log.Infof("form.Receipt.RptNo=[%v]\n", form.Receipt.RptNo)
	// Log.Infof("form.InvcNo=[%v]\n", form.InvcNo)

	if invoice.IsCreditNote {
		rcptTyCd = "R"
	}

	if salesTyCd == "N" {
		if rcptTyCd == "S" {
			intNSTaxAmtB = int(taxTyBTaxAmt)
		} else {
			intNRTaxAmtB = int(taxTyBTaxAmt)
		}
	}

	intTaxTyBTaxAmt := int(math.Floor(taxTyBTaxAmt))
	intNRTaxAmtB += intTaxTyBTaxAmt

	var sb strings.Builder
	sb.WriteString(intToHex(int64(intNSTaxAmtB), 5))
	sb.WriteString(intToHex(int64(intNRTaxAmtB), 5))
	sb.WriteString(intToHex(int64(form.Receipt.RptNo), 2))
	sb.WriteString(intToHex(int64(form.InvcNo), 4))

	intrlKey := GetKey("intrlKey", tinBhfPath)

	GetKey("cmcKey", tinBhfPath)
	GetKey("signKey", tinBhfPath)
	GetKey("mrcNo", tinBhfPath)
	GetKey("dvcSrlNo", tinBhfPath)

	decodedIntrlKey := EtimsDecode(intrlKey)
	// Log.Infof("decodedIntrlKey=[%v]\n", decodedIntrlKey)

	base32ToHex := convertToHex(decodedIntrlKey)

	hexKey := strings.ToUpper(base32ToHex)
	// Log.Infof("hexKey=[%v]\n", hexKey)

	sbString := sb.String()
	// Should equal 00000000000000000000017800004eb2
	// Log.Infof("sbString=[%v]\n", sbString)
	// Log.Infof("hexKey=[%v]\n", hexKey)

	rstData, err := EncryptEtimsData(sbString, hexKey)
	if err != nil {
		Log.Infof("unable to encrypt data=[%v], err=[%v]\n", sbString, err)
		return "", err
	}

	return rstData, nil
}

// Simulates the key retrieval (you need to replace this with actual key retrieval logic)
func GetKey(keyType, path string) string {
	decKey, err := GetEbmDevicePath(path, keyType)
	if err != nil {
		Log.Infof("unable to get key of type=[%v], err=[%v]\n", keyType, err)
		return ""
	}
	// Log.Infof("decKey=[%v]\n", decKey)

	// KRA ETIMS VSCU ENCODED Secret Key:  CH2ZWtAGYv7kYoFhQ+jK0g==
	// Hex version is 087d995ad00662fee462816143e8cad2

	decryptedStr, err := DecryptEtimsData(decKey)
	if err != nil {
		Log.Infof("err getting key=[%v], err=[%v]\n", keyType, err)
	} else {
		// Log.Infof("%v=[%v]\n", keyType, decryptedStr)
	}
	return decryptedStr
}

func convertToHex(data []byte) string {
	var buf string
	for _, b := range data {
		halfbyte := (b >> 4) & 0xF
		for i := 0; i < 2; i++ {
			if halfbyte <= 9 {
				buf += string('0' + halfbyte)
			} else {
				buf += string('a' + (halfbyte - 10))
			}
			halfbyte = b & 0xF
		}
	}
	return buf
}

func EtimsDecode(base32 string) []byte {
	bytes := make([]byte, len(base32)*5/8)
	index, offset := 0, 0

	for i := 0; i < len(base32); i++ {
		lookup := int(base32[i]) - 48
		if lookup >= 0 && lookup < len(base32Lookup) {
			digit := base32Lookup[lookup]
			if digit != 255 {
				if index <= 3 {
					index = (index + 5) % 8
					if index == 0 {
						bytes[offset] |= byte(digit)
						offset++
						if offset >= len(bytes) {
							break
						}
					} else {
						bytes[offset] |= byte(digit << (8 - index))
					}
				} else {
					index = (index + 5) % 8
					bytes[offset] |= byte(digit >> index)
					offset++
					if offset >= len(bytes) {
						break
					}
					bytes[offset] |= byte(digit << (8 - index))
				}
			}
		}
	}

	return bytes
}

func ConvertUnsignedToSigned(unsignedBytes []byte) []int8 {
	signedBytes := make([]int8, len(unsignedBytes))
	for i, b := range unsignedBytes {
		signedBytes[i] = int8(b)
	}
	return signedBytes
}

func EtimsEncode(bytes []byte) string {
	var base32 strings.Builder
	base32Chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"

	// Adjusting the encoding to fit Base32 standards
	i, index, digit := 0, 0, 0
	for i < len(bytes) {
		currByte := int(bytes[i])
		if currByte < 0 {
			currByte += 256
		}

		// When index is 0 or less than 5, shift and mask the current byte
		if index > 3 {
			var nextByte int
			if i+1 < len(bytes) {
				nextByte = int(bytes[i+1])
				if nextByte >= 0 {
					nextByte = int(bytes[i+1])
				} else {
					nextByte = int(bytes[i+1]) + 256
				}
			} else {
				nextByte = 0
			}

			digit = currByte & (255 >> index)
			index = (index + 5) % 8
			digit <<= index
			digit |= nextByte >> (8 - index)
			i++
		} else {
			digit = currByte >> (8 - index + 5&0x1F)
			index = (index + 5) % 8
			if index == 0 {
				i++
			}
		}

		// Log.Infof("currByte=[%v], digit=[%v]\n", currByte, digit)
		base32.WriteByte(base32Chars[digit])
	}

	return base32.String()
}
