package utils

import (
	"fmt"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"regexp"
	"strings"
)

var (
	nonDigitRe = regexp.MustCompile("[^0-9]")
	kenyanRe   = regexp.MustCompile("^[71]{1}[0-9]{8}$")
)

func GetPhoneNumberCountryISOCODE(phoneNumberStr string) (string, error) {

	newPhoneNumber := phoneNumberStr

	if len(newPhoneNumber) > 1 && (newPhoneNumber[0:1] == "+" || newPhoneNumber[0:1] == "0") {
		newPhoneNumber = newPhoneNumber[1:]
	}

	countryCode, err := getCountryCode(newPhoneNumber)
	if err != nil {
		return "", err
	}

	return countryCodes[countryCode], nil
}

func ParsePhoneNumber(phoneNumberStr string) (entities.PhoneNumber, error) {

	var phoneNumber entities.PhoneNumber

	phoneNumberStr = sanitizeKenyanNumber(phoneNumberStr)
	phoneNumberStr = nonDigitRe.ReplaceAllString(phoneNumberStr, "")

	if len(phoneNumberStr) < 8 {
		return phoneNumber, fmt.Errorf("number (%v) is less than 8 digits", phoneNumberStr)
	}

	if len(phoneNumberStr) > 15 {
		return phoneNumber, fmt.Errorf("number (%v) is greater than 15 digits", phoneNumberStr)
	}

	countryCode, err := getCountryCode(phoneNumberStr)
	if err != nil {
		return phoneNumber, err
	}

	// num, err := phonenumbers.Parse(phoneNumberStr, countryCodes[countryCode])
	// if err != nil {
	// 	return phoneNumber, fmt.Errorf("invalid phone number")
	// }

	// countryCode = strconv.FormatInt(int64(num.GetCountryCode()), 10)
	// number := strconv.FormatInt(int64(num.GetNationalNumber()), 10)
	number := strings.Replace(phoneNumberStr, countryCode, "", 1)

	number = strings.TrimPrefix(number, countryCode)
	countryCode = "+" + countryCode

	phoneNumber.CountryCode = countryCode
	phoneNumber.Number = number

	return phoneNumber, nil
}

func getCountryCode(phoneNumber string) (string, error) {

	valid := false
	countryCode := ""

	for i := 0; i < 3; i++ {
		countryCode = phoneNumber[0 : i+1]
		if _, ok := countryCodes[countryCode]; !ok {
			continue
		}

		valid = true
		break
	}

	if !valid {
		return "", fmt.Errorf("number (%v) does not have a valid country code", phoneNumber)
	}

	return countryCode, nil
}

func sanitizeKenyanNumber(phoneNumber string) string {

	newPhoneNumber := phoneNumber

	if len(newPhoneNumber) > 1 && (newPhoneNumber[0:1] == "+" || newPhoneNumber[0:1] == "0") {
		newPhoneNumber = newPhoneNumber[1:]
	}

	if len(newPhoneNumber) > 3 && newPhoneNumber[0:3] == "254" {
		newPhoneNumber = newPhoneNumber[3:]
	}

	if len(newPhoneNumber) > 1 && newPhoneNumber[0:1] == "0" {
		newPhoneNumber = newPhoneNumber[1:]
	}

	if kenyanRe.MatchString(newPhoneNumber) {
		return "+254" + newPhoneNumber
	}

	return phoneNumber
}

func GetPhoneNumberString(form *forms.PhoneNumberForm) string {
	if len(form.Number) > 1 && (form.Number[0:1] == "+" || form.Number[0:1] == "0") {
		form.Number = form.Number[1:]
	}

	return form.CountryCode + form.Number
}
