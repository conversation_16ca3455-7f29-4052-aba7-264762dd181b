package utils

import (
	"regexp"
	"strings"
)

/**

4.17. Item Code
itemCd is mandatory and it has to be unique. In other words, each item has its
unique itemCd.
Below is how item code is

generated. For example: itemCd:
KE2NTBA0000012

KE: Country of Origin (Kenys)
2: Product Type (Finished Product)
NT: Packaging Unit (NET)
BA: Quantity Unity (Barrel)
0000012: increments from 0000001 to N value



**/
func GenerateKRAItemCode(itemName string, codeLen int) string {

	codeLen = codeLen - 3
	// Replaces special charaters with empty string
	regex := regexp.MustCompile("[^a-zA-Z0-9]+")

	itemName = "KE2" + itemName

	// Replace special characters with an empty string
	processedString := regex.ReplaceAllString(itemName, "")

	// Uppercase string
	processedString = strings.ToUpper(processedString)

	// Limit to 20 chars max len
	if len(processedString) > codeLen {
		// Truncate the string to 20 characters
		return processedString[:codeLen]
	}

	return processedString
}

func GenerateItemCode(itemName string, codeLen int) string {

	// Replaces special charaters with empty string
	regex := regexp.MustCompile("[^a-zA-Z0-9]+")

	// Replace special characters with an empty string
	processedString := regex.ReplaceAllString(itemName, "")

	// Uppercase string
	processedString = strings.ToUpper(processedString)

	// Limit to 20 chars max len
	if len(processedString) > codeLen {
		// Truncate the string to 20 characters
		return processedString[:codeLen]
	}

	return processedString
}
