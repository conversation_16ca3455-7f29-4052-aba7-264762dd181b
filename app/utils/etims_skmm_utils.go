package utils

import (
	"io"
	"os"
	"path/filepath"
	"strings"
)

func GetEbmDevicePath(tinBhfPath string, keyType string) (string, error) {
	return CreateFilePath(filepath.Join("AppData", "EbmData", tinBhfPath, "Ebm", "device", keyType))
}

func GetEbmSequencePath(tinBhfPath string) (string, error) {
	return CreateFilePath(filepath.Join("AppData", "EbmData", tinBhfPath, "Ebm", "sequence"))
}

func CreateFilePath(path string) (string, error) {
	// Log.Infof("CreateFilePath path=[%v]\n", path)
	// Get the user's home directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}

	// Combine the home directory with the fileKind path
	filePath := filepath.Join(homeDir, path)

	// Log.Infof("filePath=[%v]\n", filePath)

	// Check if the folder path exists, if not, create it
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		Log.Infof("filePath does not exist, creating filePath=[%v]\n", filePath)
		err = os.MkdirAll(filePath, os.ModePerm)
		if err != nil {
			return "", err
		}

		return "", nil
	} else {
		// Get and file content
		return readFileAsString(filePath)
	}
}

func readFileAsString(filePath string) (string, error) {
	// Open the file
	// Log.Infof("retrieving contents of filePath=[%v]\n", filePath)
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Use a StringBuilder to efficiently build the string
	var result string
	buf := make([]byte, 1024)
	for {
		n, err := file.Read(buf)
		if err != nil && err != io.EOF {
			return "", err
		}
		if n == 0 {
			break
		}
		result += string(buf[:n])
	}

	// Log.Infof("result=[%v]\n", result)

	return result, nil
}

// Lpad pads the input string `data` with spaces on the left to make its length `strLen`.
func Lpad(data string, strLen int) string {
	data = strings.TrimSpace(data)
	dataLen := len(data)
	if dataLen == strLen || strLen == 0 {
		return data
	}
	if dataLen < strLen {
		padding := strLen - dataLen
		return strings.Repeat(" ", padding) + data
	}
	return "ERROR"
}

func LpadAmount(data string, strLen int) string {
	data = strings.ReplaceAll(data, ".", ",")
	return Lpad(data, strLen)
}
