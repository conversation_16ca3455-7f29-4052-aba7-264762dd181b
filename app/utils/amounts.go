package utils

import (
	"compliance-and-risk-management-backend/app/entities"
	"strings"
)

func ExtractAmountInBrackets(invoice *entities.Invoice, input string) error {

	input = strings.ReplaceAll(input, " ", "")
	strRunes := []rune(input)

	var amtRunes []rune
	var canAppendingNumber bool
	var netAndVatAmts []string

	for i := 0; i < len(strRunes); i++ {
		if strRunes[i] == '(' {
			canAppendingNumber = true
		}

		if canAppendingNumber && (strRunes[i] != '(' && strRunes[i] != ')' && strRunes[i] != ',') {
			amtRunes = append(amtRunes, strRunes[i])
		}

		if strRunes[i] == ')' {
			amountString := string(amtRunes)
			netAndVatAmts = append(netAndVatAmts, amountString)
			canAppendingNumber = false
			amtRunes = []rune{}
		}
	}

	numAmtsFound := len(netAndVatAmts)

	if numAmtsFound == 1 {
		invoice.TaxableAmount = ConvertStringToFloat64(netAndVatAmts[0])

		Log.Infof("netAndVatAmts[0]=[%v]\n", netAndVatAmts[0])

	} else if numAmtsFound > 1 {

		Log.Infof("netAndVatAmts[0]=[%v]\n", netAndVatAmts[0])
		Log.Infof("netAndVatAmts[1]=[%v]\n", netAndVatAmts[1])

		invoice.NetAmount = ConvertStringToFloat64(netAndVatAmts[0])
		invoice.TaxableAmount = ConvertStringToFloat64(netAndVatAmts[1])
	}

	return nil
}
