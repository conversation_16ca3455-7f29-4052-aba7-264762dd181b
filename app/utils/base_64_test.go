package utils

import (
	"encoding/base64"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestBase64EncodingAndDecoding(t *testing.T) {
	Convey("Base64EncodingAndDecoding Util", t, func() {

		Convey("can decrypt data", func() {

			encodedKey := "rtZzYYaQvIKeh22Vz4FSzEM2OjLvK+k8GJr3bmPiw/+1oSwnsk8RZ8mGGKm5/qsh2bUfxr8w6OpMVszz/1dU8A=="

			// Decode the Base64 string
			decodedKey, err := base64.StdEncoding.DecodeString(encodedKey)
			if err != nil {
				fmt.Println("Error decoding Base64:", err)
				return
			}

			// Print the length of the decoded key
			Log.Infof("Decoded Key: %x\n", decodedKey)
			Log.Infof("Length: %d bytes\n", len(decodedKey))

			So(err, ShouldBeNil)
			So(decodedKey, ShouldEqual, "09224EC00BCB483EA490EF12A247FEDAB870C86617F1498C89D1")
		})

		Convey("can encode and decode a string", func() {

			data := "09224EC00BCB483EA490EF12A247FEDAB870C86617F1498C89D1"

			sEnc := base64.StdEncoding.EncodeToString([]byte(data))
			fmt.Println(sEnc)

			sDec, _ := base64.StdEncoding.DecodeString(sEnc)
			fmt.Println(string(sDec))
			fmt.Println()

			uEnc := base64.URLEncoding.EncodeToString([]byte(data))
			fmt.Println(uEnc)
			uDec, _ := base64.URLEncoding.DecodeString(uEnc)
			fmt.Println(string(uDec))
		})
	})
}
