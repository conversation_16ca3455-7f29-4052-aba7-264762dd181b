package utils

import (
	"compliance-and-risk-management-backend/app/entities"
	"fmt"
	"os"

	"github.com/johnfercher/maroto/pkg/color"
	"github.com/johnfercher/maroto/pkg/consts"
	"github.com/johnfercher/maroto/pkg/pdf"
	"github.com/johnfercher/maroto/pkg/props"
)

func GeneratePDF(
	companyRegistration *entities.CompanyRegistration,
	zReport *entities.ZReport,
	part1Data [][]string,
	part2Data [][]string,
	part3Data [][]string,
	part4Data [][]string,
	part5Data [][]string,
	destFile string,
) {
	m := pdf.NewMaroto(consts.Portrait, consts.A3)
	m.SetPageMargins(1, 0, 180)

	buildHeading(m, companyRegistration, zReport, part1Data)
	// buildZReportSummariesPart1(m, part1Data)
	buildZReportSummariesPart2(m, part2Data)
	buildZReportSummariesPart3(m, part3Data)
	buildZReportSummariesPart4(m, part4Data)
	buildZReportSummariesPart5(m, companyRegistration, part5Data)

	err := m.OutputFileAndClose(destFile)
	if err != nil {
		fmt.Println("⚠️  Could not save PDF:", err)
		os.Exit(1)
	}

	Log.Infof("[%v] PDF saved successfully\n", destFile)
}

func buildHeading(
	m pdf.Maroto,
	companyRegistration *entities.CompanyRegistration,
	zReport *entities.ZReport,
	part1Data [][]string,
) {

	m.Row(12, func() {
		m.Col(4, func() {
			err := m.FileImage("images/tra_logo.png", props.Rect{
				Left:    0,
				Top:     0,
				Center:  true,
				Percent: 100,
			})

			if err != nil {
				fmt.Println("Image file was not loaded 😱 - ", err)
			}
		})

		m.ColSpace(0)

		m.Col(12, func() {
			m.Row(52, func() {
				m.Text("*** START OF LEGAL RECEIPT ***", props.Text{
					Top:    0,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})
				m.Text(""+companyRegistration.Name, props.Text{
					Top:    5,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})
				m.Text(""+companyRegistration.City, props.Text{
					Top:    10,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				m.Text("TIN: "+companyRegistration.TIN, props.Text{
					Top:    15,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				m.Text("VRN: "+companyRegistration.VRN, props.Text{
					Top:    20,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				m.Text("SERIAL NUMBER: "+companyRegistration.Serial, props.Text{
					Top:    25,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				m.Text("UIN: "+companyRegistration.UIN, props.Text{
					Top:    30,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				m.Text("TAX OFFICE: "+companyRegistration.TaxOffice, props.Text{
					Top:    35,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Center,
				})

				dateFormat := "01/02/2006"
				zReportDateStr := zReport.CreatedAt.Format(dateFormat)

				dateFormat = "15:04:05"
				zReportTimeStr := zReport.CreatedAt.Format(dateFormat)

				dateStr := fmt.Sprintf("DATE: %v                                                 TIME: %v", zReportDateStr, zReportTimeStr)
				m.Text(dateStr, props.Text{
					Top:    45,
					Size:   10,
					Family: consts.Helvetica,
					Align:  consts.Left,
				})
			})
		})

		buildZReportSummariesPart1(m, zReport, part1Data)
	})
}

func buildZReportSummariesPart1(
	m pdf.Maroto,
	zReport *entities.ZReport,
	part1Data [][]string,
) {

	m.Line(1)
	tableHeadings := []string{"", ""}
	m.Row(6, func() {
		m.Col(12, func() {
			m.Text("DAILY Z REPORT", props.Text{
				Top:    1,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Center,
			})
		})
	})

	m.Line(1)

	dateFormat := "20060102"
	zReportDateStr := zReport.CreatedAt.Format(dateFormat)

	dateFormat = "2006-01-02 15:04:05"
	previousDate := zReport.CreatedAt.AddDate(0, 0, -1)
	previousZReportDateStr := previousDate.Format(dateFormat)

	m.Row(10, func() {
		m.Col(12, func() {
			m.Text("CURRENT Z:                                                                "+zReportDateStr, props.Text{
				Top:    2,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Left,
			})
			m.Text("PREVIOUS Z:                                             "+previousZReportDateStr, props.Text{
				Top:    6,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Left,
			})
		})
	})

	m.Line(1)

	m.SetBackgroundColor(color.NewWhite())

	m.TableList(tableHeadings, part1Data, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{9, 3},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{9, 3},
		},
		Align:              consts.Left,
		HeaderContentSpace: 1,
		Line:               false,
	})

	m.Line(1)
}

func buildZReportSummariesPart2(
	m pdf.Maroto,
	part2Data [][]string,
) {
	tableHeadings := []string{"", ""}
	m.Row(6, func() {
		m.Col(12, func() {
			m.Text("PAYMENTS REPORT", props.Text{
				Top:    1,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Center,
			})
		})
	})

	m.Line(1)

	m.SetBackgroundColor(color.NewWhite())

	m.TableList(tableHeadings, part2Data, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{9, 3},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{9, 3},
		},
		Align:              consts.Left,
		HeaderContentSpace: 1,
		Line:               false,
	})

	m.Line(1)
}

func buildZReportSummariesPart3(
	m pdf.Maroto,
	part3Data [][]string,
) {
	tableHeadings := []string{"", ""}
	m.Row(6, func() {
		m.Col(12, func() {
			m.Text("TAX REPORT", props.Text{
				Top:    1,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Center,
			})
		})
	})

	m.Line(1)

	m.SetBackgroundColor(color.NewWhite())

	m.TableList(tableHeadings, part3Data, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{9, 3},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{9, 3},
		},
		Align:              consts.Left,
		HeaderContentSpace: 1,
		Line:               false,
	})
}

func buildZReportSummariesPart4(
	m pdf.Maroto,
	part3Data [][]string,
) {
	tableHeadings := []string{"", ""}
	m.Row(6, func() {
		m.Col(12, func() {
			m.Text("TOTAL", props.Text{
				Top:    1,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Center,
			})
		})
	})

	m.SetBackgroundColor(color.NewWhite())

	m.TableList(tableHeadings, part3Data, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{9, 3},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{9, 3},
		},
		Align:              consts.Left,
		HeaderContentSpace: 1,
		Line:               false,
	})
}

func buildZReportSummariesPart5(
	m pdf.Maroto,
	companyRegistration *entities.CompanyRegistration,
	part3Data [][]string,
) {
	tableHeadings := []string{"", ""}
	m.Row(6, func() {
		m.Col(12, func() {
			m.Text("TOTAL SUMMARY", props.Text{
				Top:    1,
				Size:   10,
				Family: consts.Helvetica,
				Style:  consts.Bold,
				Align:  consts.Center,
			})
		})
	})

	m.SetBackgroundColor(color.NewWhite())

	m.TableList(tableHeadings, part3Data, props.TableList{
		HeaderProp: props.TableListContent{
			Size:      9,
			GridSizes: []uint{9, 3},
		},
		ContentProp: props.TableListContent{
			Size:      8,
			GridSizes: []uint{9, 3},
		},
		Align:              consts.Left,
		HeaderContentSpace: 1,
		Line:               false,
	})

	m.Line(1)
	m.Row(12, func() {
		m.Col(10, func() {
			m.Text("SERIAL NUMBER:                                           "+companyRegistration.Serial, props.Text{
				Top:    2,
				Size:   10,
				Family: consts.Helvetica,
				Align:  consts.Left,
			})
			m.Text("*** END OF LEGAL RECEIPT ***", props.Text{
				Top:    15,
				Size:   10,
				Family: consts.Helvetica,
				Align:  consts.Center,
			})
		})
	})
}
