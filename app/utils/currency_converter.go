package utils

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/cockroachdb/apd"
	"github.com/leekchan/accounting"
)

func ConvertCurrencyAmountFromInt64ToFloat64(price int64, exponent int32, precision int) (float64, error) {
	displayPriceStr := accounting.FormatNumberBigDecimal(apd.New(price, -exponent), precision, ",", ".")
	displayPriceStr = strings.ReplaceAll(displayPriceStr, ",", "")
	floatAmt, err := strconv.ParseFloat(displayPriceStr, 64)
	if err != nil {
		return 0.00, err
	}
	return floatAmt, nil
}

func ConvertCurrencyAmountFromInt64ToFloat64Str(price int64, exponent int32, precision int) string {
	displayPriceStr := accounting.FormatNumberBigDecimal(apd.New(price, -exponent), precision, ",", ".")
	return displayPriceStr
}

func ConvertCurrencyAmountFromFloat64ToInt64(amount float64, scale int) (int64, error) {
	amountStr := fmt.Sprintf("%."+strconv.Itoa(scale)+"f", amount)
	amountStr = strings.Replace(amountStr, ".", "", -1)
	amountInt64, err := strconv.ParseInt(amountStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return amountInt64, nil
}

func ConvertAmountToPaystackRequestAmount(amount float64) (string, error) {
	paystackAmtFloat := amount * 100
	paystackAmtStr := fmt.Sprintf("%v", paystackAmtFloat)
	return paystackAmtStr, nil
}
