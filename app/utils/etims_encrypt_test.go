package utils

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestEncryptEtimsData(t *testing.T) {
	Convey("EncryptEtimsData Util", t, func() {

		encryptionKey := "DFC7A26B5BEEFFC0E85CE77FBE0060D6F85F0BDADF79ED875A00000000000000"

		Convey("can encrypt data", func() {

			rawString := "00000000000000000000017800004eb2"
			encryptedData, err := EncryptEtimsData(rawString, encryptionKey)
			So(err, ShouldBeNil)
			So(encryptedData, ShouldEqual, "AVA7XAGAAAATFAZAA2AVHASAAM")
		})

		Convey("can round off a float64", func() {
			var val float64 = 98.189876656778
			roundedValue := RoundFloat64(val, 2)
			So(roundedValue, ShouldEqual, 98.19)
		})
	})
}
