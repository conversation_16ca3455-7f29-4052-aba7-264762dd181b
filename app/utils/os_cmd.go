package utils

import (
	"os/exec"
	"runtime"
)

func RunOSLevelCommand(command string, args ...string) (string, error) {
	var cmd *exec.Cmd
	if runtime.GOOS == "darwin" || runtime.GOOS == "linux" {
		bashCmds := []string{"-c", command}
		bashCmds = append(bashCmds, args...)
		cmd = exec.Command("bash", bashCmds...)
	} else {
		cmd = exec.Command(command, args...)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		Log.Infof("Error: %s\n", err)
		return "", err
	}

	return string(output), nil
}
