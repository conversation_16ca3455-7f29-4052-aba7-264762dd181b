package utils

import (
	"net/http"
)

type ErrorCode string

func (s ErrorCode) String() string {
	return string(s)
}

const (
	ErrorCodeDuplicateResource ErrorCode = "duplicate_resource"
	ErrorCodeResourceNotFound  ErrorCode = "resource_not_found"
	ErrorCodeClientDeprecated  ErrorCode = "client_deprecated"
	ErrorCodeMaintenanceMode   ErrorCode = "maintenance_mode"

	ErrorCodeContactDoesNotExist              ErrorCode = "contact_does_not_exist"
	ErrorCodeContactExistsPhone               ErrorCode = "contact_exists_phone"
	ErrorCodeIncorrectPassword                ErrorCode = "invalid_old_password"
	ErrorCodeInsuficientBalance               ErrorCode = "insufficient_balance"
	ErrorCodeInvalidAPIKey                    ErrorCode = "invalid_api_key"
	ErrorCodeInvalidArgument                  ErrorCode = "invalid_argument"
	ErrorCodeInvalidCredentials               ErrorCode = "invalid_credentials"
	ErrorCodeInvalidForm                      ErrorCode = "invalid_form"
	ErrorCodeInvalidPassword                  ErrorCode = "invalid_password"
	ErrorCodeInvalidPasswordConfirmation      ErrorCode = "invalid_password_confirmation"
	ErrorCodeInvalidPasswordLength            ErrorCode = "invalid_password_length"
	ErrorCodeInvalidToken                     ErrorCode = "invalid_token"
	ErrorCodeInvalidUsername                  ErrorCode = "invalid_username"
	ErrorCodeInvalidUsernameLength            ErrorCode = "invalid_username_length"
	ErrorCodeInvalidUserStatus                ErrorCode = "invalid_user_status"
	ErrorCodeLoginRequired                    ErrorCode = "login_required"
	ErrorCodePasswordResetTokenExists         ErrorCode = "password_reset_token_exists"
	ErrorCodeRequestFailed                    ErrorCode = "request_failed"
	ErrorCodeRoleActive                       ErrorCode = "role_active"
	ErrorCodeRoleForbidden                    ErrorCode = "role_forbidden"
	ErrorCodeSessionExpired                   ErrorCode = "session_expired"
	ErrorCodeSQLFailed                        ErrorCode = "sql_failed"
	ErrorCodeUnauthorized                     ErrorCode = "unauthorized"
	ErrorCodeUserExistsEmail                  ErrorCode = "user_exists_email"
	ErrorCodeUserExistsPhone                  ErrorCode = "user_exists_phone"
	ErrorCodeUserExistsUsername               ErrorCode = "user_exits_username"
	ErrorCodeUserExistsMerchant               ErrorCode = "user_exits_merchant"
	ErrorCodeVerificationCodeExpired          ErrorCode = "expired_verification_code"
	ErrorCodeVerificationCodeInvalid          ErrorCode = "invalid_verification_code"
	ErrorCodeTransactionReviewSharingDisabled ErrorCode = "transaction_review_sharing_disabled"

	// groups specific error code
	ErrorCodeInvalidGroupEventDateRange  ErrorCode = "invalid_event_date_range"
	ErrorCodeGroupNameNotAvailable       ErrorCode = "group_name_not_available"
	ErrorCodeGroupDoesNotExist           ErrorCode = "group_does_not_exist"
	ErrorCodeUserNotAMember              ErrorCode = "new_owner_not_a_member"
	ErrorCodeEmptySlice                  ErrorCode = "empty_slice"
	ErrorCodeGroupCoverPhotoLimitReached ErrorCode = "group_cover_photos_limit"
	ErrorCodeGroupMemberExists           ErrorCode = "group_member_exists"
	ErrorCodeGroupInviteExists           ErrorCode = "group_invite_exists"
	ErrorCodeInvalidTypingEvent          ErrorCode = "invalid_typing_event"

	//merchants
	ErrorCodeInvalidBusinessUsername             ErrorCode = "invalid_business_username"
	ErrorCodeMerchantDoesNotExist                ErrorCode = "merchant_not_found"
	ErrorCodeMerchantProfileDoesNotExist         ErrorCode = "merchant_profile_not_found"
	ErrorCodePaymentMethodDoesNotExist           ErrorCode = "payment_method_not_found"
	ErrorCodePaymentMethodExistsAccountReference ErrorCode = "payment_method_exists_account_reference"
	ErrorCodeMerchantUserDoesNotExist            ErrorCode = "merchant_not_linked_to_user"
	ErrorCodeMerchantExistsBusinessUsername      ErrorCode = "merchant_exits_business_username"
	ErrorCodeInvalidBusinessNameLength           ErrorCode = "invalid_business_name_length"
	ErrorCodeInvalidKeywordNameLength            ErrorCode = "invalid_keyword_name_length"
	ErrorCodeMerchantAboutSectionAlreadyExist    ErrorCode = "merchant_about_section_already_exists"
	ErrorCodeSmsMerchantDoesNotExist             ErrorCode = "sms_merchant_does_not_exist"
	ErrorCodeSmsMerchantLinkConflict             ErrorCode = "sms_merchant_link_conflict"
	ErrorCodeCoverPhotoLimitReached              ErrorCode = "merchant_cover_photos_limit"

	// merchant products
	ErrorCodeProductTypeExists           ErrorCode = "merchant_product_type_exists"
	ErrorCodeMerchantProductDoesNotExist ErrorCode = "merchant_product_does_not_exist"
)

var (
	defaultErrorMessage = "Failed to perform request. Please try again."

	errorMessagesMap = map[ErrorCode]string{
		ErrorCodeDuplicateResource: "Resource already exists",
		ErrorCodeResourceNotFound:  "Resource not found.",
		ErrorCodeClientDeprecated:  "Client version has been deprecated. Please upgrade and try again later.",
		ErrorCodeMaintenanceMode:   "Site is in maintenance mode. Please try again later.",

		ErrorCodeContactDoesNotExist:         "Can't update user contact since the contact provided does not exist.",
		ErrorCodeContactExistsPhone:          "A contact with that phone number already exists.",
		ErrorCodeIncorrectPassword:           "You have provided an incorrect password.",
		ErrorCodeInsuficientBalance:          "You have insufficient balance to perform request.",
		ErrorCodeInvalidAPIKey:               "You have provided an invalid api key.",
		ErrorCodeInvalidArgument:             "You have provided an invalid argument.",
		ErrorCodeInvalidCredentials:          "You have provided an invalid email and/or password. Please try again.",
		ErrorCodeInvalidForm:                 "You submitted an invalid form.",
		ErrorCodeInvalidPassword:             "Password must contain valid characters.",
		ErrorCodeInvalidPasswordConfirmation: "Password confirmation does not match password.",
		// ErrorCodeInvalidPasswordLength:            fmt.Sprintf("Password must contain at least %d characters", minPasswordLength),
		ErrorCodeInvalidToken:    "You have provided an invalid token.",
		ErrorCodeInvalidUsername: "Username must contain valid characters.",
		// ErrorCodeInvalidUsernameLength:            fmt.Sprintf("Username must contain at least %d characters and at most %d characters", minUsernameLength, maxUsernameLength),
		ErrorCodeInvalidUserStatus:                "Your account is an invalid state.",
		ErrorCodeLoginRequired:                    "Login required.",
		ErrorCodeInvalidBusinessNameLength:        "Business name must be between 2 and 100 characters",
		ErrorCodeInvalidKeywordNameLength:         "Keyword name must be between 2 and 100 characters",
		ErrorCodePasswordResetTokenExists:         "Password reset token already exists for user.",
		ErrorCodeRoleActive:                       "Your account is already active. Try logging in.",
		ErrorCodeRoleForbidden:                    "You are not permitted to perform request",
		ErrorCodeSessionExpired:                   "Session has expired. Try logging in again",
		ErrorCodeUnauthorized:                     "You are not authorized to perform request.",
		ErrorCodeUserExistsEmail:                  "A user with that email already exists.",
		ErrorCodeUserExistsPhone:                  "A user with that phone number already exists.",
		ErrorCodeUserExistsUsername:               "A user with that username already exists.",
		ErrorCodeUserExistsMerchant:               "A merchant with the same name for user already exists.",
		ErrorCodeVerificationCodeExpired:          "Your verification code has expired. Try requesting another token.",
		ErrorCodeVerificationCodeInvalid:          "You have entered an invalid verification code. Try requesting another token.",
		ErrorCodeTransactionReviewSharingDisabled: "Sharing for the transaction review is disabled",

		// groups
		ErrorCodeInvalidGroupEventDateRange:  "You have provided invalid date range. Start date must be less than end date",
		ErrorCodeGroupNameNotAvailable:       "Group name not available.",
		ErrorCodeGroupDoesNotExist:           "Group with specified id does not exist.",
		ErrorCodeUserNotAMember:              "New owner is not a group member.",
		ErrorCodeEmptySlice:                  "Array cannot be empty.",
		ErrorCodeGroupCoverPhotoLimitReached: "Group has the maximum number of cover photos allowed",
		ErrorCodeGroupMemberExists:           "Current user is already a group member",
		ErrorCodeGroupInviteExists:           "User is already invited to join the group",
		ErrorCodeInvalidTypingEvent:          "Invalid typing event",

		//merchants
		ErrorCodeMerchantExistsBusinessUsername:      "A merchant with that business username already exists.",
		ErrorCodeInvalidBusinessUsername:             "Merchant username must contain valid characters.",
		ErrorCodeMerchantDoesNotExist:                "Merchant does not exist.",
		ErrorCodeMerchantProfileDoesNotExist:         "Merchant profile for the merchant does not exist",
		ErrorCodePaymentMethodDoesNotExist:           "Payment method does not exist",
		ErrorCodeMerchantAboutSectionAlreadyExist:    "Merchant about section already exists",
		ErrorCodePaymentMethodExistsAccountReference: "A payment method with same account reference exists",
		ErrorCodeMerchantUserDoesNotExist:            "You are not authorised to make this request",
		ErrorCodeSmsMerchantDoesNotExist:             "No linked merchant with given id",
		ErrorCodeSmsMerchantLinkConflict:             "Sms merchant is linked to a different merchant account",
		ErrorCodeCoverPhotoLimitReached:              "Merchant has the maximum number of cover photos allowed",

		// merchant products
		ErrorCodeProductTypeExists:           "Merchant product type already exists.",
		ErrorCodeMerchantProductDoesNotExist: "Merchant product does not exist.",
	}

	statusCodesMap = map[ErrorCode]int{
		ErrorCodeDuplicateResource: http.StatusConflict,
		ErrorCodeResourceNotFound:  http.StatusNotFound,
		ErrorCodeClientDeprecated:  http.StatusForbidden,
		ErrorCodeMaintenanceMode:   http.StatusServiceUnavailable,

		ErrorCodeContactDoesNotExist:              http.StatusNotFound,
		ErrorCodeInvalidUserStatus:                http.StatusNotAcceptable,
		ErrorCodeLoginRequired:                    http.StatusUnauthorized,
		ErrorCodeRoleForbidden:                    http.StatusForbidden,
		ErrorCodeSessionExpired:                   http.StatusUnauthorized,
		ErrorCodeSQLFailed:                        http.StatusInternalServerError,
		ErrorCodeUnauthorized:                     http.StatusUnauthorized,
		ErrorCodeUserExistsEmail:                  http.StatusConflict,
		ErrorCodeUserExistsPhone:                  http.StatusConflict,
		ErrorCodeUserExistsUsername:               http.StatusConflict,
		ErrorCodeTransactionReviewSharingDisabled: http.StatusBadRequest,
		ErrorCodeInvalidBusinessNameLength:        http.StatusBadRequest,
		ErrorCodeInvalidKeywordNameLength:         http.StatusBadRequest,

		// groups
		ErrorCodeInvalidGroupEventDateRange:  http.StatusBadRequest,
		ErrorCodeGroupNameNotAvailable:       http.StatusConflict,
		ErrorCodeGroupDoesNotExist:           http.StatusNotFound,
		ErrorCodeUserNotAMember:              http.StatusNotFound,
		ErrorCodeGroupCoverPhotoLimitReached: http.StatusForbidden,
		ErrorCodeGroupMemberExists:           http.StatusConflict,
		ErrorCodeGroupInviteExists:           http.StatusConflict,
		ErrorCodeInvalidTypingEvent:          http.StatusBadRequest,

		//merchants
		ErrorCodeMerchantExistsBusinessUsername:      http.StatusConflict,
		ErrorCodeMerchantUserDoesNotExist:            http.StatusUnauthorized,
		ErrorCodeMerchantDoesNotExist:                http.StatusNotFound,
		ErrorCodePaymentMethodExistsAccountReference: http.StatusConflict,
		ErrorCodeMerchantAboutSectionAlreadyExist:    http.StatusConflict,
		ErrorCodePaymentMethodDoesNotExist:           http.StatusNotFound,
		ErrorCodeMerchantProfileDoesNotExist:         http.StatusNotFound,
		ErrorCodeSmsMerchantDoesNotExist:             http.StatusNotFound,
		ErrorCodeSmsMerchantLinkConflict:             http.StatusConflict,
		ErrorCodeCoverPhotoLimitReached:              http.StatusForbidden,

		// merchant products
		ErrorCodeProductTypeExists:           http.StatusConflict,
		ErrorCodeMerchantProductDoesNotExist: http.StatusNotFound,
	}
)
