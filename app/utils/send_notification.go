package utils

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"net/http"

	"github.com/jinzhu/gorm"
)

func SendNotification(db *gorm.DB, expoToken, title, content string, notification models.Notification) {

	url := "https://exp.host/--/api/v2/push/send"

	payload := make(map[string]interface{})
	payload["to"] = expoToken
	payload["title"] = title
	payload["body"] = content

	jsonData, err := json.Marshal(&notification)
	if err != nil {
		log.Println("Error converting payload...")
		return
	}
	payload["data"] = string(jsonData)

	bytedData, _ := json.Marshal(payload)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(bytedData))

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("cache-control", "no-cache")
	req.Header.Add("Postman-Token", "3c423399-6bf5-42fd-8b3e-68e9d1c37724")

	res, err := http.DefaultClient.Do(req)
	CheckError(err, "Send Notification Error..")

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	log.Println(res)
	log.Println(string(body))
}
