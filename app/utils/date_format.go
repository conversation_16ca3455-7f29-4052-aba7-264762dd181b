package utils

import (
	"fmt"
	"time"
)

const layout = "2006-01-02"

func ConvertSignatureDateToString(dateString string) (string, error) {
	// 0611231239
	date, err := time.Parse("0201061504", dateString)
	if err != nil {
		Log.Infof("err converting signature dateString=[%v], err=[%v]\n", dateString, err)
		return dateString, err
	}

	return date.Format(time.RFC3339Nano), nil
}

func EtimsDateTimeToTime(input string) (time.Time, error) {
	// Define layout for parsing the string
	layout := "20060102150405"

	// Parse the input string into a time.Time object
	t, err := time.Parse(layout, input)
	if err != nil {
		fmt.Println("Error parsing date:", err)
		return t, err
	}

	return t, nil
}

// Custom function to format time.Time value to a specified date format
func FormatTime(t time.Time, dateFormat string) string {
	formatedTime := t.Format(dateFormat)
	return formatedTime
}

func ConvertStringDateToTime(dateString string) (time.Time, error) {
	return time.ParseInLocation(layout, dateString, LocKenya)
}
