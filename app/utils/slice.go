package utils

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"compliance-and-risk-management-backend/app/entities"
	"time"

	"github.com/elliotchance/orderedmap"
)

func ChunkInt64s(list []int64, chunkSize int) [][]int64 {

	maxIndex := len(list)
	numChunks := int(math.Ceil(float64(len(list)) / float64(chunkSize)))

	if len(list) == 0 {
		return nil
	}

	chunks := make([][]int64, numChunks)
	for i := 0; i < numChunks; i++ {
		var offSet int
		if i == 0 {
			offSet = 0
		} else {
			offSet = i * chunkSize
		}

		if i == numChunks-1 {
			chunks[i] = list[offSet:maxIndex]
			break
		}
		chunks[i] = list[offSet : offSet+chunkSize]
	}

	return chunks
}

func CommaSeperatedStringToInt64Slice(str string) ([]int64, error) {
	strSlice := strings.Split(str, ",")
	if len(strSlice) == 0 {
		return nil, errors.New("empty string")
	}

	int64Slice := make([]int64, 0)
	for _, a := range strSlice {
		i, err := strconv.Atoi(strings.TrimSpace(a))
		if err != nil {
			return int64Slice, err
		}
		int64Slice = append(int64Slice, int64(i))
	}
	return int64Slice, nil
}

func Int64SliceContains(list []int64, num int64) bool {
	for _, numInList := range list {
		if num == numInList {
			return true
		}
	}

	return false
}

func Int64SliceRemoveItem(list []int64, num int64) []int64 {

	newList := make([]int64, 0, len(list))

	for _, numInList := range list {
		if num != numInList {
			newList = append(newList, numInList)
		}
	}

	return newList
}

func SliceContains(list []string, word string) bool {
	for _, wordInList := range list {
		if word == wordInList {
			return true
		}
	}

	return false
}

func SliceRemove(list []string, word string) []string {

	newList := make([]string, 0, len(list))

	for _, wordInList := range list {
		if word != wordInList {
			newList = append(newList, wordInList)
		}
	}

	return newList
}

func SliceRemoveDuplicates(list []string) []string {
	// maintains the order of elements
	m := orderedmap.NewOrderedMap()
	for _, element := range list {
		trimmedElement := strings.TrimSpace(element)
		if len(trimmedElement) > 0 {
			m.Set(trimmedElement, true)
		}
	}

	uniqueList := make([]string, 0)
	for _, key := range m.Keys() {
		if _, found := m.Get(key); found {
			uniqueList = append(uniqueList, fmt.Sprintf("%v", key))
		}
	}

	return uniqueList
}

func ContainsPhoneNumber(phoneNumbers []*entities.PhoneNumber, phoneNumber *entities.PhoneNumber) bool {
	for i := range phoneNumbers {
		if phoneNumber.CountryCode == phoneNumbers[i].CountryCode && phoneNumber.Number == phoneNumbers[i].Number {
			return true
		}
	}
	return false
}

func Int64SliceCommonValues(a, b []int64) []int64 {
	aMap := make(map[int64]bool)
	shared := make([]int64, 0)
	for _, ka := range a {
		aMap[ka] = true
	}

	for _, kb := range b {
		if _, ok := aMap[kb]; ok {
			shared = append(shared, kb)
		}
	}

	return shared
}

func StringSliceCommonValues(a, b []string) []string {
	aMap := make(map[string]bool)
	shared := make([]string, 0)
	for _, ka := range a {
		aMap[strings.TrimSpace(strings.ToLower(ka))] = true
	}

	for _, kb := range b {
		if _, ok := aMap[strings.TrimSpace(strings.ToLower(kb))]; ok {
			shared = append(shared, kb)
		}
	}

	return shared
}

func Int64SliceRemoveDuplicatesAndZeros(list []int64) []int64 {
	keys := make(map[int64]bool)
	for _, element := range list {
		if element != 0 {
			keys[element] = true
		}
	}

	uniqueList := make([]int64, 0)
	for key, val := range keys {
		if val {
			uniqueList = append(uniqueList, key)
		}
	}

	return uniqueList
}

func ConvertAbrevToWeekday(abrev string) (bool, int) {
	if strings.ToLower(abrev) == "mo" {
		return true, 1
	} else if strings.ToLower(abrev) == "tu" {
		return true, 2
	} else if strings.ToLower(abrev) == "we" {
		return true, 3
	} else if strings.ToLower(abrev) == "th" {
		return true, 4
	} else if strings.ToLower(abrev) == "fr" {
		return true, 5
	} else if strings.ToLower(abrev) == "sa" {
		return true, 6
	} else if strings.ToLower(abrev) == "su" {
		return true, 7
	} else {
		return false, 0
	}
}

func TimeSliceRemoveDuplicates(timeSlice []time.Time) []time.Time {
	allKeys := make(map[time.Time]bool)
	timeList := []time.Time{}
	for _, item := range timeSlice {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			timeList = append(timeList, item)
		}
	}
	return timeList
}

func Int64SliceDifference(slice1, slice2 []int64) []int64 {
	target := make(map[int64]bool)
	for _, valSlice2 := range slice2 {
		target[valSlice2] = true
	}

	difference := make([]int64, 0)
	for _, valSlice1 := range slice1 {
		if _, ok := target[valSlice1]; !ok {
			difference = append(difference, valSlice1)
		}
	}

	return difference
}

func StringSliceDifference(slice1, slice2 []string) []string {
	target := make(map[string]bool)
	for _, valSlice2 := range slice2 {
		valSlice2 := strings.TrimSpace(strings.ToLower(valSlice2))
		target[valSlice2] = true
	}

	difference := make([]string, 0)
	for _, valSlice1 := range slice1 {
		valSlice1 := strings.TrimSpace(strings.ToLower(valSlice1))
		if _, ok := target[valSlice1]; !ok {
			difference = append(difference, valSlice1)
		}
	}

	return difference
}

func RemoveCommonWordsFromSearch(slice []string) []string {
	commonWords := []string{"in", "to", "for", "a", "is"}
	return StringSliceDifference(slice, commonWords)
}

func ArrayToString(arr []int64, delim string) (string, error) {
	if len(arr) <= 0 {
		return "", fmt.Errorf("EMPTY SLICE PARSED")
	} else {
		return strings.Trim(strings.Replace(fmt.Sprint(arr), " ", delim, -1), "[]"), nil
	}
}
