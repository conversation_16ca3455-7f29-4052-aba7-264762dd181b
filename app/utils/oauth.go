package utils

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"net/url"
	"sort"
	"strings"
)

func GenerateTwitterOAuthSignature(
	requestMethod, baseURL string,
	params map[string]string,
	consumerSecret, tokenSecret string) string {

	signatureBaseString := requestMethod + "&" + url.QueryEscape(baseURL) + "&"
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		signatureBaseString += url.QueryEscape(k) + "%3D" + url.QueryEscape(params[k]) + "%26"
	}
	signatureBaseString = strings.TrimRight(signatureBaseString, "%26")
	key := consumerSecret + "&" + tokenSecret
	hashed := hmac.New(sha1.New, []byte(key))
	hashed.Write([]byte(signatureBaseString))
	return base64.StdEncoding.EncodeToString(hashed.Sum(nil))
}
