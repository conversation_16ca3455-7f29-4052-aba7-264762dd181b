package utils

import (
	"math/rand"
	"time"
)

const (
	digits           = "0123456789"
	lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz"
	upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
)

var seededRand = rand.New(rand.NewSource(time.Now().UnixNano()))

func GenerateAPIKeyUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 32)
}

func GenerateAssetUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 32)
}

func GenerateSaleUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 32)
}

func GenerateSlugUUID() string {
	return generateRandomString(lowerCaseLetters, 4)
}

func GenerateGroupInviteCode() string {
	return generateRandomString(lowerCaseLetters+upperCaseLetters, 16)
}

func GenerateOfferingTicketUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 32)
}

func GenerateOfferingCode() string {
	return generateRandomString(digits+lowerCaseLetters, 12)
}

func GenerateOfferingOrderNumber() string {
	return generateRandomString(digits+upperCaseLetters, 8)
}

func GenerateMerchantCartNumber() string {
	return generateRandomString(digits+upperCaseLetters, 8)
}

func GenerateGroupChannelUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 16)
}

func GenerateMerchantUUID() string {
	return generateRandomString(digits+lowerCaseLetters, 12)
}

func GenerateGroupTransactionCode() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 12)
}

func GenerateGroupUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 12)
}

func GenerateTransactionCode() string {
	return generateRandomString(digits+upperCaseLetters, 8)
}

func GenerateVerificationCode() string {
	return generateRandomString(digits, 6)
}

func GenerateUsernameCode() string {
	return generateRandomString(digits, 4)
}

func GenerateURLUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 16)
}

func GenerateUserUUID() string {
	return generateRandomString(digits+lowerCaseLetters+upperCaseLetters, 32)
}

func generateRandomString(charset string, length int) string {
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[seededRand.Intn(len(charset))]
	}
	return string(b)
}
