package entities

import "gopkg.in/guregu/null.v3"

type (
	Category struct {
		SequentialIdentifier
		Name           string        `json:"name"`
		Description    string        `json:"description"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization,omitempty"`
		StoreID        int64         `json:"store_id"`
		Store          *Store        `json:"store,omitempty"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	CategoryList struct {
		Categories []*Category `json:"categories"`
		Pagination *Pagination `json:"pagination"`
	}
)
