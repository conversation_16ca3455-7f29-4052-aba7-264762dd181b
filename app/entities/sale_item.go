package entities

type (
	SaleItem struct {
		SequentialIdentifier
		CreatedBy          int64         `json:"created_by"`
		CurrencyID         int64         `json:"currency_id"`
		Discount           int64         `json:"discount"`
		ItemID             int64         `json:"item_id"`
		Item               *Item         `json:"item"`
		ItemName           string        `json:"item_name"`
		OrganizationID     int64         `json:"organization_id"`
		Organization       *Organization `json:"organization,omitempty"`
		Quantity           float64       `json:"quantity"`
		SaleID             int64         `json:"sale_id"`
		Sale               *Sale         `json:"sale"`
		Scale              int64         `json:"scale"`
		Total              int64         `json:"total"`
		TotalAmountDisplay string        `json:"total_amount_display"`
		Timestamps
	}

	SaleItemList struct {
		SaleItems  []*SaleItem `json:"sale_items"`
		Pagination *Pagination `json:"pagination"`
	}
)
