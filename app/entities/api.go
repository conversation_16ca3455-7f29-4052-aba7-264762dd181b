package entities

// JSONResponse : Success response
// swagger:response jsonResponse
type JSONResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// InvoiceSignature struct
type InvoiceSignature struct {
	InvoiceNumber string `json:"invoice_number"`
	Signature     string `json:"signature"`
}

// BadRequestResponse 400 - bad request - invalid details / wrong data type in request.
// swagger:response badRequestResponse
type BadRequestResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// ForbiddenResponse 403 - you are forbidden from accessing this resource.
// swagger:response forbidddenResponse
type ForbiddenResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// NotFoundResponse 404 - resource not found.
// swagger:response notFoundResponse
type NotFoundResponse struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

// UsersRepsApiReport struct
type UsersRepsApiReport struct {
	UsersCount int64  `json:"users_count"`
	Limit      int64  `json:"limit"`
	Offset     int64  `json:"offset"`
	NumPages   int64  `json:"num_pages"`
	Search     string `json:"search"`
	Users      []User `json:"users"`
}

type DashboardInvoiceChartReport struct {
	Date         string `json:"date"`
	RecordsCount int64  `json:"records_count"`
}
