package entities

type (
	EtimsStock struct {
		SequentialIdentifier
		Tin         string           `json:"tin"`
		BhfID       string           `json:"bhfId"`
		SarNo       int              `json:"sarNo"`
		OrgSarNo    int              `json:"orgSarNo"`
		RegTyCd     string           `json:"regTyCd"`
		CustTin     string           `json:"custTin"`
		CustNm      string           `json:"custNm"`
		CustBhfID   string           `json:"custBhfId"`
		SarTyCd     string           `json:"sarTyCd"`
		OcrnDt      string           `json:"ocrnDt"`
		TotItemCnt  int              `json:"totItemCnt"`
		TotTaxblAmt float64          `json:"totTaxblAmt"`
		TotTaxAmt   float64          `json:"totTaxAmt"`
		TotAmt      float64          `json:"totAmt"`
		Remark      string           `json:"remark"`
		RegrID      string           `json:"regrId"`
		RegrNm      string           `json:"regrNm"`
		ModrNm      string           `json:"modrNm"`
		ModrID      string           `json:"modrId"`
		UUID        string           `json:"uuid"`
		StoreID     int64            `json:"store_id"`
		ItemList    []EtimsStockItem `json:"itemList"`
		Timestamps
	}

	EtimsStockItem struct {
		SequentialIdentifier
		EtimsStockId int64   `json:"etims_stock_id"`
		ItemSeq      int     `json:"itemSeq"`
		ItemCd       string  `json:"itemCd"`
		ItemClsCd    string  `json:"itemClsCd"`
		ItemNm       string  `json:"itemNm"`
		Bcd          string  `json:"bcd"`
		PkgUnitCd    string  `json:"pkgUnitCd"`
		Pkg          float64 `json:"pkg"`
		QtyUnitCd    string  `json:"qtyUnitCd"`
		Qty          float64 `json:"qty"`
		ItemExprDt   string  `json:"itemExprDt"`
		Prc          float64 `json:"prc"`
		SplyAmt      float64 `json:"splyAmt"`
		TotDcAmt     float64 `json:"totDcAmt"`
		TaxblAmt     float64 `json:"taxblAmt"`
		TaxTyCd      string  `json:"taxTyCd"`
		TaxAmt       float64 `json:"taxAmt"`
		TotAmt       float64 `json:"totAmt"`
		UUID         string  `json:"uuid"`
		Timestamps
	}

	EtimsStockList struct {
		EtimsStock []*EtimsStock `json:"etims_stock"`
		Pagination *Pagination   `json:"pagination"`
	}

	EtimsStockItemList struct {
		EtimsStockItems []*EtimsStockItem `json:"etims_stock_items"`
		Pagination      *Pagination       `json:"pagination"`
	}
)
