package entities

import (
	"database/sql/driver"
)

type FileProcessingStatus string

const (
	FileProcessingStatusPending    FileProcessingStatus = "pending"
	FileProcessingStatusProcessed  FileProcessingStatus = "processed"
	FileProcessingStatusProcessing FileProcessingStatus = "processing"
	FileProcessingStatusFailed     FileProcessingStatus = "failed"
)

func (s FileProcessingStatus) IsValid() bool {
	return s == FileProcessingStatusPending || s == FileProcessingStatusProcessed || s == FileProcessingStatusProcessing ||
		s == FileProcessingStatusFailed
}

func (s *FileProcessingStatus) Scan(value interface{}) error {
	*s = FileProcessingStatus(string(value.([]uint8)))
	return nil
}

func (s FileProcessingStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s FileProcessingStatus) String() string {
	return string(s)
}
