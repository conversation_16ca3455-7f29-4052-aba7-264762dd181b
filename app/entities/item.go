package entities

import "gopkg.in/guregu/null.v3"

type (
	Item struct {
		SequentialIdentifier
		AvatarURL      string        `json:"avatar_url"`
		CostPrice      float64       `json:"cost_price"`
		Description    string        `json:"description"`
		ETIMSCode      string        `json:"etims_code"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization,omitempty"`
		Name           string        `json:"name"`
		RetailPrice    float64       `json:"retail_price"`
		CategoryID     int64         `json:"category_id"`
		StoreID        int64         `json:"store_id"`
		TaxPercent     float64       `json:"tax_percent"`
		UUID           string        `json:"uuid"`
		CreatedBy      int64         `json:"created_by"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	ItemList struct {
		Items      []*Item     `json:"items"`
		Pagination *Pagination `json:"pagination"`
	}
)
