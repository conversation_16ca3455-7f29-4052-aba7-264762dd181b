package entities

import "time"

type (
	APIKey struct {
		SequentialIdentifier
		APIKey         string     `json:"api_key"`
		DeletedAt      *time.Time `json:"-"`
		Description    string     `json:"description"`
		LastUsedAt     *time.Time `json:"last_used_at"`
		OrganizationID int64      `json:"organization_id"`
		StoreID        *int64     `json:"store_id"`
		Store          *Store     `json:"store"`
		UserID         int64      `json:"user_id"`
		Timestamps
	}

	APIKeyList struct {
		APIKeys    []*APIKey   `json:"api_keys"`
		Pagination *Pagination `json:"pagination"`
	}
)
