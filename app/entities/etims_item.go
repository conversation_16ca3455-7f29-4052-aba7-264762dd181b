package entities

type (
	EtimsItem struct {
		SequentialIdentifier
		AddInfo     string  `json:"addInfo"`     // Addional Information
		Bcd         string  `json:"bcd"`         // Barcode
		BhfID       string  `json:"bhfId"`       // Branch Number
		BtchNo      int     `json:"btchNo"`      // Batch Number
		DcAmt       float64 `json:"dcAmt"`       // Discount Amount
		DcRt        float64 `json:"dcRt"`        // Discount Rate
		DftPrc      float64 `json:"dftPrc"`      // Default Unit Price
		GrpPrcL1    float64 `json:"grpPrcL1"`    // Group1 Unit Price
		GrpPrcL2    float64 `json:"grpPrcL2"`    // Group2 Unit Price
		GrpPrcL3    float64 `json:"grpPrcL3"`    // Group3 Unit Price
		GrpPrcL4    float64 `json:"grpPrcL4"`    // Group4 Unit Price
		GrpPrcL5    float64 `json:"grpPrcL5"`    // Group5 Unit Price
		IsrcAplcbYn string  `json:"isrcAplcbYn"` // Insurance Applicable (Y/N)
		ItemCd      string  `json:"itemCd"`      // Item Code
		ItemClsCd   string  `json:"itemClsCd"`   // Item Classification Code
		ItemTyCd    string  `json:"itemTyCd"`    // Item Type Code
		ItemNm      string  `json:"itemNm"`      // Item Name
		ItemStdNm   string  `json:"itemStdNm"`   // Item Standard Name
		ModrID      string  `json:"modrId"`      // Modifier ID
		ModrNm      string  `json:"modrNm"`      // Modifier Name
		OrgnNatCd   string  `json:"orgnNatCd"`   // Origin Place Code (Nation)
		PkgUnitCd   string  `json:"pkgUnitCd"`   // Packaging Unit Code
		QtyUnitCd   string  `json:"qtyUnitCd"`   // Quantity Unit Code
		RegrID      string  `json:"regrId"`      // Registrant ID
		RegrNm      string  `json:"regrNm"`      // Registrant Name
		SftyQty     float64 `json:"sftyQty"`     // Safety Quantity
		SplyAmt     float64 `json:"splyAmt"`
		TaxTyCd     string  `json:"taxTyCd"` // Tax Type code
		Tin         string  `json:"tin"`     // PIN Number
		UseYn       string  `json:"useYn"`   // Yes / No
		UUID        string  `json:"uuid"`
		Timestamps
	}

	EtimsItemList struct {
		EtimsItems []*EtimsItem `json:"etims_items"`
		Pagination *Pagination  `json:"pagination"`
	}
)
