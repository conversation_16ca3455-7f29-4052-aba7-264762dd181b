package entities

import (
	"database/sql/driver"
)

type ClientIdentifier string

const (
	AndroidClientIdentifier ClientIdentifier = "android"
	WebClientIdentifier     ClientIdentifier = "web"
)

// Scan implements the Scanner interface.
func (s *ClientIdentifier) Scan(value interface{}) error {
	*s = ClientIdentifier(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s ClientIdentifier) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s ClientIdentifier) String() string {
	return string(s)
}
