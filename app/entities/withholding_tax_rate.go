package entities

import "gopkg.in/guregu/null.v3"

type (
	WithholdingTaxRate struct {
		SequentialIdentifier
		CreatedBy          int64        `json:"created_by"`
		Description        string       `json:"description"`
		Name               string       `json:"name"`
		TaxCategoryID      int64        `json:"tax_category_id"`
		TaxCategory        *TaxCategory `json:"tax_category"`
		WithholdingTaxRate float64      `json:"withholding_tax_rate"`
		DeletedAt          null.Time    `db:"deleted_at" json:"-"`
		Timestamps
	}

	WithholdingTaxRateList struct {
		WithholdingTaxRates []*WithholdingTaxRate `json:"withholding_tax_rates"`
		Pagination          *Pagination           `json:"pagination"`
	}
)
