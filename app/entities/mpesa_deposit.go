package entities

type MPesaDeposit struct {
	SequentialIdentifier
	BillRefNumber     string       `json:"bill_ref_number"`
	BusinessShortCode string       `json:"business_short_code"`
	FirstName         string       `json:"first_name"`
	InvoiceNumber     string       `json:"invoice_number"`
	LastName          string       `json:"last_name"`
	MiddleName        string       `json:"middle_name"`
	MSISDN            string       `json:"msisdn"`
	OrgAccountBalance string       `json:"org_account_balance"`
	OrganizationID    int64        `json:"organization_id"`
	PhoneNumberID     int64        `json:"phone_number_id"`
	PhoneNumber       *PhoneNumber `json:"phone_number"`
	ThirdPartyTransID string       `json:"third_party_trans_id"`
	TransactionAmount string       `json:"transaction_amount"`
	TransactionID     string       `json:"transaction_id"`
	TransactionTime   string       `json:"transaction_time"`
	TransactionType   string       `json:"transaction_type"`
	Timestamps
}

type MPesaDepositList struct {
	MPesaDeposits []*MPesaDeposit `json:"mpesa_deposits"`
	Pagination    *Pagination     `json:"pagination"`
}
