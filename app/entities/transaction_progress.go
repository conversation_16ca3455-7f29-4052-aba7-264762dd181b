package entities

import "database/sql/driver"

type TransactionProgress string

const (
	TransactionProgressWaitForApproval     TransactionProgress = "01"
	TransactionProgressApproved            TransactionProgress = "02"
	TransactionProgressCreditNoteRequested TransactionProgress = "03"
	TransactionProgressCancelled           TransactionProgress = "04"
	TransactionProgressCreditNoteGenerated TransactionProgress = "05"
	TransactionProgressTransferred         TransactionProgress = "06"
)

// Scan implements the Scanner interface.
func (s *TransactionProgress) Scan(value interface{}) error {
	*s = TransactionProgress(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s TransactionProgress) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s TransactionProgress) String() string {
	return string(s)
}
