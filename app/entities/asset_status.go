package entities

import "database/sql/driver"

const (
	AssetStatusActive  AssetStatus = "active"
	AssetStatusFailed  AssetStatus = "failed"
	AssetStatusPending AssetStatus = "pending"
)

type AssetStatus string

func (s AssetStatus) IsActive() bool {
	return s == AssetStatusActive
}

func (s *AssetStatus) Scan(value interface{}) error {
	*s = AssetStatus(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s AssetStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s AssetStatus) String() string {
	return string(s)
}
