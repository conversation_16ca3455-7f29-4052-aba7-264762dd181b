package entities

import "gopkg.in/guregu/null.v3"

type (
	UserIncome struct {
		SequentialIdentifier
		TaxCategoryID        int64            `json:"tax_category_id"`
		TaxCategory          *TaxCategory     `json:"tax_category"`
		UserID               int64            `json:"user_id"`
		User                 *User            `json:"user"`
		MerchantName         string           `json:"merchant_name"`
		MerchantPIN          string           `json:"merchant_pin"`
		Amount               float64          `json:"amount"`
		Currency             string           `json:"currency"`
		Description          string           `json:"description"`
		PaymentFrequency     PaymentFrequency `json:"payment_frequency"`
		StartDate            null.Time        `json:"start_date"`
		EndDate              null.Time        `json:"end_date"`
		WithholdingTaxRate   float64          `json:"withholding_tax_rate"`
		WithholdingTaxAmount float64          `json:"withholding_tax_amount"`
		DeletedAt            null.Time        `db:"deleted_at" json:"-"`
		Timestamps
	}

	UserIncomeList struct {
		UserIncomes []*UserIncome `json:"user_incomes"`
		Pagination  *Pagination   `json:"pagination"`
	}
)
