package entities

import "gopkg.in/guregu/null.v3"

type (
	Supplier struct {
		SequentialIdentifier
		Name           string        `json:"name"`
		Contact<PERSON>erson  string        `json:"contact_person"`
		Email          string        `json:"email"`
		PhoneNumber    string        `json:"phone_number"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization,omitempty"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	SupplierList struct {
		Suppliers  []*Supplier `json:"customers"`
		Pagination *Pagination `json:"pagination"`
	}
)
