package entities

type (
	MPesaAccountBalanceResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}

	MPesaAuthentication struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   string `json:"expires_in"`
	}

	MPesaPhoneNumberHashDecoderResponse struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	MPesaRegisterURLErrorResponse struct {
		RequestId    string `json:"requestId"`
		ErrorCode    string `json:"errorCode"`
		ErrorMessage string `json:"errorMessage"`
	}

	MPesaRegisterURLResponse struct {
		OriginatorCoversationID string `json:"OriginatorCoversationID"`
		ResponseCode            string `json:"ResponseCode"`
		ResponseDescription     string `json:"ResponseDescription"`
	}

	MPesaSTKPushRequestForm struct {
		Amount                 float64 `binding:"required" json:"amount"`
		PhoneNumber            string  `binding:"required" json:"phone_number"`
		CallBackURL            string  `binding:"required" json:"call_back_url"`
		AccountReference       string  `binding:"required" json:"account_reference"`
		TransactionDescription string  `binding:"required" json:"transaction_description"`
	}

	MPesaSTKPushResponse struct {
		MerchantRequestID   string `json:"MerchantRequestID"`
		CheckoutRequestID   string `json:"CheckoutRequestID"`
		ResponseCode        string `json:"ResponseCode"`
		ResponseDescription string `json:"ResponseDescription"`
		CustomerMessage     string `json:"CustomerMessage"`
	}

	MPesaReversalResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}

	MPesaTransactionStatusResponse struct {
		OriginatorConversationID string `json:"OriginatorConversationID"`
		ConversationID           string `json:"ConversationID"`
		ResponseCode             string `json:"ResponseCode"`
		ResponseDescription      string `json:"ResponseDescription"`
	}
)
