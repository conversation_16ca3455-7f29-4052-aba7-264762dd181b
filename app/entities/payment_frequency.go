package entities

import (
	"database/sql/driver"
)

type PaymentFrequency string

const (
	PaymentFrequencyMonthly PaymentFrequency = "monthly"
	PaymentFrequencyYearly  PaymentFrequency = "yearly"
)

func (s PaymentFrequency) IsValid() bool {
	return s == PaymentFrequencyMonthly || s == PaymentFrequencyYearly
}

// Scan implements the Scanner interface.
func (s *PaymentFrequency) Scan(value interface{}) error {
	*s = PaymentFrequency(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s PaymentFrequency) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s PaymentFrequency) String() string {
	return string(s)
}
