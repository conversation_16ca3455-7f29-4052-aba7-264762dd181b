package entities

type (
	CustomerIDType struct {
		SequentialIdentifier
		CustomerIDTypeTraID string `json:"customer_id_type_tra_id"`
		CustomerIDType      string `json:"customer_id_type"`
		CustomerID          string `json:"-"`
		Description         string `json:"description"`
		IsDefault           bool   `json:"is_default"`
		TaxCode             string `json:"tax_code"`
		Timestamps
	}

	CustomerIDTypeList struct {
		CustomerIDTypes []*CustomerIDType `json:"customer_id_types"`
		Pagination      Pagination        `json:"pagination"`
	}
)
