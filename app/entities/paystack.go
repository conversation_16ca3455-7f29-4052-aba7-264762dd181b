package entities

import "time"

type (
	PaystackAuthorization struct {
		AuthorizationCode string `json:"authorization_code"`
		Bin               string `json:"bin"`
		Last4             string `json:"last4"`
		ExpMonth          string `json:"exp_month"`
		ExpYear           string `json:"exp_year"`
		Channel           string `json:"channel"`
		CardType          string `json:"card_type"`
		Bank              string `json:"bank"`
		CountryCode       string `json:"country_code"`
		Brand             string `json:"brand"`
		Reusable          bool   `json:"reusable"`
		Signature         string `json:"signature"`
		AccountName       string `json:"account_name"`
	}

	PaystackCustomer struct {
		ID                       int64       `json:"id"`
		FirstName                interface{} `json:"first_name"`
		Identified               bool        `json:"identified"`
		Integration              int64       `json:"integration"`
		LastName                 interface{} `json:"last_name"`
		Email                    string      `json:"email"`
		CustomerCode             string      `json:"customer_code"`
		Phone                    interface{} `json:"phone"`
		Metadata                 interface{} `json:"metadata,omitempty"`
		RiskAction               string      `json:"risk_action"`
		InternationalFormatPhone interface{} `json:"international_format_phone"`
	}

	PaystackLog struct {
		StartTime int64         `json:"start_time"`
		TimeSpent int           `json:"time_spent"`
		Attempts  int           `json:"attempts"`
		Errors    int           `json:"errors"`
		Success   bool          `json:"success"`
		Mobile    bool          `json:"mobile"`
		Input     []interface{} `json:"input"`
		History   []struct {
			Type    string `json:"type"`
			Message string `json:"message"`
			Time    int    `json:"time"`
		} `json:"history"`
	}

	PaystackMeta struct {
		Total     int `json:"total"`
		Skipped   int `json:"skipped"`
		PerPage   int `json:"perPage"`
		Page      int `json:"page"`
		PageCount int `json:"pageCount"`
	}

	PaystackPlan struct {
		Amount   string          `json:"amount"`
		Currency string          `json:"currency"`
		Interval PaymentInterval `json:"interval"` // daily, weekly, monthly,quarterly, biannually, annually.
		Name     string          `json:"name"`
	}

	PaystackPlansResponse struct {
		Status  bool           `json:"status"`
		Message string         `json:"message"`
		Data    []PaystackData `json:"data"`
		Meta    PaystackMeta   `json:"meta"`
	}

	PaystackResponse struct {
		Status  bool         `json:"status"`
		Message string       `json:"message"`
		Data    PaystackData `json:"data"`
	}

	PaystackSubscriptionList struct {
		Status  bool           `json:"status"`
		Message string         `json:"message"`
		Data    []PaystackData `json:"data"`
		Meta    PaystackMeta   `json:"meta"`
	}

	PaystackSubscription struct {
		Customer string `json:"customer"`
		Plan     string `json:"plan"`
	}

	PaystackTransaction struct {
		Amount      string `json:"amount"`
		Email       string `json:"email"`
		CallbackURL string `json:"callback_url"`
	}

	PaystackData struct {
		ID                        *int64                 `json:"id"`
		AuthorizationURL          *string                `json:"authorization_url,omitempty"`
		AccessCode                *string                `json:"access_code,omitempty"`
		ActiveSubscriptions       *int64                 `json:"active_subscriptions,omitempty"`
		Amount                    *int64                 `json:"amount,omitempty"`
		Authorization             *PaystackAuthorization `json:"authorization,omitempty"`
		Channel                   *string                `json:"channel,omitempty"`
		CustomerCode              *string                `json:"customer_code,omitempty"`
		Currency                  *string                `json:"currency,omitempty"`
		Customer                  *PaystackCustomer      `json:"customer,omitempty"`
		CreatedAt                 *time.Time             `json:"created_at,omitempty"`
		CreatedAtStr              *time.Time             `json:"createdAt,omitempty"`
		CronExpression            *string                `json:"cron_expression,omitempty"`
		Description               *interface{}           `json:"description,omitempty"`
		Domain                    *string                `json:"domain,omitempty"`
		EasyCronID                *string                `json:"easy_cron_id,omitempty"`
		EmailToken                *string                `json:"email_token,omitempty"`
		Fees                      *int                   `json:"fees,omitempty"`
		FeesBreakdown             *interface{}           `json:"fees_breakdown,omitempty"`
		FeesSplit                 *interface{}           `json:"fees_split,omitempty"`
		GatewayResponse           *string                `json:"gateway_response,omitempty"`
		HostedPage                *bool                  `json:"hosted_page,omitempty"`
		HostedPageURL             *interface{}           `json:"hosted_page_url,omitempty"`
		HostedPageSummary         *interface{}           `json:"hosted_page_summary,omitempty"`
		Identified                *bool                  `json:"identified,omitempty"`
		Integration               *int64                 `json:"integration,omitempty"`
		Interval                  *string                `json:"interval,omitempty"`
		InvoiceLimit              *int                   `json:"invoice_limit,omitempty"`
		IPAddress                 *string                `json:"ip_address,omitempty"`
		IsArchived                *bool                  `json:"is_archived,omitempty"`
		IsDeleted                 *bool                  `json:"is_deleted,omitempty"`
		Log                       *PaystackLog           `json:"log,omitempty"`
		Message                   *interface{}           `json:"message,omitempty"`
		Metadata                  *string                `json:"metadata,omitempty"`
		Migrate                   *bool                  `json:"migrate,omitempty"`
		Name                      *string                `json:"name,omitempty"`
		NextPaymentDate           *time.Time             `json:"next_payment_date,omitempty"`
		OpenInvoice               *string                `json:"open_invoice,omitempty"`
		OrderID                   *interface{}           `json:"order_id,omitempty"`
		Pages                     []*interface{}         `json:"pages,omitempty"`
		PaidAt                    *time.Time             `json:"paid_at,omitempty"`
		PaidAtStr                 *time.Time             `json:"paidAt,omitempty"`
		Plan                      *interface{}           `json:"plan,omitempty"`
		PlanCode                  *string                `json:"plan_code,omitempty"`
		PlanObject                *struct{}              `json:"plan_object,omitempty"`
		PosTransactionData        *interface{}           `json:"pos_transaction_data,omitempty"`
		Quantity                  *int                   `json:"quantity,omitempty"`
		ReceiptNumber             *interface{}           `json:"receipt_number,omitempty"`
		Reference                 *string                `json:"reference,omitempty"`
		RequestedAmount           *int                   `json:"requested_amount,omitempty"`
		SendInvoices              *bool                  `json:"send_invoices,omitempty"`
		SendSms                   *bool                  `json:"send_sms,omitempty"`
		Source                    *interface{}           `json:"source,omitempty"`
		Split                     *struct{}              `json:"split,omitempty"`
		Start                     *int64                 `json:"start,omitempty"`
		Status                    *string                `json:"status,omitempty"`
		SubscriptionCode          *string                `json:"subscription_code,omitempty"`
		Subscriptions             []*interface{}         `json:"subscriptions,omitempty"`
		Subaccount                *struct{}              `json:"subaccount,omitempty"`
		TotalSubscriptions        *int64                 `json:"total_subscriptions,omitempty"`
		TotalSubscriptionsRevenue *int64                 `json:"total_subscriptions_revenue,omitempty"`
		TransactionDate           *time.Time             `json:"transaction_date,omitempty"`
		UpdatedAt                 *time.Time             `json:"updated_at,omitempty"`
	}

	PaystackPaymentValidationResponse struct {
		Status  bool         `json:"status"`
		Message string       `json:"message"`
		Data    PaystackData `json:"data"`
	}
)
