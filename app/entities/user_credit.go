package entities

import "gopkg.in/guregu/null.v3"

type (
	UserCredit struct {
		SequentialIdentifier
		UserID        int64     `json:"user_id"`
		CreditBalance float64   `json:"credit_balance"`
		DeletedAt     null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	UserCreditTopUpAPIResponse struct {
		AccountNumber string  `json:"account_number"`
		Amount        float64 `json:"amount"`
		CreditBalance float64 `json:"credit_balance"`
		PhoneNumber   string  `json:"phone_number"`
	}

	UserCreditList struct {
		UserCredits []*UserCredit `json:"user_credits"`
		Pagination  *Pagination   `json:"pagination"`
	}
)
