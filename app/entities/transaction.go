package entities

import "gopkg.in/guregu/null.v3"

type (
	Transaction struct {
		SequentialIdentifier
		UserID          int64     `json:"user_id"`
		Amount          float64   `json:"amount"`
		Description     string    `json:"description"`
		Status          string    `json:"status"`
		TransactionType string    `json:"transaction_type"`
		DeletedAt       null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	TransactionList struct {
		Transactions []*Transaction `json:"transactions"`
		Pagination   *Pagination    `json:"pagination"`
	}
)
