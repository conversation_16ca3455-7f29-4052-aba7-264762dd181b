package entities

import "gopkg.in/guregu/null.v3"

type (
	TaxCategory struct {
		SequentialIdentifier
		ApplicableTaxType   *TaxType    `json:"applicable_tax_type"`
		ApplicableTaxTypeID int64       `json:"applicable_tax_type_id"`
		Description         string      `json:"description"`
		DueDate             null.String `json:"due_date"`
		HasWithholdingTax   bool        `json:"has_withholding_tax"`
		IncomeSource        string      `json:"income_source"`
		TaxRate             float64     `json:"tax_rate"`
		DeletedAt           null.Time   `db:"deleted_at" json:"-"`
		Timestamps
	}

	TaxCategoryList struct {
		TaxCategories []*TaxCategory `json:"tax_categories"`
		Pagination    *Pagination    `json:"pagination"`
	}
)
