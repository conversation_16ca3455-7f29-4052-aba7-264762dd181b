package entities

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type Session struct {
	SequentialIdentifier
	ApplicationID         int64           `json:"application_id"`
	ApplicationType       ApplicationType `json:"application_type"`
	DeactivatedAt         null.Time       `json:"deactivated_at"`
	IPAddress             string          `json:"ip_address"`
	LastRefreshedAt       time.Time       `json:"last_refreshed_at"`
	MobileDeviceSessionID null.Int        `json:"-"`
	UserAgent             string          `json:"user_agent"`
	UserID                int64           `json:"user_id"`
	Timestamps
}

type FullSession struct {
	SequentialIdentifier
	ApplicationID         int64            `json:"application_id"`
	ApplicationType       ApplicationType  `json:"application_type"`
	ClientIdentifier      ClientIdentifier `json:"client_identifier"`
	DeactivatedAt         null.Time        `json:"deactivated_at"`
	IPAddress             string           `json:"ip_address"`
	LastRefreshedAt       time.Time        `json:"last_refreshed_at"`
	MobileDeviceSessionID null.Int         `json:"-"`
	UserAgent             string           `json:"user_agent"`
	UserID                int64            `json:"user_id"`
	UserStatus            UserStatus       `json:"user_status"`
	Timestamps
}

type SessionList struct {
	Sessions   []*Session  `json:"session"`
	Pagination *Pagination `json:"pagination"`
}
