package entities

import (
	"database/sql/driver"
)

type ApplicationType string

const (
	ApplicationTypeAdmin ApplicationType = "admin"
	ApplicationTypeUser  ApplicationType = "user"
)

func (s ApplicationType) IsValid() bool {
	return s == ApplicationTypeAdmin ||
		s == ApplicationTypeUser
}

// Scan implements the Scanner interface.
func (s *ApplicationType) Scan(value interface{}) error {
	*s = ApplicationType(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s ApplicationType) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s ApplicationType) String() string {
	return string(s)
}
