package entities

type EtimsNotice struct {
	SequentialIdentifier
	Content              string `json:"content"`
	DetailURL            string `json:"detail_url"`             // Etims DtlURL
	NoticeNumber         int64  `json:"notice_number"`          // Etims NoticeNo
	RegistrationDateTime string `json:"registration_date_time"` // Etims regDt
	RegistrationName     string `json:"registration_name"`      // Etims RegrNm
	Title                string `json:"title"`
	Timestamps
}

type EtimsNoticeList struct {
	EtimsNotices []*EtimsNotice `json:"etims_notices"`
	Pagination   *Pagination    `json:"pagination"`
}
