package entities

import (
	"database/sql/driver"
)

type EtimsTaxType string

const (
	EtimsTaxTypeA EtimsTaxType = "A" // A-Exempt
	EtimsTaxTypeB EtimsTaxType = "B" // B-16.00%
	EtimsTaxTypeC EtimsTaxType = "C" // C-0%
	EtimsTaxTypeD EtimsTaxType = "D" // D- Non-VAT
	EtimsTaxTypeE EtimsTaxType = "E" // E-8%
)

func (s EtimsTaxType) IsValid() bool {
	return s == EtimsTaxTypeA || s == EtimsTaxTypeB || s == EtimsTaxTypeC ||
		s == EtimsTaxTypeD || s == EtimsTaxTypeE
}

// Scan implements the Scanner interface.
func (s *EtimsTaxType) Scan(value interface{}) error {
	*s = EtimsTaxType(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s EtimsTaxType) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s EtimsTaxType) String() string {
	return string(s)
}
