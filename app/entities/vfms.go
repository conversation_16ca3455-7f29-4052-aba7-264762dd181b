package entities

import "time"

type (
	VFMSItem struct {
		SequentialIdentifier
		Name        string `json:"name"`
		ID          int    `json:"id"`
		UnitMeasure string `json:"unitMeasure"`
		IsTaxable   bool   `json:"isTaxable"`
		VFMSID      int64  `json:"vfms_id"`
		Timestamps
	}

	VFMSItemList struct {
		VFMSItems  []*VFMSItem `json:"vfms_items"`
		Pagination *Pagination `json:"pagination"`
	}

	VFMSSalesResponse struct {
		SalesCustomer string `json:"salesCustomer"`
		CustomerPhone string `json:"customerPhone"`
		SalesCurrency string `json:"salesCurrency"`
		SalesItems    []struct {
			ItemID      int     `json:"itemId"`
			Description string  `json:"description"`
			Discount    float64 `json:"discount"`
			ItemName    string  `json:"itemName"`
			Price       float64 `json:"price"`
			Quantity    float64 `json:"quantity"`
		} `json:"salesItems"`
		ReceitpAmount       float64   `json:"receitpAmount"`
		ReceiptNumber       string    `json:"receiptNumber"`
		TaxExclussive       float64   `json:"taxExclussive"`
		TaxAmount           float64   `json:"taxAmount"`
		BusinessName        string    `json:"businessName"`
		TinNumber           string    `json:"tinNumber"`
		VrnNumber           string    `json:"vrnNumber"`
		Street              string    `json:"street"`
		IssueDate           time.Time `json:"issueDate"`
		ReferenceNumber     string    `json:"referenceNumber"`
		ResponseNumber      string    `json:"responseNumber"`
		GetDailyTransaction int       `json:"getDailyTransaction"`
		GetAllTransaction   int       `json:"getAllTransaction"`
		Urn                 string    `json:"urn"`
		Type                string    `json:"type"`
		Znumber             string    `json:"znumber"`
	}
)
