package entities

import "gopkg.in/guregu/null.v3"

type (
	Customer struct {
		SequentialIdentifier
		FirstName      string        `json:"first_name"`
		LastName       string        `json:"last_name"`
		Email          string        `json:"email"`
		PhoneNumber    string        `json:"phone_number"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization,omitempty"`
		StoreID        int64         `json:"store_id"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	CustomerList struct {
		Customers  []*Customer `json:"customers"`
		Pagination *Pagination `json:"pagination"`
	}
)
