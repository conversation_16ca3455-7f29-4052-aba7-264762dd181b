package entities

type ContentType string

const (
	ContentTypeAVI  ContentType = "video/x-msvideo"
	ContentTypeCSV  ContentType = "text/csv"
	ContentTypeAPK  ContentType = "application/vnd.android.package-archive"
	ContentTypeDOC  ContentType = "application/msword"
	ContentTypeDOCX ContentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	ContentTypeGIF  ContentType = "image/gif"
	ContentTypeHTML ContentType = "text/html"
	ContentTypeJPEG ContentType = "image/jpeg"
	ContentTypeSVG  ContentType = "image/svg+xml"
	ContentTypeJS   ContentType = "application/x-javascript"
	ContentTypeJSON ContentType = "application/json"
	ContentTypeM4A  ContentType = "audio/m4a"
	ContentTypeMOV  ContentType = "video/quicktime"
	ContentTypeMP4  ContentType = "video/mp4"
	ContentTypeMP3  ContentType = "audio/mpeg"
	ContentTypeMPEG ContentType = "video/mpeg"
	ContentType3GPP ContentType = "video/3gpp"
	ContentTypeOGG  ContentType = "video/ogg"
	ContentTypeWEBP ContentType = "image/webp"
	ContentTypeWEBM ContentType = "video/webm"
	ContentTypeFLV  ContentType = "video/x-flv"
	ContentTypeM4V  ContentType = "video/x-m4v"
	ContentTypeASF  ContentType = "video/ms-asf"
	ContentTypeWMV  ContentType = "video/x-ms-wmv"
	ContentTypePDF  ContentType = "application/pdf"
	ContentTypePNG  ContentType = "image/png"
	ContentTypePPT  ContentType = "application/vnd.ms-powerpoint"
	ContentTypePPTX ContentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
	ContentTypeTSV  ContentType = "text/tab-separated-values"
	ContentTypeTXT  ContentType = "text/plain"
	ContentTypeWAV  ContentType = "audio/wav"
	ContentTypeXLS  ContentType = "application/vnd.ms-excel"
	ContentTypeXLSX ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
)

var (
	ImageContentTypes           = []ContentType{ContentTypeGIF, ContentTypeJPEG, ContentTypePNG, ContentTypeWEBP}
	AllowedMerchantContentTypes = []ContentType{ContentTypeGIF, ContentTypeJPEG, ContentTypePNG, ContentTypeWEBP,
		ContentTypeM4A, ContentTypeMOV, ContentTypeMP3, ContentTypeMPEG, ContentTypeWAV, ContentTypeAVI,
		ContentTypeMP4, ContentType3GPP, ContentTypeOGG, ContentTypeWEBM, ContentTypeFLV, ContentTypeM4V,
		ContentTypeASF, ContentTypeWMV}

	AllowedGroupContentTypes = []ContentType{
		ContentTypeGIF, ContentTypeJPEG, ContentTypePNG, ContentTypeSVG, ContentTypeMOV,
		ContentTypeMPEG, ContentTypeAVI, ContentTypeMP4, ContentType3GPP, ContentTypeOGG,
		ContentTypeWEBM, ContentTypeFLV, ContentTypeM4V, ContentTypeASF, ContentTypeWMV,
		ContentTypePDF, ContentTypeCSV, ContentTypeXLSX, ContentTypeXLS, ContentTypeDOC,
		ContentTypeDOCX, ContentTypePPTX, ContentTypePPT, ContentTypeAPK, ContentTypeWEBP,
	}
)

func (t ContentType) String() string {
	return string(t)
}
