package entities

import "time"

type (
	ExcelFileUpload struct {
		SequentialIdentifier
		UUID           string               `json:"uuid"`
		DeletedAt      *time.Time           `json:"-"`
		FileName       string               `json:"file_name"`
		CreatedBy      int64                `json:"created_by"`
		OrganizationID int64                `json:"organization_id"`
		RecordsCount   int64                `json:"records_count"`
		StoreID        int64                `json:"store_id"`
		Store          *Store               `json:"store"`
		Status         FileProcessingStatus `json:"status"`
		Timestamps
	}

	ExcelFileUploadList struct {
		ExcelFileUploads []*ExcelFileUpload `json:"excel_file_uploads"`
		Pagination       *Pagination        `json:"pagination"`
	}
)
