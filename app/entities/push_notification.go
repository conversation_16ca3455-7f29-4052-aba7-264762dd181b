package entities

type PushNotification struct {
	SequentialIdentifier
	ApplicationType   ApplicationType        `json:"application_type"`
	AppNotificationID *int64                 `json:"app_notification_id"`
	Notification      string                 `json:"notification"`
	SessionID         int64                  `json:"session_id"`
	Title             string                 `json:"title"`
	Icon              string                 `json:"icon"`
	Link              string                 `json:"link"`
	Status            PushNotificationStatus `json:"status"`
	Timestamps
}

type PushNotificationList struct {
	PushNotifications []*PushNotification `json:"push_notifications"`
	Pagination        *Pagination         `json:"pagination"`
}
