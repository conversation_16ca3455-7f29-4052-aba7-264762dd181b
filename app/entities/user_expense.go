package entities

import "gopkg.in/guregu/null.v3"

type (
	UserExpense struct {
		SequentialIdentifier
		ExpenseTypeID int64        `json:"expense_type_id"`
		ExpenseType   *ExpenseType `json:"expense_type"`
		UserID        int64        `json:"user_id"`
		User          *User        `json:"user"`
		MerchantName  string       `json:"merchant_name"`
		MerchantPIN   string       `json:"merchant_pin"`
		Amount        float64      `json:"amount"`
		Currency      string       `json:"currency"`
		Description   string       `json:"description"`
		DeletedAt     null.Time    `db:"deleted_at" json:"-"`
		Timestamps
	}

	UserExpenseList struct {
		UserExpenses []*UserExpense `json:"user_expenses"`
		Pagination   *Pagination    `json:"pagination"`
	}
)
