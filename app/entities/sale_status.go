package entities

import (
	"database/sql/driver"
)

type SaleStatus string

const (
	SaleStatusPending   SaleStatus = "pending"
	SaleStatusComplete  SaleStatus = "complete"
	SaleStatusCancelled SaleStatus = "cancelled"
)

func (s SaleStatus) IsValid() bool {
	return s == SaleStatusPending || s == SaleStatusComplete || s == SaleStatusCancelled
}

// Scan implements the Scanner interface.
func (s *SaleStatus) Scan(value interface{}) error {
	*s = SaleStatus(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s SaleStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s SaleStatus) String() string {
	return string(s)
}
