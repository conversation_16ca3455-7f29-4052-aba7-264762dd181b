package entities

import "database/sql/driver"

type PushNotificationStatus string

const (
	PushNotificationStatusRead   PushNotificationStatus = "read"
	PushNotificationStatusUnread PushNotificationStatus = "unread"
)

func (s PushNotificationStatus) IsValid() bool {
	return s == PushNotificationStatusRead || s == PushNotificationStatusUnread
}

func (s PushNotificationStatus) IsRead() bool {
	return s == PushNotificationStatusRead
}

func (s *PushNotificationStatus) Scan(value interface{}) error {
	*s = PushNotificationStatus(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s PushNotificationStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s PushNotificationStatus) String() string {
	return string(s)
}
