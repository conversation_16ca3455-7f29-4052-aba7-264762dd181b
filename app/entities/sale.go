package entities

type (
	Sale struct {
		SequentialIdentifier
		AmountTendered     int64         `json:"amount_tendered"`
		Change             int64         `json:"change"`
		CreatedBy          int64         `json:"created_by"`
		CurrencyID         int64         `json:"currency_id"`
		Currency           *Currency     `json:"currency"`
		CustomerID         *int64        `json:"customer_id"`
		OrganizationID     int64         `json:"organization_id"`
		Organization       *Organization `json:"organization"`
		PaymentMethod      string        `json:"payment_method"`
		SaleItems          []*SaleItem   `json:"sale_items"`
		Scale              int64         `json:"scale"`
		Status             string        `json:"status"`
		StoreID            int64         `json:"store_id"`
		Store              *Store        `json:"store"`
		Subtotal           int64         `json:"subtotal"`
		Total              int64         `json:"total"`
		TotalAmountDisplay string        `json:"total_amount_display"`
		UUID               string        `json:"uuid"`
		Vat                int64         `json:"vat,omitempty"`
		Timestamps
	}

	SaleList struct {
		Sales      []*Sale     `json:"sales"`
		Pagination *Pagination `json:"pagination"`
	}
)
