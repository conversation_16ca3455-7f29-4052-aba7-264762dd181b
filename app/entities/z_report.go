package entities

type (
	ZReport struct {
		SequentialIdentifier
		FileName       string  `json:"file_name"`
		InvoiceCount   int64   `json:"invoice_count"`
		IsSynced       bool    `json:"is_synced"`
		NetAmount      float64 `json:"net_amount"`
		PmtAmount      float64 `json:"pmt_amount"`
		Status         string  `json:"status"`
		TotalAmount    float64 `json:"total_amount"`
		VatAmount      float64 `json:"vat_amount"`
		ZReportDate    string  `json:"z_report_date"`
		ZReportTime    string  `json:"z_report_time"`
		ZReportPayload string  `json:"z_report_payload"`
		Timestamps
	}

	ZReportTotals struct {
		Count       int64   `json:"count"`
		NetAmount   float64 `json:"net_amount"`
		VatAmount   float64 `json:"vat_amount"`
		PmtAmount   float64 `json:"pmt_amount"`
		TotalAmount float64 `json:"total_amount"`
	}
)
