package entities

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type (
	IncomeTracking struct {
		SequentialIdentifier
		Balance      float64   `db:"balance" json:"balance"`
		TrackingDate time.Time `db:"tracking_date" json:"tracking_date"`
		DeletedAt    null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	IncomeTrackingList struct {
		IncomeTrackings []*IncomeTracking `json:"income_trackings"`
		Pagination      *Pagination       `json:"pagination"`
	}
)
