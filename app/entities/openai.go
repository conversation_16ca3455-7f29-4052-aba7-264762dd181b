package entities

type (
	OpenAIAssistant struct {
		ID           string      `json:"id"`
		Object       string      `json:"object"`
		CreatedAt    int         `json:"created_at"`
		Name         string      `json:"name"`
		Description  interface{} `json:"description"`
		Model        string      `json:"model"`
		Instructions string      `json:"instructions"`
		Tools        []struct {
			Type string `json:"type"`
		} `json:"tools"`
		FileIds  []interface{} `json:"file_ids"`
		Metadata struct {
		} `json:"metadata"`
		Error *interface{} `json:"error,omitempty"`
	}

	OpenAIAssistantList struct {
		Object string `json:"object"`
		Data   []struct {
			ID           string        `json:"id"`
			Object       string        `json:"object"`
			CreatedAt    int           `json:"created_at"`
			Name         string        `json:"name"`
			Description  interface{}   `json:"description"`
			Model        string        `json:"model"`
			Instructions string        `json:"instructions"`
			Tools        []interface{} `json:"tools"`
			FileIds      []interface{} `json:"file_ids"`
			Metadata     struct {
			} `json:"metadata"`
		} `json:"data"`
		FirstID string       `json:"first_id"`
		LastID  string       `json:"last_id"`
		Has<PERSON><PERSON> bool         `json:"has_more"`
		Error   *interface{} `json:"error,omitempty"`
	}

	OpenAIChatResponse struct {
		Choices []struct {
			FinishReason string `json:"finish_reason"`
			Index        int    `json:"index"`
			Message      struct {
				Content string `json:"content"`
				Role    string `json:"role"`
			} `json:"message"`
		} `json:"choices"`
		Created int    `json:"created"`
		ID      string `json:"id"`
		Model   string `json:"model"`
		Object  string `json:"object"`
		Usage   struct {
			CompletionTokens int `json:"completion_tokens"`
			PromptTokens     int `json:"prompt_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
		Error *interface{} `json:"error,omitempty"`
	}

	OpenAIThread struct {
		ID        string `json:"id"`
		Object    string `json:"object"`
		CreatedAt int    `json:"created_at"`
		Metadata  struct {
		} `json:"metadata"`
		Error *interface{} `json:"error,omitempty"`
	}

	OpenAIThreadMessage struct {
		ID        string `json:"id"`
		Object    string `json:"object"`
		CreatedAt int    `json:"created_at"`
		ThreadID  string `json:"thread_id"`
		Role      string `json:"role"`
		Content   []struct {
			Type string `json:"type"`
			Text struct {
				Value       string        `json:"value"`
				Annotations []interface{} `json:"annotations"`
			} `json:"text"`
		} `json:"content"`
		FileIds     []interface{} `json:"file_ids"`
		AssistantID interface{}   `json:"assistant_id"`
		RunID       interface{}   `json:"run_id"`
		Metadata    struct {
		} `json:"metadata"`
		Error *interface{} `json:"error,omitempty"`
	}

	OpenAIThreadMessages struct {
		Object string `json:"object"`
		Data   []struct {
			ID        string `json:"id"`
			Object    string `json:"object"`
			CreatedAt int    `json:"created_at"`
			ThreadID  string `json:"thread_id"`
			Role      string `json:"role"`
			Content   []struct {
				Type string `json:"type"`
				Text struct {
					Value       string        `json:"value"`
					Annotations []interface{} `json:"annotations"`
				} `json:"text"`
			} `json:"content"`
			FileIds     []interface{} `json:"file_ids"`
			AssistantID interface{}   `json:"assistant_id"`
			RunID       interface{}   `json:"run_id"`
			Metadata    struct {
			} `json:"metadata"`
		} `json:"data"`
		FirstID string       `json:"first_id"`
		LastID  string       `json:"last_id"`
		HasMore bool         `json:"has_more"`
		Error   *interface{} `json:"error,omitempty"`
	}

	OpenAIThreadRunResponse struct {
		ID           string      `json:"id"`
		Object       string      `json:"object"`
		CreatedAt    int         `json:"created_at"`
		AssistantID  string      `json:"assistant_id"`
		ThreadID     string      `json:"thread_id"`
		Status       string      `json:"status"`
		StartedAt    int         `json:"started_at"`
		ExpiresAt    interface{} `json:"expires_at"`
		CancelledAt  interface{} `json:"cancelled_at"`
		FailedAt     interface{} `json:"failed_at"`
		CompletedAt  int         `json:"completed_at"`
		LastError    interface{} `json:"last_error"`
		Model        string      `json:"model"`
		Instructions interface{} `json:"instructions"`
		Tools        []struct {
			Type string `json:"type"`
		} `json:"tools"`
		FileIds  []string `json:"file_ids"`
		Metadata struct {
		} `json:"metadata"`
		Error *interface{} `json:"error,omitempty"`
	}
)
