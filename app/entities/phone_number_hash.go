package entities

import "gopkg.in/guregu/null.v3"

type (
	PhoneNumberHash struct {
		SequentialIdentifier
		Hash        string    `json:"hash"`
		PhoneNumber string    `json:"phone_number"`
		Provider    string    `json:"provider"`
		NewBalance  float64   `json:"new_balance"`
		DeletedAt   null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	PhoneNumberHashAPIResponse struct {
		Hash        string  `json:"hash"`
		PhoneNumber string  `json:"phone_number"`
		Provider    string  `json:"provider"`
		NewBalance  float64 `json:"new_balance"`
	}

	PhoneNumberHashUploadAPIResponse struct {
		Count int `json:"count"`
	}

	PhoneNumberHashList struct {
		PhoneNumberHashes []*PhoneNumberHash `json:"phone_number_hashes"`
		Pagination        *Pagination        `json:"pagination"`
	}
)
