package entities

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type (
	Receiving struct {
		SequentialIdentifier
		ItemID         int64         `json:"item_id"`
		Quantity       float64       `json:"quantity"`
		CostPrice      float64       `json:"cost_price"`
		Total          float64       `json:"total"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization,omitempty"`
		ReceivedAt     time.Time     `json:"received_at"`
		CreatedBy      int64         `json:"created_by"`
		SupplierID     int64         `json:"supplier_id"`
		Description    string        `json:"description"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	ReceivingsList struct {
		Receivings []*Receiving `json:"items"`
		Pagination *Pagination  `json:"pagination"`
	}
)
