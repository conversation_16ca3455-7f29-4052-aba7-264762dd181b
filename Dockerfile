# specify the base image to  be used for the application, alpine or ubuntu
FROM golang:1.21-alpine

ARG MPESA_CONSUMER_KEY
ARG MPESA_CONSUMER_SECRET

# create a working directory inside the image
WORKDIR /app

# copy all files
COPY . . 
COPY .env.prod /app/.env

# download Go modules and dependencies
RUN go mod tidy
RUN go get github.com/joho/godotenv
RUN go get github.com/robfig/cron

# compile application
RUN go build -o /compliance-and-risk-management-backend

# Add executable permissions
RUN chmod +x /compliance-and-risk-management-backend

# set entrypoint
ENTRYPOINT [ "/compliance-and-risk-management-backend" ]

EXPOSE 9015