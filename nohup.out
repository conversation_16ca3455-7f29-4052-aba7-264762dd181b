Starting VsdcApplication...

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v2.5.6)

2024-07-17 19:50:09.265  INFO 50781 --- [           main] com.kra.vsdc.VsdcApplication             : Starting VsdcApplication v0.0.1-SNAPSHOT using Java 17.0.8 on Gideons-MacBook-Pro.local with PID 50781 (/Users/<USER>/Desktop/Work/ETIMS/vscu_jar_20230816/etims-vscu-prod-1.0.7.jar started by gideon in /Users/<USER>/go/src/compliance-and-risk-management-backend)
2024-07-17 19:50:09.268  INFO 50781 --- [           main] com.kra.vsdc.VsdcApplication             : No active profile set, falling back to default profiles: default
2024-07-17 19:50:10.253  INFO 50781 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-07-17 19:50:10.358  INFO 50781 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 95 ms. Found 2 JPA repository interfaces.
2024-07-17 19:50:11.297  INFO 50781 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8088 (http)
2024-07-17 19:50:11.312  INFO 50781 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2024-07-17 19:50:11.312  INFO 50781 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.58]
2024-07-17 19:50:11.406  INFO 50781 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2024-07-17 19:50:11.406  INFO 50781 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2044 ms
2024-07-17 19:50:11.504  INFO 50781 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2024-07-17 19:50:11.764  INFO 50781 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2024-07-17 19:50:11.770  INFO 50781 --- [           main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2024-07-17 19:50:12.277  INFO 50781 --- [           main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2024-07-17 19:50:12.348  INFO 50781 --- [           main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.32.Final
2024-07-17 19:50:12.538  INFO 50781 --- [           main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2024-07-17 19:50:12.664  INFO 50781 --- [           main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2024-07-17 19:50:13.515  INFO 50781 --- [           main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2024-07-17 19:50:13.523  INFO 50781 --- [           main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 19:50:13.977  WARN 50781 --- [           main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 19:50:16.830  WARN 50781 --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8088 is already in use
2024-07-17 19:50:16.846  INFO 50781 --- [           main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 19:50:16.846  INFO 50781 --- [           main] .SchemaDropperImpl$DelayedDropActionImpl : HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2024-07-17 19:50:16.861  INFO 50781 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2024-07-17 19:50:16.866  INFO 50781 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2024-07-17 19:50:16.872  INFO 50781 --- [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2024-07-17 19:50:16.894  INFO 50781 --- [           main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-17 19:50:16.925 ERROR 50781 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

Starting VsdcApplication...

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v2.5.6)

2024-07-17 19:50:39.916  INFO 50905 --- [           main] com.kra.vsdc.VsdcApplication             : Starting VsdcApplication v0.0.1-SNAPSHOT using Java 17.0.8 on Gideons-MacBook-Pro.local with PID 50905 (/Users/<USER>/Desktop/Work/ETIMS/vscu_jar_20230816/etims-vscu-prod-1.0.7.jar started by gideon in /Users/<USER>/go/src/compliance-and-risk-management-backend)
2024-07-17 19:50:39.919  INFO 50905 --- [           main] com.kra.vsdc.VsdcApplication             : No active profile set, falling back to default profiles: default
2024-07-17 19:50:40.779  INFO 50905 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-07-17 19:50:40.892  INFO 50905 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 102 ms. Found 2 JPA repository interfaces.
2024-07-17 19:50:41.905  INFO 50905 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8088 (http)
2024-07-17 19:50:41.920  INFO 50905 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2024-07-17 19:50:41.920  INFO 50905 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.58]
2024-07-17 19:50:42.015  INFO 50905 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2024-07-17 19:50:42.015  INFO 50905 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2012 ms
2024-07-17 19:50:42.158  INFO 50905 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2024-07-17 19:50:42.338  INFO 50905 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2024-07-17 19:50:42.343  INFO 50905 --- [           main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
2024-07-17 19:50:42.675  INFO 50905 --- [           main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2024-07-17 19:50:42.728  INFO 50905 --- [           main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.4.32.Final
2024-07-17 19:50:42.875  INFO 50905 --- [           main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2024-07-17 19:50:43.025  INFO 50905 --- [           main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2024-07-17 19:50:43.804  INFO 50905 --- [           main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2024-07-17 19:50:43.811  INFO 50905 --- [           main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 19:50:44.246  WARN 50905 --- [           main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2024-07-17 19:50:46.249  WARN 50905 --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8088 is already in use
2024-07-17 19:50:46.256  INFO 50905 --- [           main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2024-07-17 19:50:46.256  INFO 50905 --- [           main] .SchemaDropperImpl$DelayedDropActionImpl : HHH000477: Starting delayed evictData of schema as part of SessionFactory shut-down'
2024-07-17 19:50:46.263  INFO 50905 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2024-07-17 19:50:46.266  INFO 50905 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2024-07-17 19:50:46.268  INFO 50905 --- [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2024-07-17 19:50:46.281  INFO 50905 --- [           main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-07-17 19:50:46.296 ERROR 50905 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

